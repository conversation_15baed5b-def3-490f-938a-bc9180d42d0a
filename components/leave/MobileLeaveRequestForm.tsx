"use client"

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Calendar, Clock, Wifi, WifiOff, Send, Save } from 'lucide-react';
import { IndependentDateRangePicker, DateRange } from '@/components/ui/independent-date-range-picker';
import { usePWA } from '@/hooks/use-pwa';
import { useLeaveRequests } from '@/hooks/use-leave-requests';
import { useLeaveTypes } from '@/hooks/use-leave-types';
import { format, differenceInDays } from 'date-fns';

interface MobileLeaveRequestFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function MobileLeaveRequestForm({ onSuccess, onCancel }: MobileLeaveRequestFormProps) {
  const { isOnline, storeOfflineRequest } = usePWA();
  const { createLeaveRequest, loading } = useLeaveRequests();
  const { leaveTypes, fetchLeaveTypes } = useLeaveTypes();

  const [formData, setFormData] = useState({
    leaveTypeId: '',
    dateRange: undefined as DateRange | undefined,
    reason: '',
    emergencyContact: '',
    attachments: [] as File[]
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load leave types on mount
  useEffect(() => {
    fetchLeaveTypes();
  }, [fetchLeaveTypes]);

  // Calculate duration
  const calculateDuration = () => {
    if (formData.dateRange?.from && formData.dateRange?.to) {
      return differenceInDays(formData.dateRange.to, formData.dateRange.from) + 1;
    }
    return 0;
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.leaveTypeId) {
      newErrors.leaveTypeId = 'Please select a leave type';
    }

    if (!formData.dateRange?.from) {
      newErrors.dateRange = 'Please select a start date';
    }

    if (!formData.dateRange?.to) {
      newErrors.dateRange = 'Please select an end date';
    }

    if (formData.dateRange?.from && formData.dateRange?.to) {
      if (formData.dateRange.from > formData.dateRange.to) {
        newErrors.dateRange = 'End date must be after start date';
      }

      if (formData.dateRange.from < new Date()) {
        newErrors.dateRange = 'Start date cannot be in the past';
      }
    }

    if (!formData.reason.trim()) {
      newErrors.reason = 'Please provide a reason for leave';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const requestData = {
        leaveTypeId: formData.leaveTypeId,
        startDate: formData.dateRange?.from,
        endDate: formData.dateRange?.to,
        reason: formData.reason,
        emergencyContact: formData.emergencyContact,
        attachments: formData.attachments,
        duration: calculateDuration()
      };

      if (isOnline) {
        // Submit online
        await createLeaveRequest(requestData);
        onSuccess?.();
      } else {
        // Store offline
        await storeOfflineRequest(requestData);
        onSuccess?.();
      }
    } catch (error) {
      console.error('Error submitting leave request:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle input changes
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setFormData(prev => ({ ...prev, attachments: files }));
  };

  const duration = calculateDuration();
  const selectedLeaveType = leaveTypes.find(type => type._id === formData.leaveTypeId);

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h1 className="text-2xl font-bold text-gray-900">Leave Request</h1>
          <div className="flex items-center gap-2">
            {isOnline ? (
              <Badge variant="outline" className="text-green-600 border-green-600">
                <Wifi className="w-3 h-3 mr-1" />
                Online
              </Badge>
            ) : (
              <Badge variant="outline" className="text-orange-600 border-orange-600">
                <WifiOff className="w-3 h-3 mr-1" />
                Offline
              </Badge>
            )}
          </div>
        </div>
        
        {!isOnline && (
          <Alert className="mb-4">
            <WifiOff className="h-4 w-4" />
            <AlertDescription>
              You're offline. Your request will be saved and submitted when you're back online.
            </AlertDescription>
          </Alert>
        )}
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Leave Type Selection */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Leave Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="leaveType">Leave Type *</Label>
              <Select 
                value={formData.leaveTypeId} 
                onValueChange={(value) => handleInputChange('leaveTypeId', value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select leave type" />
                </SelectTrigger>
                <SelectContent>
                  {leaveTypes.map((type) => (
                    <SelectItem key={type._id} value={type._id}>
                      <div className="flex items-center gap-2">
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: type.color }}
                        />
                        {type.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.leaveTypeId && (
                <p className="text-sm text-red-600 mt-1">{errors.leaveTypeId}</p>
              )}
            </div>

            {selectedLeaveType && (
              <div className="p-3 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>Balance:</strong> {selectedLeaveType.balance || 0} days available
                </p>
                {selectedLeaveType.description && (
                  <p className="text-sm text-blue-700 mt-1">
                    {selectedLeaveType.description}
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Date Selection */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Leave Dates
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Select Dates *</Label>
              <div className="mt-2">
                <IndependentDateRangePicker
                  value={formData.dateRange}
                  onChange={(value) => setFormData(prev => ({ ...prev, dateRange: value }))}
                  placeholder="Select your leave dates"
                  minDate={new Date()} // Prevent selecting past dates
                  labels={{
                    startDate: "Start Date",
                    endDate: "End Date",
                    clear: "Clear Dates"
                  }}
                  clearable={true}
                  allowSingleDate={false} // Require both start and end dates
                  className="w-full"
                />
              </div>
              {errors.dateRange && (
                <p className="text-sm text-red-600 mt-1">{errors.dateRange}</p>
              )}
            </div>

            {duration > 0 && (
              <div className="p-3 bg-green-50 rounded-lg">
                <div className="flex items-center gap-2 text-green-800">
                  <Clock className="w-4 h-4" />
                  <span className="font-medium">Duration: {duration} day{duration !== 1 ? 's' : ''}</span>
                </div>
                {formData.dateRange?.from && formData.dateRange?.to && (
                  <div className="text-sm text-green-700 mt-1">
                    {format(formData.dateRange.from, 'MMM dd, yyyy')} - {format(formData.dateRange.to, 'MMM dd, yyyy')}
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Reason and Details */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Additional Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="reason">Reason for Leave *</Label>
              <Textarea
                id="reason"
                placeholder="Please provide a reason for your leave request..."
                value={formData.reason}
                onChange={(e) => handleInputChange('reason', e.target.value)}
                className="mt-1 min-h-[100px]"
                maxLength={500}
              />
              <div className="flex justify-between items-center mt-1">
                {errors.reason && (
                  <p className="text-sm text-red-600">{errors.reason}</p>
                )}
                <p className="text-sm text-gray-500 ml-auto">
                  {formData.reason.length}/500
                </p>
              </div>
            </div>

            <div>
              <Label htmlFor="emergencyContact">Emergency Contact</Label>
              <Input
                id="emergencyContact"
                placeholder="Phone number or contact person"
                value={formData.emergencyContact}
                onChange={(e) => handleInputChange('emergencyContact', e.target.value)}
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="attachments">Attachments (Optional)</Label>
              <Input
                id="attachments"
                type="file"
                multiple
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                onChange={handleFileUpload}
                className="mt-1"
              />
              {formData.attachments.length > 0 && (
                <div className="mt-2">
                  <p className="text-sm text-gray-600">
                    {formData.attachments.length} file(s) selected
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            className="flex-1"
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          
          <Button
            type="submit"
            className="flex-1"
            disabled={isSubmitting || loading}
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                {isOnline ? 'Submitting...' : 'Saving...'}
              </div>
            ) : (
              <div className="flex items-center gap-2">
                {isOnline ? <Send className="w-4 h-4" /> : <Save className="w-4 h-4" />}
                {isOnline ? 'Submit Request' : 'Save Offline'}
              </div>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
