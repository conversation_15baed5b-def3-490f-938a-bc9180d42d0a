// components/dashboard-sidebar.tsx
"use client"

import { useEffect, useRef } from "react"
import type { HTMLAttributes } from "react"

import { cn } from "../lib/utils"
import {
  BarChart3,
  Calendar,
  CreditCard,
  FileText,
  LayoutDashboard,
  Settings,
  Users,
  Building2,
  BellRing,
  Briefcase,
  LifeBuoy,
  LogOut,
  // CheckSquare, // Commented out - used in Tasks module
  // Package, // Commented out - used in Inventory module
  DollarSign,
  UserCircle,
  LineChart,
  MessageSquare,
  Target,
  Receipt,
  BookOpen,
  Shield,
  MonitorSmartphone,
  ShieldAlert,
  Bell,
  Layers,
  UserCog,
  Lock,
  Cog,
  Ban,
  PieChart,
  TrendingUp,
  TrendingDown,
  Landmark,
  ClipboardCheck,
  GitBranch,
  Clock,
  ListTodo,
  FileBarChart,
  // Laptop, // Commented out - used in Inventory module
  // ShoppingCart, // Commented out - used in Inventory module
  // Warehouse, // Commented out - used in Inventory module
  UserPlus,
  PlusCircle,
  ClipboardList,
  ListChecks,
  CheckCircle,
  XCircle,
  Phone,
  Mail,
  MessageCircle,
  AtSign,
  History,
  Archive,
  AlertCircle,
  Percent,
  ArrowRightLeft,
  Download,
  Trash2,
  Building,
  Zap,
  Gift,
  ShoppingCart,
  Package,
  Truck,
  FileCheck,
  UserCheck,
  ClipboardSignature,
  Handshake,
  Scale,
  AlertTriangle,
  FileSearch,
  RotateCcw,
  Database,
  Bug
} from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Button } from "./ui/button"
import { ScrollArea } from "./ui/scroll-area"
import { Separator } from "./ui/separator"
import { MobileThemeSwitcher } from "./theme-switcher"
import { useAuth } from "../lib/frontend/hooks/useAuth"
import { UserRole } from "../types/user-roles"
import { useNavigationStore } from "../lib/frontend/navigationStore"

// Define types for navigation items
type NavigationSubItem = {
  href: string
  title: string
  icon?: React.ComponentType<{ className?: string }>
  roles: UserRole[]
}

type NavigationItem = {
  href: string
  title: string
  icon: React.ComponentType<{ className?: string }>
  badge?: number
  roles: UserRole[]
  subItems?: NavigationSubItem[]
}

interface SidebarNavProps extends HTMLAttributes<HTMLElement> {
  items: NavigationItem[]
}

export function SidebarNav({ className, items, onNavigate, ...props }: SidebarNavProps & { onNavigate?: (title: string) => void }) {
  const pathname = usePathname() || ''
  const { user } = useAuth()
  const userRole = user?.role || UserRole.EMPLOYEE
  const startNavigation = useNavigationStore(state => state.startNavigation)

  // Create refs for active items
  const activeItemRef = useRef<HTMLDivElement>(null)
  const activeSubItemRef = useRef<HTMLAnchorElement>(null)

  // Check if the current path is a sub-item of a main item
  const isSubItemActive = (item: NavigationItem) => {
    if (!item.subItems) return false
    return item.subItems.some((subItem: NavigationSubItem) =>
      pathname === subItem.href || pathname.startsWith(`${subItem.href}/`)
    )
  }

  // Function to handle navigation
  const handleNavigation = (title: string) => {
    startNavigation(title)
    if (onNavigate) {
      onNavigate(title)
    }
  }

  // Function to scroll to active item
  const scrollToActiveItem = () => {
    // Give the DOM time to update before scrolling
    const timer = setTimeout(() => {
      // First try to scroll to active sub-item if it exists
      if (activeSubItemRef.current) {
        activeSubItemRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest'
        })
      }
      // Otherwise scroll to active main item
      else if (activeItemRef.current) {
        activeItemRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest'
        })
      }
    }, 100)

    return timer
  }

  // Scroll to active item when component mounts and when pathname changes
  useEffect(() => {
    const timer = scrollToActiveItem()
    return () => clearTimeout(timer)
  }, [pathname])

  return (
    <nav className={cn("flex flex-col gap-1", className)} {...props}>
      {items.map((item, index) => {
        // Filter sub-items based on user role
        const filteredSubItems = item.subItems?.filter(subItem =>
          subItem.roles.includes(userRole)
        ) || []

        const hasSubItems = filteredSubItems.length > 0
        const isActive = pathname === item.href || pathname.startsWith(`${item.href}/`)
        const isItemWithActiveSubItem = isSubItemActive(item)

        return (
          <div key={item.href} className="flex flex-col mb-2">
            {/* Main item */}
            <div
              ref={isActive ? activeItemRef : null}
              className={cn(
                "group flex items-center justify-between rounded-md px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground",
                (isActive || isItemWithActiveSubItem) ? "bg-primary/10 text-primary" : "text-muted-foreground hover:text-foreground",
              )}
            >
              <Link
                href={item.href}
                className="flex items-center gap-3 flex-1"
                onClick={() => handleNavigation(item.title)}
              >
                <item.icon
                  className={cn(
                    "h-4 w-4",
                    (isActive || isItemWithActiveSubItem) ? "text-primary" : "text-muted-foreground group-hover:text-foreground",
                  )}
                />
                {item.title}
              </Link>
              <div className="flex items-center">
                {item.badge && (
                  <span className="flex h-5 min-w-5 items-center justify-center rounded-full bg-primary px-1.5 text-xs font-medium text-primary-foreground">
                    {item.badge}
                  </span>
                )}
              </div>
            </div>

            {/* Sub-items - always visible */}
            {hasSubItems && (
              <div className="ml-6 mt-2 mb-1 flex flex-col gap-1 border-l pl-2 border-border/50">
                {filteredSubItems.map((subItem) => {
                  const isSubItemActive = pathname === subItem.href || pathname.startsWith(`${subItem.href}/`);
                  const SubItemIcon = subItem.icon;

                  return (
                    <Link
                      key={subItem.href}
                      href={subItem.href}
                      ref={isSubItemActive ? activeSubItemRef : null}
                      onClick={() => handleNavigation(subItem.title)}
                      className={cn(
                        "flex items-center gap-2.5 rounded-md px-3 py-1.5 text-xs font-medium transition-colors hover:bg-accent hover:text-accent-foreground",
                        isSubItemActive
                          ? "text-primary font-medium"
                          : "text-muted-foreground hover:text-foreground"
                      )}
                    >
                      {SubItemIcon && (
                        <SubItemIcon
                          className={cn(
                            "h-3.5 w-3.5",
                            isSubItemActive ? "text-primary" : "text-muted-foreground"
                          )}
                        />
                      )}
                      <span>{subItem.title}</span>
                    </Link>
                  );
                })}
              </div>
            )}

            {/* Add separator after each item except the last one */}
            {index < items.length - 1 && (
              <Separator className="mt-2 bg-border/40" />
            )}
          </div>
        )
      })}
    </nav>
  )
}

export function DashboardSidebar({ onNavigate }: { onNavigate?: (title: string) => void }) {
  const { user, logout } = useAuth();
  const userRole = user?.role || UserRole.EMPLOYEE; // Default to employee if no role is found

  // NOTE: Some modules are commented out pending full implementation
  // Modules currently hidden: Training, Tasks, Attendance, Inventory (Procurement)

  // Define navigation items with their associated roles
  const allMainItems = [
    {
      title: "Dashboard",
      href: "/dashboard",
      icon: LayoutDashboard,
      roles: [
        UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST, UserRole.RECRUITER, UserRole.TRAINING_COORDINATOR, UserRole.DEPARTMENT_HEAD,
        UserRole.TEAM_LEADER, UserRole.EMPLOYEE, UserRole.CONTRACTOR, UserRole.INTERN, UserRole.FINANCE_DIRECTOR,
        UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT, UserRole.PAYROLL_SPECIALIST, UserRole.SALES_DIRECTOR,
        UserRole.SALES_MANAGER, UserRole.ACCOUNT_MANAGER, UserRole.SALES_REPRESENTATIVE, UserRole.CUSTOMER_SERVICE_MANAGER,
        UserRole.CUSTOMER_SERVICE_REPRESENTATIVE, UserRole.MARKETING_MANAGER, UserRole.MARKETING_SPECIALIST
      ] // All roles can access dashboard
    },
    {
      title: "Employees",
      href: "/dashboard/employees",
      icon: Users,
      badge: 2,
      roles: [
        UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
      ],
      subItems: [
        {
          title: "Dashboard",
          href: "/dashboard/employees",
          icon: LayoutDashboard,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
          ]
        },
        {
          title: "Directory",
          href: "/dashboard/employees/directory",
          icon: Users,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
          ]
        },
        {
          title: "Registration",
          href: "/dashboard/employees/register",
          icon: UserPlus,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST
          ]
        },
        {
          title: "Departments",
          href: "/dashboard/departments",
          icon: Building2,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD
          ]
        },
        {
          title: "Roles",
          href: "/dashboard/employee/roles",
          icon: Briefcase,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST
          ]
        },
        {
          title: "Reports",
          href: "/dashboard/employees/reports",
          icon: BarChart3,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD
          ]
        },
        {
          title: "Export Data",
          href: "/dashboard/hr/employees/export",
          icon: Download,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD
          ]
        },
        {
          title: "Bulk Delete",
          href: "/dashboard/hr/employees/bulk-delete",
          icon: Trash2,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER
          ]
        }
      ]
    },
    {
      title: "Payroll",
      href: "/dashboard/payroll",
      icon: CreditCard,
      roles: [
        UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER,
        UserRole.PAYROLL_SPECIALIST, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER
      ],
      subItems: [
        {
          href: "/dashboard/payroll",
          title: "Dashboard",
          icon: LayoutDashboard,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PAYROLL_SPECIALIST, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER]
        },
        {
          href: "/dashboard/payroll/run",
          title: "Run Payroll",
          icon: DollarSign,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PAYROLL_SPECIALIST]
        },
        {
          href: "/dashboard/payroll/previous-runs",
          title: "Previous Payroll Runs",
          icon: Calendar,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PAYROLL_SPECIALIST]
        },
        {
          href: "/dashboard/payroll/employee-salaries",
          title: "Employee Salaries",
          icon: Users,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PAYROLL_SPECIALIST, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER]
        },
        {
          href: "/dashboard/payroll/salary-structure",
          title: "Salary Structures",
          icon: DollarSign,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PAYROLL_SPECIALIST, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER]
        },
        {
          href: "/dashboard/payroll/salary-bands",
          title: "Salary Bands",
          icon: Building,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PAYROLL_SPECIALIST, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER]
        },
        {
          href: "/dashboard/payroll/taxes",
          title: "Tax Management",
          icon: Percent,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PAYROLL_SPECIALIST, UserRole.HR_DIRECTOR]
        },
        {
          href: "/dashboard/payroll/allowances",
          title: "Allowances",
          icon: PlusCircle,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PAYROLL_SPECIALIST]
        },
        {
          href: "/dashboard/payroll/deductions",
          title: "Deductions",
          icon: Percent,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PAYROLL_SPECIALIST]
        },
        {
          href: "/dashboard/payroll/payslips",
          title: "Payslips",
          icon: FileText,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PAYROLL_SPECIALIST]
        },
        {
          href: "/dashboard/payroll/reports",
          title: "Payroll Reports",
          icon: BarChart3,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PAYROLL_SPECIALIST]
        },
        {
          href: "/dashboard/payroll/accounting",
          title: "Accounting Integration",
          icon: ArrowRightLeft,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PAYROLL_SPECIALIST]
        },
        {
          href: "/dashboard/payroll/export",
          title: "Export Data",
          icon: Download,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PAYROLL_SPECIALIST, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER]
        },
        {
          href: "/dashboard/payroll/bulk-operations",
          title: "Bulk Operations",
          icon: Zap,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PAYROLL_SPECIALIST]
        },
        {
          href: "/dashboard/payroll/salary-revisions",
          title: "Salary Revisions",
          icon: TrendingUp,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR,
                 UserRole.FINANCE_DIRECTOR, UserRole.PAYROLL_MANAGER]
        },
        {
          href: "/dashboard/payroll/compensation",
          title: "Compensation",
          icon: Gift,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR,
                 UserRole.FINANCE_DIRECTOR, UserRole.PAYROLL_MANAGER, UserRole.FINANCE_MANAGER]
        }
      ]
    },
    {
      title: "Accounting",
      href: "/dashboard/accounting",
      icon: DollarSign,
      roles: [
        UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
        UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT, UserRole.PAYROLL_SPECIALIST
      ],
      subItems: [
        {
          href: "/dashboard/accounting/dashboard",
          title: "Financial Dashboard",
          icon: BarChart3,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT, UserRole.PAYROLL_SPECIALIST]
        },
        {
          href: "/dashboard/accounting/budget/planning",
          title: "Budget Planning",
          icon: PieChart,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT]
        },
        {
          href: "/dashboard/accounting/income/overview",
          title: "Income Management",
          icon: TrendingUp,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT]
        },
        {
          href: "/dashboard/accounting/income/drafts",
          title: "Income Drafts & Approvals",
          icon: FileCheck,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT, UserRole.HR_MANAGER]
        },
        {
          href: "/dashboard/accounting/expenditures",
          title: "Expenditure",
          icon: TrendingDown,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT]
        },
        {
          href: "/dashboard/accounting/expenditure/drafts",
          title: "Expenditure Drafts & Approvals",
          icon: FileCheck,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT, UserRole.HR_MANAGER]
        },
        {
          href: "/dashboard/accounting/assets/register",
          title: "Asset Management",
          icon: Building2,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT]
        },
        {
          href: "/dashboard/accounting/payroll/processing",
          title: "Payroll & Benefits",
          icon: CreditCard,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT, UserRole.PAYROLL_SPECIALIST]
        },
        {
          href: "/dashboard/accounting/payments",
          title: "Payment Management",
          icon: CreditCard,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT]
        },
        {
          href: "/dashboard/accounting/vouchers/payment",
          title: "Vouchers",
          icon: Receipt,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT]
        },
        {
          href: "/dashboard/accounting/ledger/chart-of-accounts",
          title: "Ledger",
          icon: BookOpen,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT]
        },
        {
          href: "/dashboard/accounting/banking",
          title: "Banking & Treasury",
          icon: Landmark,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT]
        },
        {
          href: "/dashboard/accounting/security",
          title: "Security Management",
          icon: Shield,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER]
        },
        {
          href: "/dashboard/accounting/integrations",
          title: "Integrations",
          icon: ArrowRightLeft,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT]
        },
        {
          href: "/dashboard/accounting/reports",
          title: "Financial Reports",
          icon: FileText,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT]
        }
      ]
    },
    {
      title: "Procurement",
      href: "/dashboard/procurement",
      icon: ShoppingCart,
      roles: [
        UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
        UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT, UserRole.PROCUREMENT_MANAGER,
        UserRole.PROCUREMENT_OFFICER
      ],
      subItems: [
        {
          href: "/dashboard/procurement/dashboard",
          title: "Procurement Dashboard",
          icon: LayoutDashboard,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT, UserRole.PROCUREMENT_MANAGER,
                 UserRole.PROCUREMENT_OFFICER]
        },
        {
          href: "/dashboard/procurement/requisitions",
          title: "Purchase Requisitions",
          icon: ClipboardList,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PROCUREMENT_MANAGER, UserRole.PROCUREMENT_OFFICER]
        },
        {
          href: "/dashboard/procurement/purchase-orders",
          title: "Purchase Orders",
          icon: FileCheck,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PROCUREMENT_MANAGER, UserRole.PROCUREMENT_OFFICER]
        },
        {
          href: "/dashboard/procurement/suppliers",
          title: "Supplier Management",
          icon: UserCheck,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PROCUREMENT_MANAGER, UserRole.PROCUREMENT_OFFICER]
        },
        {
          href: "/dashboard/procurement/contracts",
          title: "Contract Management",
          icon: ClipboardSignature,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PROCUREMENT_MANAGER]
        },
        {
          href: "/dashboard/procurement/tenders",
          title: "Tender Management",
          icon: Handshake,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PROCUREMENT_MANAGER]
        },
        {
          href: "/dashboard/procurement/inventory",
          title: "Inventory Management",
          icon: Package,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PROCUREMENT_MANAGER, UserRole.PROCUREMENT_OFFICER]
        },
        {
          href: "/dashboard/procurement/deliveries",
          title: "Delivery Tracking",
          icon: Truck,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PROCUREMENT_MANAGER, UserRole.PROCUREMENT_OFFICER]
        },
        {
          href: "/dashboard/procurement/categories",
          title: "Category Management",
          icon: Layers,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PROCUREMENT_MANAGER]
        },
        {
          href: "/dashboard/procurement/compliance",
          title: "Compliance & Audit",
          icon: Scale,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PROCUREMENT_MANAGER]
        },
        {
          href: "/dashboard/procurement/reports",
          title: "Procurement Reports",
          icon: BarChart3,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.FINANCE_MANAGER, UserRole.PROCUREMENT_MANAGER]
        },
        {
          href: "/dashboard/procurement/settings",
          title: "Procurement Settings",
          icon: Settings,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR,
                 UserRole.PROCUREMENT_MANAGER]
        }
      ]
    },
    // {
    //   title: "Attendance",
    //   href: "/dashboard/attendance",
    //   icon: Calendar,
    //   roles: [
    //     UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //     UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
    //   ],
    //   subItems: [
    //     {
    //       title: "Dashboard",
    //       href: "/dashboard/attendance",
    //       icon: LayoutDashboard,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //         UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
    //       ]
    //     },
    //     {
    //       title: "Daily Tracking",
    //       href: "/dashboard/attendance/daily",
    //       icon: Clock,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //         UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
    //       ]
    //     },
    //     {
    //       title: "Shift Management",
    //       href: "/dashboard/attendance/shifts",
    //       icon: Calendar,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //         UserRole.HR_SPECIALIST
    //       ]
    //     },
    //     {
    //       title: "Overtime",
    //       href: "/dashboard/attendance/overtime",
    //       icon: Clock,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //         UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD
    //       ]
    //     },
    //     {
    //       title: "Reports",
    //       href: "/dashboard/attendance/reports",
    //       icon: FileText,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //         UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD
    //       ]
    //     },
    //     {
    //       title: "Policies",
    //       href: "/dashboard/attendance/policies",
    //       icon: ClipboardCheck,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //         UserRole.HR_SPECIALIST
    //       ]
    //     }
    //   ]
    // },
    // {
    //   title: "Tasks",
    //   href: "/dashboard/tasks",
    //   icon: CheckSquare,
    //   badge: 5,
    //   roles: [
    //     UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //     UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER, UserRole.EMPLOYEE,
    //     UserRole.CONTRACTOR, UserRole.INTERN
    //   ],
    //   subItems: [
    //     {
    //       title: "Dashboard",
    //       href: "/dashboard/tasks",
    //       icon: LayoutDashboard,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //         UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER, UserRole.EMPLOYEE,
    //         UserRole.CONTRACTOR, UserRole.INTERN
    //       ]
    //     },
    //     {
    //       title: "My Tasks",
    //       href: "/dashboard/tasks/my-tasks",
    //       icon: CheckSquare,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //         UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER, UserRole.EMPLOYEE,
    //         UserRole.CONTRACTOR, UserRole.INTERN
    //       ]
    //     },
    //     {
    //       title: "Task Management",
    //       href: "/dashboard/tasks/management",
    //       icon: ListTodo,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //         UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
    //       ]
    //     },
    //     {
    //       title: "Task Tracking",
    //       href: "/dashboard/tasks/tracking",
    //       icon: Clock,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //         UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER, UserRole.EMPLOYEE
    //       ]
    //     },
    //     {
    //       title: "Collaboration",
    //       href: "/dashboard/tasks/collaboration",
    //       icon: Users,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //         UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER, UserRole.EMPLOYEE,
    //         UserRole.CONTRACTOR, UserRole.INTERN
    //       ]
    //     },
    //     {
    //       title: "Reports",
    //       href: "/dashboard/tasks/reports",
    //       icon: FileBarChart,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //         UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
    //       ]
    //     },
    //     {
    //       title: "Templates",
    //       href: "/dashboard/tasks/templates",
    //       icon: ClipboardCheck,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //         UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
    //       ]
    //     }
    //   ]
    // },
    // {
    //   title: "Inventory",
    //   href: "/dashboard/inventory",
    //   icon: Package,
    //   badge: 8,
    //   roles: [
    //     UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER
    //   ],
    //   subItems: [
    //     {
    //       title: "Dashboard",
    //       href: "/dashboard/inventory",
    //       icon: LayoutDashboard,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER
    //       ]
    //     },
    //     {
    //       title: "Stock Management",
    //       href: "/dashboard/inventory/stock",
    //       icon: Package,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER
    //       ]
    //     },
    //     {
    //       title: "Equipment",
    //       href: "/dashboard/inventory/equipment",
    //       icon: Laptop,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER
    //       ]
    //     },
    //     {
    //       title: "Assets",
    //       href: "/dashboard/inventory/assets",
    //       icon: Building2,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER
    //       ]
    //     },
    //     {
    //       title: "Suppliers",
    //       href: "/dashboard/inventory/suppliers",
    //       icon: Users,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER
    //       ]
    //     },
    //     {
    //       title: "Purchase Orders",
    //       href: "/dashboard/inventory/purchase-orders",
    //       icon: ShoppingCart,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER
    //       ]
    //     },
    //     {
    //       title: "Warehouse",
    //       href: "/dashboard/inventory/warehouse",
    //       icon: Warehouse,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER
    //       ]
    //     },
    //     {
    //       title: "Reports",
    //       href: "/dashboard/inventory/reports",
    //       icon: BarChart3,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER
    //       ]
    //     }
    //   ]
    // },
    {
      title: "Invoices",
      href: "/dashboard/invoices",
      icon: Receipt,
      badge: 4,
      roles: [
        UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER,
        UserRole.ACCOUNTANT
      ],
      subItems: [
        {
          title: "Dashboard",
          href: "/dashboard/invoices",
          icon: LayoutDashboard,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER,
            UserRole.ACCOUNTANT
          ]
        },
        {
          title: "All Invoices",
          href: "/dashboard/invoices/all",
          icon: FileText,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER,
            UserRole.ACCOUNTANT
          ]
        },
        {
          title: "Create Invoice",
          href: "/dashboard/invoices/create",
          icon: PlusCircle,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER,
            UserRole.ACCOUNTANT
          ]
        },
        {
          title: "Payments",
          href: "/dashboard/invoices/payments",
          icon: CreditCard,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER,
            UserRole.ACCOUNTANT
          ]
        },
        {
          title: "Reports",
          href: "/dashboard/invoices/reports",
          icon: BarChart3,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER,
            UserRole.ACCOUNTANT
          ]
        },
        {
          title: "Settings",
          href: "/dashboard/invoices/settings",
          icon: Settings,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER
          ]
        }
      ]
    },
    // {
    //   title: "Departments",
    //   href: "/dashboard/departments",
    //   icon: Building2,
    //   roles: [
    //     UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //     UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD
    //   ]
    // },
    {
      title: "Recruitment",
      href: "/dashboard/recruitment",
      icon: Briefcase,
      roles: [
        UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST, UserRole.RECRUITER
      ],
      subItems: [
        {
          title: "Dashboard",
          href: "/dashboard/recruitment",
          icon: LayoutDashboard,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.RECRUITER
          ]
        },
        {
          title: "Jobs",
          href: "/dashboard/recruitment/jobs",
          icon: Briefcase,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.RECRUITER
          ]
        },
        {
          title: "Candidates",
          href: "/dashboard/recruitment/candidates",
          icon: Users,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.RECRUITER
          ]
        },
        {
          title: "Applications",
          href: "/dashboard/recruitment/applications",
          icon: ClipboardCheck,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.RECRUITER
          ]
        },
        {
          title: "Interviews",
          href: "/dashboard/recruitment/interviews",
          icon: Calendar,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.RECRUITER
          ]
        },
        {
          title: "Offers",
          href: "/dashboard/recruitment/offers",
          icon: FileText,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.RECRUITER
          ]
        },
        {
          title: "Pipeline",
          href: "/dashboard/recruitment/pipeline",
          icon: GitBranch,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.RECRUITER
          ]
        },
        {
          title: "Reports",
          href: "/dashboard/recruitment/reports",
          icon: BarChart3,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.RECRUITER
          ]
        }
      ]
    },




  ]

  const allManagementItems = [
    {
      title: "Leave Management",
      href: "/dashboard/leave",
      icon: FileText,
      badge: 5,
      roles: [
        UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
      ],
      subItems: [
        {
          title: "Dashboard",
          href: "/dashboard/leave",
          icon: LayoutDashboard,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
          ]
        },
        {
          title: "Requests",
          href: "/dashboard/leave/requests",
          icon: FileText,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
          ]
        },
        {
          title: "Calendar",
          href: "/dashboard/leave/calendar",
          icon: Calendar,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
          ]
        },
        {
          title: "Balances",
          href: "/dashboard/leave/balances",
          icon: BarChart3,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
          ]
        },
        {
          title: "Types",
          href: "/dashboard/leave/types",
          icon: Layers,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST
          ]
        },
        {
          title: "Reports",
          href: "/dashboard/leave/reports",
          icon: FileBarChart,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST
          ]
        },
        {
          title: "Settings",
          href: "/dashboard/leave/settings",
          icon: Settings,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER
          ]
        }
      ]
    },
    {
      title: "Performance",
      href: "/dashboard/performance",
      icon: BarChart3,
      roles: [
        UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
        UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
      ],
      subItems: [
        {
          title: "Dashboard",
          href: "/dashboard/performance",
          icon: LayoutDashboard,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
          ]
        },
        {
          title: "Reviews",
          href: "/dashboard/performance/reviews",
          icon: ClipboardCheck,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
          ]
        },
        {
          title: "Goals",
          href: "/dashboard/performance/goals",
          icon: Target,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
          ]
        },
        {
          title: "Feedback",
          href: "/dashboard/performance/feedback",
          icon: MessageSquare,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
          ]
        },
        {
          title: "Reports",
          href: "/dashboard/performance/reports",
          icon: FileBarChart,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER
          ]
        },
        {
          title: "Settings",
          href: "/dashboard/performance/settings",
          icon: Settings,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER
          ]
        }
      ]
    },
    {
      title: "Onboarding",
      href: "/dashboard/onboarding",
      icon: ClipboardCheck,
      roles: [
        UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST, UserRole.RECRUITER, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
      ],
      subItems: [
        {
          title: "Dashboard",
          href: "/dashboard/onboarding",
          icon: LayoutDashboard,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.RECRUITER, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
          ]
        },
        {
          title: "Processes",
          href: "/dashboard/onboarding/processes",
          icon: ClipboardCheck,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.RECRUITER, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
          ]
        },
        {
          title: "Tasks",
          href: "/dashboard/onboarding/tasks",
          icon: ListTodo,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.RECRUITER, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
          ]
        },
        {
          title: "Templates",
          href: "/dashboard/onboarding/templates",
          icon: ClipboardCheck,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST
          ]
        },
        {
          title: "Documents",
          href: "/dashboard/onboarding/documents",
          icon: FileText,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.RECRUITER
          ]
        },
        {
          title: "Analytics",
          href: "/dashboard/onboarding/analytics",
          icon: BarChart3,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER
          ]
        }
      ]
    },
    // {
    //   title: "Training",
    //   href: "/dashboard/training",
    //   icon: GraduationCap,
    //   roles: [
    //     UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //     UserRole.TRAINING_COORDINATOR, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
    //   ],
    //   subItems: [
    //     {
    //       title: "Dashboard",
    //       href: "/dashboard/training",
    //       icon: LayoutDashboard,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //         UserRole.TRAINING_COORDINATOR, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
    //       ]
    //     },
    //     {
    //       title: "Courses",
    //       href: "/dashboard/training/courses",
    //       icon: BookOpen,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //         UserRole.TRAINING_COORDINATOR, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
    //       ]
    //     },
    //     {
    //       title: "Sessions",
    //       href: "/dashboard/training/sessions",
    //       icon: Calendar,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //         UserRole.TRAINING_COORDINATOR, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
    //       ]
    //     },
    //     {
    //       title: "Certifications",
    //       href: "/dashboard/training/certifications",
    //       icon: FileText,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //         UserRole.TRAINING_COORDINATOR, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
    //       ]
    //     },
    //     {
    //       title: "Employee Training",
    //       href: "/dashboard/training/employees",
    //       icon: Users,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //         UserRole.TRAINING_COORDINATOR, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
    //       ]
    //     },
    //     {
    //       title: "Reports",
    //       href: "/dashboard/training/reports",
    //       icon: FileBarChart,
    //       roles: [
    //         UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
    //         UserRole.TRAINING_COORDINATOR
    //       ]
    //     }
    //   ]
    // },

  ]

  // CRM items
  const allCrmItems = [
    {
      title: "Customers",
      href: "/dashboard/crm/customers",
      icon: UserCircle,
      badge: 3,
      roles: [
        UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.SALES_DIRECTOR, UserRole.SALES_MANAGER,
        UserRole.ACCOUNT_MANAGER, UserRole.SALES_REPRESENTATIVE, UserRole.CUSTOMER_SERVICE_MANAGER,
        UserRole.CUSTOMER_SERVICE_REPRESENTATIVE
      ],
      subItems: [
        {
          title: "Dashboard",
          href: "/dashboard/crm/customers",
          icon: LayoutDashboard,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.SALES_DIRECTOR, UserRole.SALES_MANAGER,
            UserRole.ACCOUNT_MANAGER, UserRole.SALES_REPRESENTATIVE, UserRole.CUSTOMER_SERVICE_MANAGER,
            UserRole.CUSTOMER_SERVICE_REPRESENTATIVE
          ]
        },
        {
          title: "Directory",
          href: "/dashboard/crm/customers/directory",
          icon: Users,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.SALES_DIRECTOR, UserRole.SALES_MANAGER,
            UserRole.ACCOUNT_MANAGER, UserRole.SALES_REPRESENTATIVE, UserRole.CUSTOMER_SERVICE_MANAGER,
            UserRole.CUSTOMER_SERVICE_REPRESENTATIVE
          ]
        },
        {
          title: "Active Customers",
          href: "/dashboard/crm/customers/active",
          icon: CheckCircle,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.SALES_DIRECTOR, UserRole.SALES_MANAGER,
            UserRole.ACCOUNT_MANAGER, UserRole.SALES_REPRESENTATIVE, UserRole.CUSTOMER_SERVICE_MANAGER,
            UserRole.CUSTOMER_SERVICE_REPRESENTATIVE
          ]
        },
        {
          title: "Leads",
          href: "/dashboard/crm/customers/leads",
          icon: Target,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.SALES_DIRECTOR, UserRole.SALES_MANAGER,
            UserRole.ACCOUNT_MANAGER, UserRole.SALES_REPRESENTATIVE
          ]
        },
        {
          title: "Contacts",
          href: "/dashboard/crm/customers/contacts",
          icon: Phone,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.SALES_DIRECTOR, UserRole.SALES_MANAGER,
            UserRole.ACCOUNT_MANAGER, UserRole.SALES_REPRESENTATIVE, UserRole.CUSTOMER_SERVICE_MANAGER,
            UserRole.CUSTOMER_SERVICE_REPRESENTATIVE
          ]
        },
        {
          title: "Reports",
          href: "/dashboard/crm/customers/reports",
          icon: FileBarChart,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.SALES_DIRECTOR, UserRole.SALES_MANAGER
          ]
        }
      ]
    },
    {
      title: "Sales Analytics",
      href: "/dashboard/crm/analytics",
      icon: LineChart,
      roles: [
        UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.SALES_DIRECTOR, UserRole.SALES_MANAGER,
        UserRole.MARKETING_MANAGER
      ],
      subItems: [
        {
          title: "Dashboard",
          href: "/dashboard/crm/analytics",
          icon: LayoutDashboard,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.SALES_DIRECTOR, UserRole.SALES_MANAGER,
            UserRole.MARKETING_MANAGER
          ]
        },
        {
          title: "Sales Performance",
          href: "/dashboard/crm/analytics/performance",
          icon: BarChart3,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.SALES_DIRECTOR, UserRole.SALES_MANAGER,
            UserRole.MARKETING_MANAGER
          ]
        },
        {
          title: "Revenue Analysis",
          href: "/dashboard/crm/analytics/revenue",
          icon: DollarSign,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.SALES_DIRECTOR, UserRole.SALES_MANAGER,
            UserRole.MARKETING_MANAGER
          ]
        },
        {
          title: "Customer Metrics",
          href: "/dashboard/crm/analytics/customers",
          icon: UserCircle,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.SALES_DIRECTOR, UserRole.SALES_MANAGER,
            UserRole.MARKETING_MANAGER
          ]
        },
        {
          title: "Forecasting",
          href: "/dashboard/crm/analytics/forecasting",
          icon: TrendingUp,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.SALES_DIRECTOR, UserRole.SALES_MANAGER
          ]
        },
        {
          title: "Custom Reports",
          href: "/dashboard/crm/analytics/reports",
          icon: FileBarChart,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.SALES_DIRECTOR, UserRole.SALES_MANAGER
          ]
        }
      ]
    },
  ]

  const allAdminItems = [
    {
      title: "Admin",
      href: "/dashboard/admin",
      icon: Shield,
      roles: [
        UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN
      ],
      subItems: [
        {
          title: "Security Alerts",
          href: "/dashboard/admin/security/alerts",
          icon: ShieldAlert,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN
          ]
        },
        {
          title: "Database Tools",
          href: "/dashboard/admin/database-tools",
          icon: Cog,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN
          ]
        },
        {
          title: "Fix Employee Numbers",
          href: "/dashboard/admin/fix-employee-numbers",
          icon: UserCog,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN
          ]
        },
        {
          title: "Database Remover",
          href: "/dashboard/admin/database-remover",
          icon: Database,
          roles: [
            UserRole.SUPER_ADMIN
          ]
        }
      ]
    }
  ]

  const allAuditItems = [
    {
      title: "Auditors",
      href: "/dashboard/auditors",
      icon: FileSearch,
      roles: [
        UserRole.SUPER_ADMIN, UserRole.AUDITOR
      ],
      subItems: [
        {
          title: "Audit Dashboard",
          href: "/dashboard/auditors",
          icon: LayoutDashboard,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.AUDITOR
          ]
        },
        {
          title: "Deleted Items",
          href: "/dashboard/auditors/deleted-items",
          icon: Trash2,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.AUDITOR
          ]
        },
        {
          title: "Audit Trail",
          href: "/dashboard/auditors/audit-trail",
          icon: FileText,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.AUDITOR
          ]
        },
        {
          title: "Compliance Reports",
          href: "/dashboard/auditors/compliance-reports",
          icon: BarChart3,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.AUDITOR
          ]
        },
        {
          title: "Recovery Center",
          href: "/dashboard/auditors/recovery-center",
          icon: RotateCcw,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.AUDITOR
          ]
        },
        {
          title: "Audit Settings",
          href: "/dashboard/auditors/settings",
          icon: Settings,
          roles: [
            UserRole.SUPER_ADMIN
          ]
        }
      ]
    }
  ]

  const allSystemItems = [
    {
      title: "Calendar",
      href: "/dashboard/calendar",
      icon: Calendar,
      roles: [
        UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST, UserRole.RECRUITER, UserRole.TRAINING_COORDINATOR, UserRole.DEPARTMENT_HEAD,
        UserRole.TEAM_LEADER, UserRole.EMPLOYEE, UserRole.CONTRACTOR, UserRole.INTERN, UserRole.FINANCE_DIRECTOR,
        UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT, UserRole.PAYROLL_SPECIALIST, UserRole.SALES_DIRECTOR,
        UserRole.SALES_MANAGER, UserRole.ACCOUNT_MANAGER, UserRole.SALES_REPRESENTATIVE, UserRole.CUSTOMER_SERVICE_MANAGER,
        UserRole.CUSTOMER_SERVICE_REPRESENTATIVE, UserRole.MARKETING_MANAGER, UserRole.MARKETING_SPECIALIST
      ], // All roles can access calendar
      subItems: [
        {
          title: "Overview",
          href: "/dashboard/calendar",
          icon: Calendar,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.RECRUITER, UserRole.TRAINING_COORDINATOR, UserRole.DEPARTMENT_HEAD,
            UserRole.TEAM_LEADER, UserRole.EMPLOYEE, UserRole.CONTRACTOR, UserRole.INTERN, UserRole.FINANCE_DIRECTOR,
            UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT, UserRole.PAYROLL_SPECIALIST, UserRole.SALES_DIRECTOR,
            UserRole.SALES_MANAGER, UserRole.ACCOUNT_MANAGER, UserRole.SALES_REPRESENTATIVE, UserRole.CUSTOMER_SERVICE_MANAGER,
            UserRole.CUSTOMER_SERVICE_REPRESENTATIVE, UserRole.MARKETING_MANAGER, UserRole.MARKETING_SPECIALIST
          ]
        },
        {
          title: "My Events",
          href: "/dashboard/calendar/events",
          icon: ListTodo,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.RECRUITER, UserRole.TRAINING_COORDINATOR, UserRole.DEPARTMENT_HEAD,
            UserRole.TEAM_LEADER, UserRole.EMPLOYEE, UserRole.CONTRACTOR, UserRole.INTERN, UserRole.FINANCE_DIRECTOR,
            UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT, UserRole.PAYROLL_SPECIALIST, UserRole.SALES_DIRECTOR,
            UserRole.SALES_MANAGER, UserRole.ACCOUNT_MANAGER, UserRole.SALES_REPRESENTATIVE, UserRole.CUSTOMER_SERVICE_MANAGER,
            UserRole.CUSTOMER_SERVICE_REPRESENTATIVE, UserRole.MARKETING_MANAGER, UserRole.MARKETING_SPECIALIST
          ]
        },
        {
          title: "Team Schedule",
          href: "/dashboard/calendar/team",
          icon: Users,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
          ]
        },
        {
          title: "Analytics",
          href: "/dashboard/calendar/analytics",
          icon: BarChart3,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER
          ]
        },
        {
          title: "Settings",
          href: "/dashboard/calendar/settings",
          icon: Settings,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.RECRUITER, UserRole.TRAINING_COORDINATOR, UserRole.DEPARTMENT_HEAD,
            UserRole.TEAM_LEADER, UserRole.EMPLOYEE, UserRole.CONTRACTOR, UserRole.INTERN, UserRole.FINANCE_DIRECTOR,
            UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT, UserRole.PAYROLL_SPECIALIST, UserRole.SALES_DIRECTOR,
            UserRole.SALES_MANAGER, UserRole.ACCOUNT_MANAGER, UserRole.SALES_REPRESENTATIVE, UserRole.CUSTOMER_SERVICE_MANAGER,
            UserRole.CUSTOMER_SERVICE_REPRESENTATIVE, UserRole.MARKETING_MANAGER, UserRole.MARKETING_SPECIALIST
          ]
        }
      ]
    },
    {
      title: "Notifications",
      href: "/dashboard/notifications",
      icon: BellRing,
      badge: 3,
      roles: [
        UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST, UserRole.RECRUITER, UserRole.TRAINING_COORDINATOR, UserRole.DEPARTMENT_HEAD,
        UserRole.TEAM_LEADER, UserRole.EMPLOYEE, UserRole.CONTRACTOR, UserRole.INTERN, UserRole.FINANCE_DIRECTOR,
        UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT, UserRole.PAYROLL_SPECIALIST, UserRole.SALES_DIRECTOR,
        UserRole.SALES_MANAGER, UserRole.ACCOUNT_MANAGER, UserRole.SALES_REPRESENTATIVE, UserRole.CUSTOMER_SERVICE_MANAGER,
        UserRole.CUSTOMER_SERVICE_REPRESENTATIVE, UserRole.MARKETING_MANAGER, UserRole.MARKETING_SPECIALIST
      ], // All roles can access notifications
      subItems: [
        {
          title: "All Notifications",
          href: "/dashboard/notifications",
          icon: Bell,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.RECRUITER, UserRole.TRAINING_COORDINATOR, UserRole.DEPARTMENT_HEAD,
            UserRole.TEAM_LEADER, UserRole.EMPLOYEE, UserRole.CONTRACTOR, UserRole.INTERN, UserRole.FINANCE_DIRECTOR,
            UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT, UserRole.PAYROLL_SPECIALIST, UserRole.SALES_DIRECTOR,
            UserRole.SALES_MANAGER, UserRole.ACCOUNT_MANAGER, UserRole.SALES_REPRESENTATIVE, UserRole.CUSTOMER_SERVICE_MANAGER,
            UserRole.CUSTOMER_SERVICE_REPRESENTATIVE, UserRole.MARKETING_MANAGER, UserRole.MARKETING_SPECIALIST
          ]
        },
        {
          title: "Unread",
          href: "/dashboard/notifications/unread",
          icon: Clock,
          badge: 3,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.RECRUITER, UserRole.TRAINING_COORDINATOR, UserRole.DEPARTMENT_HEAD,
            UserRole.TEAM_LEADER, UserRole.EMPLOYEE, UserRole.CONTRACTOR, UserRole.INTERN, UserRole.FINANCE_DIRECTOR,
            UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT, UserRole.PAYROLL_SPECIALIST, UserRole.SALES_DIRECTOR,
            UserRole.SALES_MANAGER, UserRole.ACCOUNT_MANAGER, UserRole.SALES_REPRESENTATIVE, UserRole.CUSTOMER_SERVICE_MANAGER,
            UserRole.CUSTOMER_SERVICE_REPRESENTATIVE, UserRole.MARKETING_MANAGER, UserRole.MARKETING_SPECIALIST
          ]
        },
        {
          title: "Important",
          href: "/dashboard/notifications/important",
          icon: AlertCircle,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.RECRUITER, UserRole.TRAINING_COORDINATOR, UserRole.DEPARTMENT_HEAD,
            UserRole.TEAM_LEADER, UserRole.EMPLOYEE, UserRole.CONTRACTOR, UserRole.INTERN, UserRole.FINANCE_DIRECTOR,
            UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT, UserRole.PAYROLL_SPECIALIST, UserRole.SALES_DIRECTOR,
            UserRole.SALES_MANAGER, UserRole.ACCOUNT_MANAGER, UserRole.SALES_REPRESENTATIVE, UserRole.CUSTOMER_SERVICE_MANAGER,
            UserRole.CUSTOMER_SERVICE_REPRESENTATIVE, UserRole.MARKETING_MANAGER, UserRole.MARKETING_SPECIALIST
          ]
        },
        {
          title: "Archive",
          href: "/dashboard/notifications/archive",
          icon: Archive,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.RECRUITER, UserRole.TRAINING_COORDINATOR, UserRole.DEPARTMENT_HEAD,
            UserRole.TEAM_LEADER, UserRole.EMPLOYEE, UserRole.CONTRACTOR, UserRole.INTERN, UserRole.FINANCE_DIRECTOR,
            UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT, UserRole.PAYROLL_SPECIALIST, UserRole.SALES_DIRECTOR,
            UserRole.SALES_MANAGER, UserRole.ACCOUNT_MANAGER, UserRole.SALES_REPRESENTATIVE, UserRole.CUSTOMER_SERVICE_MANAGER,
            UserRole.CUSTOMER_SERVICE_REPRESENTATIVE, UserRole.MARKETING_MANAGER, UserRole.MARKETING_SPECIALIST
          ]
        },
        {
          title: "Settings",
          href: "/dashboard/notifications/settings",
          icon: Settings,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.RECRUITER, UserRole.TRAINING_COORDINATOR, UserRole.DEPARTMENT_HEAD,
            UserRole.TEAM_LEADER, UserRole.EMPLOYEE, UserRole.CONTRACTOR, UserRole.INTERN, UserRole.FINANCE_DIRECTOR,
            UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT, UserRole.PAYROLL_SPECIALIST, UserRole.SALES_DIRECTOR,
            UserRole.SALES_MANAGER, UserRole.ACCOUNT_MANAGER, UserRole.SALES_REPRESENTATIVE, UserRole.CUSTOMER_SERVICE_MANAGER,
            UserRole.CUSTOMER_SERVICE_REPRESENTATIVE, UserRole.MARKETING_MANAGER, UserRole.MARKETING_SPECIALIST
          ]
        }
      ]
    },
    {
      title: "Security Management",
      href: "/dashboard/admin/security",
      icon: ShieldAlert,
      roles: [
        UserRole.SUPER_ADMIN
      ], // Only super admin can access security management
      subItems: [
        {
          title: "Active Sessions",
          href: "/dashboard/admin/security/sessions",
          icon: MonitorSmartphone,
          roles: [UserRole.SUPER_ADMIN]
        },
        {
          title: "User Access Control",
          href: "/dashboard/admin/security/users",
          icon: Shield,
          roles: [UserRole.SUPER_ADMIN]
        },
        {
          title: "Blocking Management",
          href: "/dashboard/admin/security/blocking",
          icon: Ban,
          roles: [UserRole.SUPER_ADMIN]
        }
      ]
    },

    {
      title: "Settings",
      href: "/dashboard/settings",
      icon: Settings,
      roles: [
        UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER, UserRole.EMPLOYEE,
        UserRole.CONTRACTOR, UserRole.INTERN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER,
        UserRole.ACCOUNTANT, UserRole.PAYROLL_SPECIALIST, UserRole.SALES_DIRECTOR, UserRole.SALES_MANAGER,
        UserRole.ACCOUNT_MANAGER, UserRole.SALES_REPRESENTATIVE, UserRole.CUSTOMER_SERVICE_MANAGER,
        UserRole.CUSTOMER_SERVICE_REPRESENTATIVE, UserRole.MARKETING_MANAGER, UserRole.MARKETING_SPECIALIST
      ],
      subItems: [
        {
          title: "General",
          href: "/dashboard/settings/general",
          icon: Cog,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN]
        },
        {
          title: "Company",
          href: "/dashboard/settings/company",
          icon: Building2,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER]
        },
        {
          title: "Users",
          href: "/dashboard/settings/users",
          icon: UserCog,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER]
        },
        {
          title: "Notifications",
          href: "/dashboard/settings/notifications",
          icon: Bell,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER, UserRole.EMPLOYEE,
            UserRole.CONTRACTOR, UserRole.INTERN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER,
            UserRole.ACCOUNTANT, UserRole.PAYROLL_SPECIALIST, UserRole.SALES_DIRECTOR, UserRole.SALES_MANAGER,
            UserRole.ACCOUNT_MANAGER, UserRole.SALES_REPRESENTATIVE, UserRole.CUSTOMER_SERVICE_MANAGER,
            UserRole.CUSTOMER_SERVICE_REPRESENTATIVE, UserRole.MARKETING_MANAGER, UserRole.MARKETING_SPECIALIST
          ]
        },
        {
          title: "Integrations",
          href: "/dashboard/settings/integrations",
          icon: Layers,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN]
        },
        {
          title: "Security",
          href: "/dashboard/settings/security",
          icon: Lock,
          roles: [
            UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
            UserRole.HR_SPECIALIST, UserRole.DEPARTMENT_HEAD, UserRole.TEAM_LEADER, UserRole.EMPLOYEE,
            UserRole.CONTRACTOR, UserRole.INTERN, UserRole.FINANCE_DIRECTOR, UserRole.FINANCE_MANAGER,
            UserRole.ACCOUNTANT, UserRole.PAYROLL_SPECIALIST, UserRole.SALES_DIRECTOR, UserRole.SALES_MANAGER,
            UserRole.ACCOUNT_MANAGER, UserRole.SALES_REPRESENTATIVE, UserRole.CUSTOMER_SERVICE_MANAGER,
            UserRole.CUSTOMER_SERVICE_REPRESENTATIVE, UserRole.MARKETING_MANAGER, UserRole.MARKETING_SPECIALIST
          ]
        }
      ]
    },
    {
      title: "Debug Services",
      href: "/dashboard/debug",
      icon: Bug,
      roles: [
        UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER
      ],
      subItems: [
        {
          title: "Dashboard",
          href: "/dashboard/debug",
          icon: LayoutDashboard,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER]
        },
        {
          title: "System Tests",
          href: "/dashboard/debug/system",
          icon: Database,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN]
        },
        {
          title: "Authentication",
          href: "/dashboard/debug/auth",
          icon: Shield,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN]
        },
        {
          title: "Leave Management",
          href: "/dashboard/debug/leave",
          icon: FileText,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER]
        },
        {
          title: "Performance",
          href: "/dashboard/debug/performance",
          icon: Zap,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN]
        },
        {
          title: "API Endpoints",
          href: "/dashboard/debug/api",
          icon: ArrowRightLeft,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN]
        },
        {
          title: "Health Checks",
          href: "/dashboard/debug/health",
          icon: AlertCircle,
          roles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER]
        }
      ]
    },
    {
      title: "Help & Support",
      href: "/dashboard/support",
      icon: LifeBuoy,
      roles: [
        UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN, UserRole.HR_DIRECTOR, UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST, UserRole.RECRUITER, UserRole.TRAINING_COORDINATOR, UserRole.DEPARTMENT_HEAD,
        UserRole.TEAM_LEADER, UserRole.EMPLOYEE, UserRole.CONTRACTOR, UserRole.INTERN, UserRole.FINANCE_DIRECTOR,
        UserRole.FINANCE_MANAGER, UserRole.ACCOUNTANT, UserRole.PAYROLL_SPECIALIST, UserRole.SALES_DIRECTOR,
        UserRole.SALES_MANAGER, UserRole.ACCOUNT_MANAGER, UserRole.SALES_REPRESENTATIVE, UserRole.CUSTOMER_SERVICE_MANAGER,
        UserRole.CUSTOMER_SERVICE_REPRESENTATIVE, UserRole.MARKETING_MANAGER, UserRole.MARKETING_SPECIALIST
      ] // All roles can access help & support
    },
  ]

  // Filter items based on user role
  const mainItems = allMainItems.filter(item => item.roles.includes(userRole));
  const managementItems = allManagementItems.filter(item => item.roles.includes(userRole));
  const crmItems = allCrmItems.filter(item => item.roles.includes(userRole));
  const adminItems = allAdminItems.filter(item => item.roles.includes(userRole));
  const auditItems = allAuditItems.filter(item => item.roles.includes(userRole));
  const systemItems = allSystemItems.filter(item => item.roles.includes(userRole));

  return (
    <div className="flex h-full flex-col">
      {/* Sidebar header with logo */}
      <div className="flex h-16 items-center gap-2 border-b border-border/40 px-6">
        <Building2 className="h-6 w-6 text-primary" />
        <span className="text-xl font-semibold tracking-tight">Kawandama Hills</span>
      </div>

      <Separator className="mx-3 my-2 bg-border/60" />

      {/* Scrollable navigation */}
      <ScrollArea className="flex-1 px-3 py-4">
        <div className="flex flex-col gap-4">
          {/* Main Section */}
          {mainItems.length > 0 && (
            <div className="flex flex-col">
              <div className="flex items-center px-4 mb-2">
                <h3 className="text-xs font-medium uppercase tracking-wider text-muted-foreground">Main</h3>
                <Separator className="flex-grow ml-2 bg-border/40" />
              </div>
              <SidebarNav items={mainItems} onNavigate={onNavigate} />
            </div>
          )}

          {/* Management Section */}
          {managementItems.length > 0 && (
            <div className="flex flex-col mt-2">
              <div className="flex items-center px-4 mb-2">
                <h3 className="text-xs font-medium uppercase tracking-wider text-muted-foreground">Management</h3>
                <Separator className="flex-grow ml-2 bg-border/40" />
              </div>
              <SidebarNav items={managementItems} onNavigate={onNavigate} />
            </div>
          )}

          {/* CRM Section */}
          {crmItems.length > 0 && (
            <div className="flex flex-col mt-2">
              <div className="flex items-center px-4 mb-2">
                <h3 className="text-xs font-medium uppercase tracking-wider text-muted-foreground">CRM</h3>
                <Separator className="flex-grow ml-2 bg-border/40" />
              </div>
              <SidebarNav items={crmItems} onNavigate={onNavigate} />
            </div>
          )}

          {/* Admin Section */}
          {adminItems.length > 0 && (
            <div className="flex flex-col mt-2">
              <div className="flex items-center px-4 mb-2">
                <h3 className="text-xs font-medium uppercase tracking-wider text-muted-foreground">Administration</h3>
                <Separator className="flex-grow ml-2 bg-border/40" />
              </div>
              <SidebarNav items={adminItems} onNavigate={onNavigate} />
            </div>
          )}

          {/* Audit Section */}
          {auditItems.length > 0 && (
            <div className="flex flex-col mt-2">
              <div className="flex items-center px-4 mb-2">
                <h3 className="text-xs font-medium uppercase tracking-wider text-muted-foreground">Audit & Compliance</h3>
                <Separator className="flex-grow ml-2 bg-border/40" />
              </div>
              <SidebarNav items={auditItems} onNavigate={onNavigate} />
            </div>
          )}

          {/* System Section */}
          {systemItems.length > 0 && (
            <div className="flex flex-col mt-2">
              <div className="flex items-center px-4 mb-2">
                <h3 className="text-xs font-medium uppercase tracking-wider text-muted-foreground">System</h3>
                <Separator className="flex-grow ml-2 bg-border/40" />
              </div>
              <SidebarNav items={systemItems} onNavigate={onNavigate} />
            </div>
          )}

          {/* Mobile Theme Switcher - only visible on mobile */}
          <div className="mt-6 md:hidden">
            <MobileThemeSwitcher />
          </div>
        </div>
      </ScrollArea>

      {/* Sidebar footer */}
      <div className="mt-auto border-t border-border/40 p-4">
        <div className="mb-4 flex items-center justify-between">
          <div>
            <h3 className="text-sm font-medium">
              {user ? `${user.firstName} ${user.lastName}` : 'Guest User'}
            </h3>
            <p className="text-xs text-muted-foreground">
              {user?.role ? user.role.replace('_', ' ').split(' ').map(word =>
                word.charAt(0).toUpperCase() + word.slice(1)
              ).join(' ') : 'Not signed in'}
            </p>
          </div>
          <Button variant="outline" size="icon" className="h-8 w-8" onClick={() => window.location.href = '/dashboard/profile'}>
            <Settings className="h-4 w-4" />
          </Button>
        </div>

        {/* Documentation Link - Accessible to all users */}
        <Button
          variant="ghost"
          className="w-full justify-start gap-2 text-muted-foreground hover:text-foreground mb-2"
          asChild
        >
          <Link href="/docs">
            <BookOpen className="h-4 w-4" />
            <span>Documentation</span>
          </Link>
        </Button>

        <Button
          variant="ghost"
          className="w-full justify-start gap-2 text-muted-foreground hover:text-foreground"
          onClick={async () => {
            try {
              // Call the logout API through our auth hook
              await logout();

              // Redirect to loading page which will handle further redirection
              window.location.href = '/loading';
            } catch (error) {
              console.error('Logout failed:', error);

              // Fallback: manually clear cookie and redirect
              document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; samesite=lax';
              window.location.href = '/login';
            }
          }}
        >
          <LogOut className="h-4 w-4" />
          <span>Log out</span>
        </Button>
      </div>
    </div>
  )
}
