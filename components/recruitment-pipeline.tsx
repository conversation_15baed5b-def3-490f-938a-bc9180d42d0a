"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { useEffect, useState, useCallback } from "react"
import { toast } from "@/components/ui/use-toast"
import {
  RefreshCw,
  AlertCircle,
  Clock,
  Users,
  Briefcase,
  Calendar,
  MapPin,
  AlertTriangle
} from "lucide-react"
import { EmptyState, useEmptyState } from "@/components/ui/empty-state"
import { RecruitmentAnalytics, OpenPosition } from "@/lib/services/dashboard/recruitment-pipeline-service"
import { apiRequest, ApiError } from "@/lib/utils/api-error-handler"

interface RecruitmentPipelineProps {
  className?: string
}

// Remove static data - will be replaced with real API data

export function RecruitmentPipeline({ className }: RecruitmentPipelineProps) {
  const [recruitmentData, setRecruitmentData] = useState<RecruitmentAnalytics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())
  const { getEmptyState } = useEmptyState()

  const fetchRecruitmentData = useCallback(async (showToast = false) => {
    setIsLoading(true)
    setError(null)

    try {
      const data = await apiRequest('/api/dashboard/organization/recruitment', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'all', limit: 10 })
      })

      if (data.success && data.data?.analytics) {
        setRecruitmentData(data.data.analytics)
        setLastRefresh(new Date())

        if (showToast) {
          toast({
            title: "Recruitment Data Updated",
            description: `Loaded ${data.data.analytics.totalOpenPositions} open positions`,
            variant: "default",
          })
        }
      } else {
        throw new ApiError('Invalid response format', 500)
      }

    } catch (err) {
      console.error('Error fetching recruitment data:', err)

      let errorMessage = 'Failed to load recruitment data'
      if (err instanceof ApiError) {
        errorMessage = err.message
      } else if (err instanceof Error) {
        errorMessage = err.message
      }

      setError(errorMessage)

      if (showToast) {
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        })
      }
    } finally {
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchRecruitmentData()

    // Set up auto-refresh every 10 minutes
    const interval = setInterval(() => {
      fetchRecruitmentData()
    }, 600000)

    return () => clearInterval(interval)
  }, [fetchRecruitmentData])

  const handleRefresh = () => {
    fetchRecruitmentData(true)
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'paused':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'closed':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'draft':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-600'
      case 'high':
        return 'text-orange-600'
      case 'medium':
        return 'text-yellow-600'
      case 'low':
        return 'text-gray-600'
      default:
        return 'text-gray-600'
    }
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const getDaysRemaining = (deadline: Date) => {
    const now = new Date()
    const diffTime = new Date(deadline).getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              Recruitment Pipeline
              {recruitmentData && (
                <Badge variant="secondary" className="text-xs">
                  {recruitmentData.totalOpenPositions} open
                </Badge>
              )}
              {recruitmentData && recruitmentData.openPositions.some(pos => pos.isUrgent) && (
                <Badge variant="destructive" className="text-xs">
                  {recruitmentData.openPositions.filter(pos => pos.isUrgent).length} urgent
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Current recruitment funnel and open positions
              {lastRefresh && (
                <span className="text-xs text-muted-foreground ml-2">
                  • Updated {lastRefresh.toLocaleTimeString()}
                </span>
              )}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {recruitmentData && (
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  <span>{recruitmentData.totalApplicants} applicants</span>
                </div>
                <div className="flex items-center gap-1">
                  <Briefcase className="h-3 w-3" />
                  <span>{recruitmentData.conversionRate.toFixed(1)}% conversion</span>
                </div>
              </div>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="pipeline" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="pipeline">Pipeline</TabsTrigger>
            <TabsTrigger value="positions">Open Positions</TabsTrigger>
          </TabsList>

          <TabsContent value="pipeline" className="space-y-4">
            {isLoading ? (
              <Skeleton className="h-[240px] w-full" />
            ) : getEmptyState(
              isLoading,
              error,
              recruitmentData && recruitmentData.pipelineStages.length > 0,
              {
                noDataTitle: "No recruitment data found",
                noDataDescription: "Pipeline data will appear here when available",
                onRetry: handleRefresh,
                icon: Briefcase
              }
            ) || (
              <div className="h-[240px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={recruitmentData.pipelineStages}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                    <XAxis dataKey="stage" />
                    <YAxis />
                    <Tooltip
                      formatter={(value, name, props) => [
                        `${value} candidates`,
                        "Count",
                        props.payload.conversionRate ? `${props.payload.conversionRate.toFixed(1)}% conversion` : ''
                      ]}
                      contentStyle={{
                        backgroundColor: "hsl(var(--background))",
                        borderColor: "hsl(var(--border))",
                        borderRadius: "0.5rem",
                        boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
                      }}
                    />
                    <Bar
                      dataKey="count"
                      fill="#3b82f6"
                      radius={[4, 4, 0, 0]}
                      name="Candidates"
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            )}
          </TabsContent>

          <TabsContent value="positions">
            {isLoading ? (
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, index) => (
                  <div key={index} className="flex items-center justify-between rounded-lg border p-3">
                    <div className="space-y-1">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-3 w-20" />
                    </div>
                    <div className="flex items-center gap-4">
                      <Skeleton className="h-8 w-16" />
                      <Skeleton className="h-6 w-16" />
                    </div>
                  </div>
                ))}
              </div>
            ) : getEmptyState(
              isLoading,
              error,
              recruitmentData && recruitmentData.openPositions.length > 0,
              {
                noDataTitle: "No open positions found",
                noDataDescription: "Open positions will appear here when available",
                onRetry: handleRefresh,
                icon: Briefcase
              }
            ) || (
              <div className="space-y-4">
                {recruitmentData.openPositions.map((position) => (
                  <div
                    key={position.id}
                    className="flex items-start gap-4 rounded-lg border p-4 hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{position.title}</span>
                            {position.isUrgent && (
                              <AlertTriangle className="h-4 w-4 text-red-500" />
                            )}
                          </div>
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <Building className="h-3 w-3" />
                              {position.department}
                            </span>
                            <span className="flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              {position.location}
                            </span>
                            <span className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {position.daysOpen} days open
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center gap-3">
                          <div className="text-center">
                            <div className="text-sm font-medium">{position.applicants}</div>
                            <div className="text-xs text-muted-foreground">Applicants</div>
                          </div>

                          <div className="flex flex-col gap-1">
                            <Badge className={cn("text-xs", getStatusColor(position.status))}>
                              {position.status.charAt(0).toUpperCase() + position.status.slice(1)}
                            </Badge>
                            {position.priority !== 'low' && (
                              <Badge variant="outline" className={cn("text-xs", getPriorityColor(position.priority))}>
                                {position.priority}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>

                      {position.deadline && (
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          <span>
                            Deadline: {formatDate(position.deadline)}
                            {(() => {
                              const daysRemaining = getDaysRemaining(position.deadline);
                              if (daysRemaining < 0) {
                                return <span className="text-red-600 ml-1">(Overdue)</span>;
                              } else if (daysRemaining <= 3) {
                                return <span className="text-orange-600 ml-1">({daysRemaining} days left)</span>;
                              } else {
                                return <span className="ml-1">({daysRemaining} days left)</span>;
                              }
                            })()}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
