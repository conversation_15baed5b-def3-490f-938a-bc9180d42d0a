"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { EmployeeOverview } from "@/components/employee-overview"
import { DepartmentBreakdown } from "@/components/department-breakdown"
import { RecruitmentPipeline } from "@/components/recruitment-pipeline"
import { RecentActivities } from "@/components/recent-activities"
import { UpcomingReviews } from "@/components/upcoming-reviews"
import { PerformanceMetrics } from "@/components/performance-metrics"
import { LeaveManagement } from "@/components/leave-management"
import { DateRangePicker } from "@/components/date-range-picker"
import { PlusCircle } from "lucide-react"
import { MultiStepEmployeeFormOverlay } from "@/components/forms/overlays/multi-step-employee-form-overlay"

export function DashboardPage() {
  return (
    <DashboardShell>
      {/* Dashboard Header with Welcome Message */}
      <DashboardHeader heading="Dashboard" text="Welcome to your Kawandama Hills Plantation Management Dashboard.">
        <div className="flex items-center gap-2">
          <DateRangePicker />
          <MultiStepEmployeeFormOverlay
            mode="create"
            trigger={
              <Button>
                <PlusCircle className="mr-2 h-4 w-4" />
                New Employee
              </Button>
            }
          />
        </div>
      </DashboardHeader>

      {/* Key Metrics Section */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Key Metrics</h2>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <EmployeeOverview />
        </div>
      </section>

      {/* Department & Recruitment Section */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Organization Overview</h2>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
          <DepartmentBreakdown className="lg:col-span-4" />
          <RecruitmentPipeline className="lg:col-span-3" />
        </div>
      </section>

      {/* Activities & Reviews Section */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Recent Updates</h2>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
          <RecentActivities className="lg:col-span-4" />
          <UpcomingReviews className="lg:col-span-3" />
        </div>
      </section>

      {/* Performance & Leave Section */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Performance & Time Off</h2>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
          <PerformanceMetrics className="lg:col-span-4" />
          <LeaveManagement className="lg:col-span-3" />
        </div>
      </section>
    </DashboardShell>
  )
}
