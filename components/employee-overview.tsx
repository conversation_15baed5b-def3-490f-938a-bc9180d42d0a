import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { Users, UserPlus, UserMinus, User<PERSON><PERSON>ck, Briefcase, TrendingUp, TrendingDown, Minus } from "lucide-react"
import React, { useEffect, useState, useCallback } from "react"
import { useEmployeeStore } from "@/lib/frontend/employeeStore"
import { useDepartmentStore } from "@/lib/frontend/departmentStore"
import { Skeleton } from "@/components/ui/skeleton"
import { apiGet, ApiError } from "@/lib/utils/api-error-handler"

interface EmployeeOverviewProps {
  className?: string
}

interface DashboardStats {
  employees: {
    total: number
    active: number
    inactive: number
    onLeave: number
    terminated: number
    changePercentage: number
  }
  newHires: {
    count: number
    inProbation: number
    changePercentage: number
  }
  turnover: {
    rate: string
    changePercentage: number
  }
  attendance: {
    rate: number
    changePercentage: number
  }
  departmentDistribution: Array<{ department: string; count: number }>
  employmentTypeDistribution: Array<{ type: string; count: number }>
}

export function EmployeeOverview({ className }: EmployeeOverviewProps) {
  const { fetchEmployees } = useEmployeeStore()
  const { departments, fetchDepartments } = useDepartmentStore()
  const [totalEmployees, setTotalEmployees] = useState<number | null>(null)
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchDashboardStats = useCallback(async () => {
    try {
      const data = await apiGet('/api/dashboard/stats')
      // The API returns data directly, not wrapped in success/data structure
      if (data && typeof data === 'object' && data.employees && data.newHires && data.turnover && data.attendance) {
        setDashboardStats(data)
        setError(null)
      } else {
        throw new ApiError('Invalid response format', 500)
      }
    } catch (err) {
      console.error('Error fetching dashboard stats:', err)
      let errorMessage = 'Failed to load dashboard statistics'
      if (err instanceof ApiError) {
        errorMessage = err.message
      } else if (err instanceof Error) {
        errorMessage = err.message
      }
      setError(errorMessage)
    }
  }, [])

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      try {
        // Fetch employees to get total count
        const employeeData = await fetchEmployees(1, 1, "")
        setTotalEmployees(employeeData?.totalDocs || 0)

        // Fetch departments
        await fetchDepartments()

        // Fetch dashboard stats for metrics
        await fetchDashboardStats()
      } catch (error) {
        console.error("Error loading data for stats:", error)
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [fetchEmployees, fetchDepartments, fetchDashboardStats])

  // Helper function to get trend icon and color
  const getTrendIndicator = (changePercentage: number) => {
    if (changePercentage > 0) {
      return {
        icon: TrendingUp,
        color: "text-green-600 dark:text-green-400",
        prefix: "+"
      }
    } else if (changePercentage < 0) {
      return {
        icon: TrendingDown,
        color: "text-red-600 dark:text-red-400",
        prefix: ""
      }
    } else {
      return {
        icon: Minus,
        color: "text-gray-600 dark:text-gray-400",
        prefix: ""
      }
    }
  }

  // Helper function to format change text
  const formatChangeText = (changePercentage: number, context: string) => {
    const trend = getTrendIndicator(changePercentage)
    return `${trend.prefix}${Math.abs(changePercentage).toFixed(1)}% ${context}`
  }

  return (
    <>
      <Card className={cn("overflow-hidden", className)}>
        <CardContent className="p-0">
          <div className="bg-gradient-to-r from-primary/20 to-primary/5 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Employees</p>
                <h3 className="mt-1 text-3xl font-bold">
                  {isLoading ? "Loading..." : totalEmployees !== null ? totalEmployees : "N/A"}
                </h3>
                <p className="mt-1 flex items-center text-xs font-medium text-green-600 dark:text-green-400">
                  <span className="i-lucide-trending-up mr-1 h-3 w-3" />
                  Active workforce
                </p>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/20">
                <Users className="h-6 w-6 text-primary" />
              </div>
            </div>
            <div className="mt-4 h-1.5 w-full overflow-hidden rounded-full bg-primary/10">
              <div className="h-full w-full rounded-full bg-primary"></div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className={cn("overflow-hidden", className)}>
        <CardContent className="p-0">
          <div className="bg-gradient-to-r from-amber-500/20 to-amber-500/5 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Departments</p>
                <h3 className="mt-1 text-3xl font-bold">
                  {isLoading ? "Loading..." : departments.length}
                </h3>
                <p className="mt-1 flex items-center text-xs font-medium text-amber-600 dark:text-amber-400">
                  <span className="i-lucide-trending-up mr-1 h-3 w-3" />
                  Active departments
                </p>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-amber-500/20">
                <Briefcase className="h-6 w-6 text-amber-600 dark:text-amber-400" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className={cn("overflow-hidden", className)}>
        <CardContent className="p-0">
          <div className="bg-gradient-to-r from-green-500/20 to-green-500/5 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">New Hires</p>
                <h3 className="mt-1 text-3xl font-bold">
                  {isLoading ? (
                    <Skeleton className="h-8 w-16" />
                  ) : dashboardStats ? (
                    dashboardStats.newHires.count
                  ) : (
                    "N/A"
                  )}
                </h3>
                {isLoading ? (
                  <Skeleton className="h-4 w-24 mt-1" />
                ) : dashboardStats ? (
                  <p className={cn(
                    "mt-1 flex items-center text-xs font-medium",
                    getTrendIndicator(dashboardStats.newHires.changePercentage).color
                  )}>
                    {React.createElement(getTrendIndicator(dashboardStats.newHires.changePercentage).icon, {
                      className: "mr-1 h-3 w-3"
                    })}
                    {formatChangeText(dashboardStats.newHires.changePercentage, "from last month")}
                  </p>
                ) : (
                  <p className="mt-1 text-xs text-muted-foreground">Data unavailable</p>
                )}
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-500/20">
                <UserPlus className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
            {isLoading ? (
              <Skeleton className="h-4 w-32 mt-4" />
            ) : dashboardStats && dashboardStats.newHires.inProbation > 0 ? (
              <div className="mt-4 flex items-center gap-1">
                <div className="h-2 w-2 rounded-full bg-green-500"></div>
                <p className="text-xs text-muted-foreground">
                  {dashboardStats.newHires.inProbation} in probation
                </p>
              </div>
            ) : null}
          </div>
        </CardContent>
      </Card>

      <Card className={cn("overflow-hidden", className)}>
        <CardContent className="p-0">
          <div className="bg-gradient-to-r from-amber-500/20 to-amber-500/5 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Turnover Rate</p>
                <h3 className="mt-1 text-3xl font-bold">
                  {isLoading ? (
                    <Skeleton className="h-8 w-16" />
                  ) : dashboardStats ? (
                    `${dashboardStats.turnover.rate}%`
                  ) : (
                    "N/A"
                  )}
                </h3>
                {isLoading ? (
                  <Skeleton className="h-4 w-24 mt-1" />
                ) : dashboardStats ? (
                  <p className={cn(
                    "mt-1 flex items-center text-xs font-medium",
                    // For turnover, lower is better, so invert the color logic
                    dashboardStats.turnover.changePercentage < 0
                      ? "text-green-600 dark:text-green-400"
                      : dashboardStats.turnover.changePercentage > 0
                      ? "text-red-600 dark:text-red-400"
                      : "text-gray-600 dark:text-gray-400"
                  )}>
                    {React.createElement(getTrendIndicator(dashboardStats.turnover.changePercentage).icon, {
                      className: "mr-1 h-3 w-3"
                    })}
                    {formatChangeText(dashboardStats.turnover.changePercentage, "from last month")}
                  </p>
                ) : (
                  <p className="mt-1 text-xs text-muted-foreground">Data unavailable</p>
                )}
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-amber-500/20">
                <UserMinus className="h-6 w-6 text-amber-600 dark:text-amber-400" />
              </div>
            </div>
            {isLoading ? (
              <Skeleton className="h-1.5 w-full mt-4" />
            ) : dashboardStats ? (
              <div className="mt-4 h-1.5 w-full overflow-hidden rounded-full bg-amber-500/10">
                <div
                  className="h-full rounded-full bg-amber-500"
                  style={{ width: `${Math.min(parseFloat(dashboardStats.turnover.rate), 100)}%` }}
                ></div>
              </div>
            ) : null}
          </div>
        </CardContent>
      </Card>

      <Card className={cn("overflow-hidden", className)}>
        <CardContent className="p-0">
          <div className="bg-gradient-to-r from-blue-500/20 to-blue-500/5 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Attendance Rate</p>
                <h3 className="mt-1 text-3xl font-bold">
                  {isLoading ? (
                    <Skeleton className="h-8 w-16" />
                  ) : dashboardStats ? (
                    `${dashboardStats.attendance.rate.toFixed(1)}%`
                  ) : (
                    "N/A"
                  )}
                </h3>
                {isLoading ? (
                  <Skeleton className="h-4 w-24 mt-1" />
                ) : dashboardStats ? (
                  <p className={cn(
                    "mt-1 flex items-center text-xs font-medium",
                    getTrendIndicator(dashboardStats.attendance.changePercentage).color
                  )}>
                    {React.createElement(getTrendIndicator(dashboardStats.attendance.changePercentage).icon, {
                      className: "mr-1 h-3 w-3"
                    })}
                    {formatChangeText(dashboardStats.attendance.changePercentage, "from last month")}
                  </p>
                ) : (
                  <p className="mt-1 text-xs text-muted-foreground">Data unavailable</p>
                )}
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-500/20">
                <UserCheck className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            {isLoading ? (
              <Skeleton className="h-1.5 w-full mt-4" />
            ) : dashboardStats ? (
              <div className="mt-4 h-1.5 w-full overflow-hidden rounded-full bg-blue-500/10">
                <div
                  className="h-full rounded-full bg-blue-500"
                  style={{ width: `${Math.min(dashboardStats.attendance.rate, 100)}%` }}
                ></div>
              </div>
            ) : null}
          </div>
        </CardContent>
      </Card>
    </>
  )
}
