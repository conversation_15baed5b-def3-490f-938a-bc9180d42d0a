// components/forms/leave-request-form.tsx
"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { IndependentDateRangePicker, DateRange } from "@/components/ui/independent-date-range-picker"
import { format, addDays } from "date-fns"
import type { LeaveRequest } from "@/types/leave-request"

const leaveRequestFormSchema = z.object({
  leaveType: z.string().min(1, { message: "Leave type is required." }),
  dateRange: z.object({
    from: z.date(),
    to: z.date(),
  }),
  reason: z.string().min(5, { message: "Please provide a reason for your leave request." }),
  contactInfo: z.string().optional(),
})

type LeaveRequestFormValues = z.infer<typeof leaveRequestFormSchema>

interface LeaveRequestFormProps {
  initialData?: Partial<LeaveRequest>
  onSubmit: (data: LeaveRequestFormValues) => void | Promise<void>
}

export function LeaveRequestForm({ initialData, onSubmit }: LeaveRequestFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [date, setDate] = useState<DateRange | undefined>({
    from: initialData?.startDate ? new Date(initialData.startDate) : new Date(),
    to: initialData?.endDate ? new Date(initialData.endDate) : addDays(new Date(), 5),
  })

  const defaultValues: Partial<LeaveRequestFormValues> = {
    leaveType: (initialData as any)?.leaveType || "annual",
    dateRange: (date && date.from && date.to) ? { from: date.from, to: date.to } : { from: new Date(), to: addDays(new Date(), 5) },
    reason: initialData?.reason || "",
    contactInfo: (initialData as any)?.contactInfo || "",
  }

  const form = useForm<LeaveRequestFormValues>({
    resolver: zodResolver(leaveRequestFormSchema) as any,
    defaultValues,
  })

  const handleSubmit = async (data: LeaveRequestFormValues) => {
    setIsSubmitting(true)
    try {
      await onSubmit(data)
      form.reset()
    } catch (error) {
      console.error("Error submitting form:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control as any}
          name="leaveType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Leave Type</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select leave type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="annual">Annual Leave</SelectItem>
                  <SelectItem value="sick">Sick Leave</SelectItem>
                  <SelectItem value="personal">Personal Leave</SelectItem>
                  <SelectItem value="maternity">Maternity Leave</SelectItem>
                  <SelectItem value="paternity">Paternity Leave</SelectItem>
                  <SelectItem value="unpaid">Unpaid Leave</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control as any}
          name="dateRange"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Leave Dates</FormLabel>
              <FormControl>
                <IndependentDateRangePicker
                  value={field.value}
                  onChange={(value) => {
                    field.onChange(value)
                    setDate(value)
                  }}
                  placeholder="Select your leave dates"
                  minDate={new Date()} // Prevent selecting past dates
                  labels={{
                    startDate: "Start Date",
                    endDate: "End Date",
                    clear: "Clear Dates"
                  }}
                  clearable={true}
                  allowSingleDate={false} // Require both start and end dates
                  className="w-full"
                />
              </FormControl>
              <FormDescription>
                Select the start and end dates for your leave request. Each date can be selected independently.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control as any}
          name="reason"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Reason for Leave</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Please provide details about your leave request"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control as any}
          name="contactInfo"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Emergency Contact Information</FormLabel>
              <FormControl>
                <Textarea placeholder="Contact information during leave (optional)" {...field} />
              </FormControl>
              <FormDescription>Provide contact details in case of emergency during your leave.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline">
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Submitting..." : initialData ? "Update Request" : "Submit Request"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
