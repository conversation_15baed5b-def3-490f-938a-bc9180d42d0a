"use client"

import type React from "react"
import { FormOverlay } from "@/components/forms/form-overlay"
import { LeaveRequestFormWithStats } from "@/components/leave-management/leave-request-form-with-stats"
import type { LeaveRequest } from "@/types/leave-request"

interface LeaveRequestFormOverlayProps {
  initialData?: Partial<LeaveRequest>
  trigger: React.ReactNode
  mode: "create" | "edit"
}

export function LeaveRequestFormOverlay({ initialData, trigger, mode }: LeaveRequestFormOverlayProps) {
  const handleSubmit = () => {
    // In a real app, this would save the data to the database
    console.log("Leave request submitted successfully")
  }

  return (
    <FormOverlay
      title={mode === "create" ? "Request Leave" : "Edit Leave Request"}
      description={mode === "create" ? "Submit a new leave request for an employee" : "Update leave request details"}
      trigger={trigger}
      size="full"
      onSubmit={handleSubmit}
    >
      <LeaveRequestFormWithStats
        onSuccess={handleSubmit}
        onCancel={() => console.log("Leave request cancelled")}
      />
    </FormOverlay>
  )
}
