"use client"

import type React from "react"
import { useState } from "react"
import { FormOverlay } from "@/components/forms/form-overlay"
import { LeaveRequestFormWithStats } from "@/components/leave-management/leave-request-form-with-stats"
import type { LeaveRequest } from "@/types/leave-request"

interface LeaveRequestFormOverlayProps {
  initialData?: Partial<LeaveRequest>
  trigger: React.ReactNode
  mode: "create" | "edit"
  onSuccess?: () => void
}

export function LeaveRequestFormOverlay({ initialData, trigger, mode, onSuccess }: LeaveRequestFormOverlayProps) {
  const [open, setOpen] = useState(false)

  const handleSuccess = () => {
    // Close the dialog on successful submission
    setOpen(false)
    // Call parent success callback if provided
    onSuccess?.()
  }

  const handleCancel = () => {
    // Close the dialog on cancel
    setOpen(false)
  }

  return (
    <FormOverlay
      title={mode === "create" ? "Request Leave" : "Edit Leave Request"}
      description={mode === "create" ? "Submit a new leave request for an employee" : "Update leave request details"}
      trigger={trigger}
      size="full"
      open={open}
      onOpenChange={setOpen}
    >
      <LeaveRequestFormWithStats
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </FormOverlay>
  )
}
