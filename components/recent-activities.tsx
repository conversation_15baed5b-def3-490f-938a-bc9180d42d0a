"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { useEffect, useState, useCallback } from "react"
import { Skeleton } from "@/components/ui/skeleton"
import { Button } from "@/components/ui/button"
import { RefreshCw, AlertCircle } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import Link from "next/link"

interface RecentActivitiesProps {
  className?: string
}

interface Activity {
  id: string
  user: {
    name: string
    avatar: string
    initials: string
    email?: string
  }
  employee?: {
    name: string
    id: string
  }
  action: string
  department: string
  time: string
  type: string
  timestamp: Date
  module: string
  entityType: string
  entityId: string
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
}

export function RecentActivities({ className }: RecentActivitiesProps) {
  const [activities, setActivities] = useState<Activity[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())

  const fetchRecentActivities = useCallback(async (showToast = false) => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/dashboard/activities?limit=8')

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch recent activities')
      }

      const data = await response.json()

      if (data.success && data.data?.activities) {
        setActivities(data.data.activities)
        setLastRefresh(new Date())

        if (showToast) {
          toast({
            title: "Activities Updated",
            description: `Loaded ${data.data.activities.length} recent activities`,
            variant: "default",
          })
        }
      } else {
        throw new Error('Invalid response format')
      }

    } catch (err) {
      console.error('Error fetching recent activities:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to load recent activities'
      setError(errorMessage)

      if (showToast) {
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        })
      }
    } finally {
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchRecentActivities()

    // Set up auto-refresh every 30 seconds
    const interval = setInterval(() => {
      fetchRecentActivities()
    }, 30000)

    return () => clearInterval(interval)
  }, [fetchRecentActivities])

  const handleRefresh = () => {
    fetchRecentActivities(true)
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'low':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getActivityTypeIcon = (type: string) => {
    switch (type) {
      case 'new-employee':
        return '👤'
      case 'update-employee':
        return '✏️'
      case 'create':
        return '➕'
      case 'update':
        return '📝'
      case 'delete':
        return '🗑️'
      case 'approve':
        return '✅'
      case 'reject':
        return '❌'
      default:
        return '📋'
    }
  }

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              Recent Activities
              {activities.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {activities.length}
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Latest system activities and changes
              {lastRefresh && (
                <span className="text-xs text-muted-foreground ml-2">
                  • Updated {lastRefresh.toLocaleTimeString()}
                </span>
              )}
            </CardDescription>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
            className="h-8 w-8 p-0"
          >
            <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="flex items-start gap-4">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                  <Skeleton className="h-3 w-1/4" />
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="py-6 text-center">
            <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground mb-3">{error}</p>
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              Try Again
            </Button>
          </div>
        ) : activities.length === 0 ? (
          <div className="py-6 text-center text-muted-foreground">
            <p className="text-sm">No recent activities found</p>
            <p className="text-xs mt-1">Activities will appear here as users interact with the system</p>
          </div>
        ) : (
          <div className="space-y-4">
            {activities.map((activity) => (
              <div key={activity.id} className="flex items-start gap-4 group hover:bg-muted/50 rounded-lg p-2 -m-2 transition-colors">
                <div className="relative">
                  <Avatar className="h-8 w-8 border border-primary/10">
                    <AvatarImage src={activity.user.avatar || "/placeholder.svg"} alt={activity.user.name} />
                    <AvatarFallback className="text-xs">{activity.user.initials}</AvatarFallback>
                  </Avatar>
                  <div className="absolute -bottom-1 -right-1 text-xs">
                    {getActivityTypeIcon(activity.type)}
                  </div>
                </div>
                <div className="flex-1 space-y-1 min-w-0">
                  <div className="flex items-center gap-2 flex-wrap">
                    <p className="text-sm font-medium leading-none truncate">{activity.user.name}</p>
                    <Badge variant="outline" className="text-xs font-normal">
                      {activity.department}
                    </Badge>
                    {activity.severity !== 'low' && (
                      <Badge className={cn("text-xs", getSeverityColor(activity.severity))}>
                        {activity.severity}
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {activity.employee ? (
                      <>
                        <Link
                          href={`/dashboard/hr/employees/${activity.employee.id}`}
                          className="font-medium text-primary hover:underline"
                        >
                          {activity.employee.name}
                        </Link>
                        {' '}
                        {activity.action}
                      </>
                    ) : (
                      activity.action
                    )}
                  </p>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <span>{activity.time}</span>
                    <span>•</span>
                    <span className="capitalize">{activity.module}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
