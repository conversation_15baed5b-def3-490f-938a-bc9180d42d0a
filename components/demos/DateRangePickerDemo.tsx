"use client"

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { IndependentDateRangePicker, DateRange } from '@/components/ui/independent-date-range-picker';
import { format } from 'date-fns';
import { Calendar, CheckCircle, XCircle } from 'lucide-react';

export function DateRangePickerDemo() {
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [previousRange, setPreviousRange] = useState<DateRange | undefined>();

  const handleDateChange = (newRange: DateRange | undefined) => {
    setPreviousRange(dateRange);
    setDateRange(newRange);
  };

  const formatDateRange = (range: DateRange | undefined) => {
    if (!range) return 'No dates selected';
    
    if (range.from && range.to) {
      return `${format(range.from, 'MMM dd, yyyy')} - ${format(range.to, 'MMM dd, yyyy')}`;
    }
    
    if (range.from) {
      return `Start: ${format(range.from, 'MMM dd, yyyy')}`;
    }
    
    if (range.to) {
      return `End: ${format(range.to, 'MMM dd, yyyy')}`;
    }
    
    return 'No dates selected';
  };

  const calculateDuration = (range: DateRange | undefined) => {
    if (range?.from && range?.to) {
      const diffTime = Math.abs(range.to.getTime() - range.from.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
      return diffDays;
    }
    return 0;
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">Improved Date Range Picker</h1>
        <p className="text-gray-600">
          Independent date selection with friction-free user experience
        </p>
      </div>

      {/* Main Demo */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            Leave Request Date Selection
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Select Leave Dates
              </label>
              <IndependentDateRangePicker
                value={dateRange}
                onChange={handleDateChange}
                placeholder="Choose your leave dates"
                minDate={new Date()}
                labels={{
                  startDate: "Start Date",
                  endDate: "End Date",
                  clear: "Clear Selection"
                }}
                clearable={true}
                allowSingleDate={false}
                className="max-w-md"
              />
            </div>

            {/* Current Selection Display */}
            <div className="p-4 bg-gray-50 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">Current Selection:</h3>
              <p className="text-gray-700">{formatDateRange(dateRange)}</p>
              
              {dateRange?.from && dateRange?.to && (
                <div className="mt-2 flex items-center gap-4">
                  <Badge variant="outline" className="text-green-600 border-green-600">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    {calculateDuration(dateRange)} days
                  </Badge>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Features Comparison */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg text-red-600 flex items-center gap-2">
              <XCircle className="w-5 h-5" />
              Previous Issues
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• Linked month navigation between calendars</li>
              <li>• Difficult to select dates across different months</li>
              <li>• Poor mobile experience with dual calendars</li>
              <li>• Confusing UX when months change together</li>
              <li>• Limited accessibility for date input</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg text-green-600 flex items-center gap-2">
              <CheckCircle className="w-5 h-5" />
              New Improvements
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• Independent date input fields</li>
              <li>• Visual calendar for easy selection</li>
              <li>• Mobile-optimized interface</li>
              <li>• Clear date validation and constraints</li>
              <li>• Keyboard accessible date inputs</li>
              <li>• Intuitive clear and reset functionality</li>
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Usage Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Key Features</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid md:grid-cols-3 gap-4">
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Independent Selection</h4>
              <p className="text-sm text-gray-600">
                Each date can be selected independently without affecting the other calendar view.
              </p>
            </div>
            
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Smart Validation</h4>
              <p className="text-sm text-gray-600">
                Automatic validation ensures end date is after start date and prevents past dates.
              </p>
            </div>
            
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Multiple Input Methods</h4>
              <p className="text-sm text-gray-600">
                Use date inputs for precise entry or visual calendar for quick selection.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Technical Details */}
      <Card>
        <CardHeader>
          <CardTitle>Implementation Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="prose prose-sm max-w-none">
            <p className="text-gray-600 mb-4">
              The new <code>IndependentDateRangePicker</code> component solves the UX issues by:
            </p>
            <ul className="text-gray-600 space-y-1">
              <li>Using separate HTML date inputs for precise date entry</li>
              <li>Providing a single calendar that updates based on which field is active</li>
              <li>Implementing smart date constraints to prevent invalid selections</li>
              <li>Supporting both keyboard and mouse interactions</li>
              <li>Maintaining accessibility standards with proper labels and ARIA attributes</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Previous Selection History */}
      {previousRange && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Previous Selection</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">{formatDateRange(previousRange)}</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
