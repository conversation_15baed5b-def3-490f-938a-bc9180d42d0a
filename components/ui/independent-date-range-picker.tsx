"use client"

import * as React from "react"
import { format, isValid, parse, isBefore, isAfter, startOfDay } from "date-fns"
import { Calendar as CalendarIcon, X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { cn } from "@/lib/utils"

export interface DateRange {
  from?: Date
  to?: Date
}

interface IndependentDateRangePickerProps {
  value?: DateRange
  onChange?: (range: DateRange | undefined) => void
  placeholder?: string
  disabled?: boolean
  className?: string
  minDate?: Date
  maxDate?: Date
  disabledDates?: (date: Date) => boolean
  allowSingleDate?: boolean
  clearable?: boolean
  formatString?: string
  labels?: {
    startDate?: string
    endDate?: string
    clear?: string
  }
}

export function IndependentDateRangePicker({
  value,
  onChange,
  placeholder = "Select date range",
  disabled = false,
  className,
  minDate,
  maxDate,
  disabledDates,
  allowSingleDate = true,
  clearable = true,
  formatString = "MMM dd, yyyy",
  labels = {
    startDate: "Start Date",
    endDate: "End Date",
    clear: "Clear"
  }
}: IndependentDateRangePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false)
  const [activeField, setActiveField] = React.useState<'start' | 'end' | null>(null)
  const [startInputValue, setStartInputValue] = React.useState("")
  const [endInputValue, setEndInputValue] = React.useState("")

  // Update input values when value prop changes
  React.useEffect(() => {
    if (value?.from) {
      setStartInputValue(format(value.from, "yyyy-MM-dd"))
    } else {
      setStartInputValue("")
    }
    
    if (value?.to) {
      setEndInputValue(format(value.to, "yyyy-MM-dd"))
    } else {
      setEndInputValue("")
    }
  }, [value])

  const handleDateSelect = (date: Date | undefined, field: 'start' | 'end') => {
    if (!date) return

    const newRange = { ...value }
    
    if (field === 'start') {
      newRange.from = date
      // If end date is before start date, clear it
      if (newRange.to && isBefore(newRange.to, date)) {
        newRange.to = undefined
      }
    } else {
      newRange.to = date
      // If start date is after end date, clear it
      if (newRange.from && isAfter(newRange.from, date)) {
        newRange.from = undefined
      }
    }

    onChange?.(newRange)
  }

  const handleInputChange = (inputValue: string, field: 'start' | 'end') => {
    if (field === 'start') {
      setStartInputValue(inputValue)
    } else {
      setEndInputValue(inputValue)
    }

    // Try to parse the date
    const parsedDate = parse(inputValue, "yyyy-MM-dd", new Date())
    if (isValid(parsedDate)) {
      handleDateSelect(parsedDate, field)
    }
  }

  const isDateDisabled = (date: Date, field: 'start' | 'end') => {
    // Apply min/max date restrictions
    if (minDate && isBefore(date, startOfDay(minDate))) return true
    if (maxDate && isAfter(date, startOfDay(maxDate))) return true
    
    // Apply custom disabled dates
    if (disabledDates && disabledDates(date)) return true
    
    // For end date, disable dates before start date
    if (field === 'end' && value?.from && isBefore(date, startOfDay(value.from))) {
      return true
    }
    
    // For start date, disable dates after end date
    if (field === 'start' && value?.to && isAfter(date, startOfDay(value.to))) {
      return true
    }
    
    return false
  }

  const clearRange = () => {
    onChange?.(undefined)
    setStartInputValue("")
    setEndInputValue("")
  }

  const formatDisplayValue = () => {
    if (!value?.from && !value?.to) return placeholder
    
    if (value.from && value.to) {
      return `${format(value.from, formatString)} - ${format(value.to, formatString)}`
    }
    
    if (value.from) {
      return allowSingleDate 
        ? format(value.from, formatString)
        : `${format(value.from, formatString)} - ...`
    }
    
    if (value.to) {
      return `... - ${format(value.to, formatString)}`
    }
    
    return placeholder
  }

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal",
              (!value?.from && !value?.to) && "text-muted-foreground"
            )}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            <span className="flex-1 truncate">{formatDisplayValue()}</span>
            {clearable && (value?.from || value?.to) && (
              <X 
                className="ml-2 h-4 w-4 opacity-50 hover:opacity-100" 
                onClick={(e) => {
                  e.stopPropagation()
                  clearRange()
                }}
              />
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="p-4 space-y-4">
            {/* Date Input Fields */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">{labels.startDate}</Label>
                <Input
                  type="date"
                  value={startInputValue}
                  onChange={(e) => handleInputChange(e.target.value, 'start')}
                  onFocus={() => setActiveField('start')}
                  min={minDate ? format(minDate, "yyyy-MM-dd") : undefined}
                  max={value?.to ? format(value.to, "yyyy-MM-dd") : maxDate ? format(maxDate, "yyyy-MM-dd") : undefined}
                  className="w-full"
                />
              </div>
              
              <div className="space-y-2">
                <Label className="text-sm font-medium">{labels.endDate}</Label>
                <Input
                  type="date"
                  value={endInputValue}
                  onChange={(e) => handleInputChange(e.target.value, 'end')}
                  onFocus={() => setActiveField('end')}
                  min={value?.from ? format(value.from, "yyyy-MM-dd") : minDate ? format(minDate, "yyyy-MM-dd") : undefined}
                  max={maxDate ? format(maxDate, "yyyy-MM-dd") : undefined}
                  className="w-full"
                />
              </div>
            </div>

            {/* Calendar for visual selection */}
            <div className="border-t pt-4">
              <div className="text-sm font-medium mb-2">
                {activeField === 'start' ? 'Select Start Date' : 
                 activeField === 'end' ? 'Select End Date' : 
                 'Click a date input above to select'}
              </div>
              
              {activeField && (
                <Calendar
                  mode="single"
                  selected={activeField === 'start' ? value?.from : value?.to}
                  onSelect={(date) => {
                    if (date && activeField) {
                      handleDateSelect(date, activeField)
                    }
                  }}
                  disabled={(date) => isDateDisabled(date, activeField)}
                  defaultMonth={
                    activeField === 'start' 
                      ? value?.from || new Date()
                      : value?.to || value?.from || new Date()
                  }
                  className="rounded-md border"
                />
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between items-center pt-2 border-t">
              {clearable && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearRange}
                  disabled={!value?.from && !value?.to}
                >
                  {labels.clear}
                </Button>
              )}
              
              <Button
                size="sm"
                onClick={() => setIsOpen(false)}
                className="ml-auto"
              >
                Done
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
