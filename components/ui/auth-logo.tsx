"use client";

import { BookOpen } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

interface AuthLogoProps {
  size?: 'sm' | 'md' | 'lg';
}

export function AuthLogo({ size = 'md' }: AuthLogoProps) {
  const logoSizeClasses = {
    sm: { width: 120, height: 120 },
    md: { width: 160, height: 160 },
    lg: { width: 200, height: 200 },
  };

  return (
    <div className="flex flex-col items-center">
      <div className="mb-6">
        <Image
          src="/images/logo.png"
          alt="Kawandama Hills Plantation Logo"
          width={logoSizeClasses[size].width}
          height={logoSizeClasses[size].height}
          style={{ objectFit: 'contain' }}
          priority
        />
      </div>
      <div className="text-center">
        <h2 className="text-2xl font-bold text-green-600 tracking-tight">
          Kawandama Hills Plantation
        </h2>
        <p className="text-sm text-gray-500 mt-1">Management System</p>
      </div>

      {/* Documentation Link - Close to title */}
                  <div className="flex justify-center pt-3">
                    <Link
                      href="/docs"
                      className="flex items-center gap-2 px-3 py-1.5 text-sm text-green-600 hover:text-green-700 hover:bg-green-50 rounded-md transition-colors duration-200 border border-green-200 hover:border-green-300"
                    >
                      <BookOpen className="h-4 w-4" />
                      <span className="font-medium">System Documentation & User Guides</span>
                    </Link>
                  </div>

    </div>
  );
}
