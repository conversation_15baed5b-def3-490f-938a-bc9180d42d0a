"use client"

import * as React from "react"
import { Check, ChevronsUpDown, Search, User, Users, Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"

export interface Employee {
  _id: string
  firstName: string
  lastName: string
  email: string
  employeeId?: string
  position?: string
  departmentId?: {
    _id: string
    name: string
  }
  profilePicture?: string
  employmentStatus: 'active' | 'inactive' | 'terminated'
}

interface EmployeeSelectorProps {
  employees: Employee[]
  value?: string
  onValueChange?: (value: string) => void
  onSearchChange?: (search: string) => void
  placeholder?: string
  emptyText?: string
  disabled?: boolean
  loading?: boolean
  className?: string
  showDepartment?: boolean
  showEmployeeId?: boolean
  showStatus?: boolean
  filterStatus?: 'active' | 'inactive' | 'terminated' | 'all'
}

export function EmployeeSelector({
  employees,
  value,
  onValueChange,
  onSearchChange,
  placeholder = "Select employee...",
  emptyText = "No employees found",
  disabled = false,
  loading = false,
  className,
  showDepartment = true,
  showEmployeeId = true,
  showStatus = false,
  filterStatus = 'active'
}: EmployeeSelectorProps) {
  const [open, setOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState("")

  // Filter employees based on status
  const filteredEmployees = React.useMemo(() => {
    let filtered = employees

    if (filterStatus !== 'all') {
      filtered = filtered.filter(emp => emp.employmentStatus === filterStatus)
    }

    return filtered
  }, [employees, filterStatus])

  // Find selected employee
  const selectedEmployee = filteredEmployees.find(emp => emp._id === value)

  // Handle search change
  const handleSearchChange = (search: string) => {
    setSearchValue(search)
    onSearchChange?.(search)
  }

  // Get employee display name
  const getEmployeeDisplayName = (employee: Employee) => {
    return `${employee.firstName} ${employee.lastName}`
  }

  // Get employee initials
  const getEmployeeInitials = (employee: Employee) => {
    return `${employee.firstName.charAt(0)}${employee.lastName.charAt(0)}`
  }

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'terminated':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-full justify-between", className)}
          disabled={disabled}
        >
          {selectedEmployee ? (
            <div className="flex items-center gap-2 flex-1 min-w-0">
              <Avatar className="h-6 w-6">
                <AvatarImage src={selectedEmployee.profilePicture} />
                <AvatarFallback className="text-xs">
                  {getEmployeeInitials(selectedEmployee)}
                </AvatarFallback>
              </Avatar>
              <div className="flex flex-col items-start min-w-0 flex-1">
                <span className="font-medium truncate">
                  {getEmployeeDisplayName(selectedEmployee)}
                </span>
                {(showEmployeeId || showDepartment) && (
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    {showEmployeeId && selectedEmployee.employeeId && (
                      <span>ID: {selectedEmployee.employeeId}</span>
                    )}
                    {showDepartment && selectedEmployee.departmentId && (
                      <span>{selectedEmployee.departmentId.name}</span>
                    )}
                  </div>
                )}
              </div>
              {showStatus && (
                <Badge variant="outline" className={cn("text-xs", getStatusColor(selectedEmployee.employmentStatus))}>
                  {selectedEmployee.employmentStatus}
                </Badge>
              )}
            </div>
          ) : (
            <div className="flex items-center gap-2 text-muted-foreground">
              <User className="h-4 w-4" />
              <span>{placeholder}</span>
            </div>
          )}
          {loading ? (
            <Loader2 className="ml-2 h-4 w-4 shrink-0 animate-spin" />
          ) : (
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[400px] p-0" align="start">
        <Command shouldFilter={false}>
          <div className="flex items-center border-b px-3">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <CommandInput
              placeholder="Search employees..."
              value={searchValue}
              onValueChange={handleSearchChange}
              className="flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
            />
          </div>
          <CommandList>
            <ScrollArea className="h-[300px]">
              {loading ? (
                <div className="flex items-center justify-center py-6">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="ml-2 text-sm text-muted-foreground">Loading employees...</span>
                </div>
              ) : filteredEmployees.length === 0 ? (
                <CommandEmpty className="py-6 text-center text-sm">
                  <Users className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
                  {emptyText}
                </CommandEmpty>
              ) : (
                <CommandGroup>
                  {filteredEmployees.map((employee) => (
                    <CommandItem
                      key={employee._id}
                      value={employee._id}
                      onSelect={(currentValue) => {
                        onValueChange?.(currentValue === value ? "" : currentValue)
                        setOpen(false)
                      }}
                      className="flex items-center gap-3 p-3"
                    >
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={employee.profilePicture} />
                        <AvatarFallback className="text-xs">
                          {getEmployeeInitials(employee)}
                        </AvatarFallback>
                      </Avatar>
                      
                      <div className="flex flex-col flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="font-medium truncate">
                            {getEmployeeDisplayName(employee)}
                          </span>
                          {showStatus && (
                            <Badge variant="outline" className={cn("text-xs", getStatusColor(employee.employmentStatus))}>
                              {employee.employmentStatus}
                            </Badge>
                          )}
                        </div>
                        
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          {showEmployeeId && employee.employeeId && (
                            <span>ID: {employee.employeeId}</span>
                          )}
                          {employee.position && (
                            <span>{employee.position}</span>
                          )}
                          {showDepartment && employee.departmentId && (
                            <span>• {employee.departmentId.name}</span>
                          )}
                        </div>
                        
                        <div className="text-xs text-muted-foreground truncate">
                          {employee.email}
                        </div>
                      </div>
                      
                      <Check
                        className={cn(
                          "ml-auto h-4 w-4",
                          value === employee._id ? "opacity-100" : "opacity-0"
                        )}
                      />
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
            </ScrollArea>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
