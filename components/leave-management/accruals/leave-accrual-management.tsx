"use client"

import { useState } from "react"
import { format } from "date-fns"
import { Calendar as CalendarIcon, Play, CheckCircle, AlertCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"
import { useLeaveAccruals } from "@/hooks/use-leave-accruals"

interface AccrualResult {
  employeeId: string;
  leaveTypeId: string;
  accruedDays: number;
  accrualPeriod: string;
  reason: string;
  eligible: boolean;
  errors?: string[];
}

export function LeaveAccrualManagement() {
  const [accrualType, setAccrualType] = useState<'monthly' | 'quarterly' | 'annually'>('monthly')
  const [processingDate, setProcessingDate] = useState<Date>(new Date())
  const [results, setResults] = useState<any>(null)
  
  const {
    loading,
    error,
    processMonthlyAccruals,
    processQuarterlyAccruals,
    processAnnualAccruals,
  } = useLeaveAccruals()

  const handleProcessAccruals = async () => {
    let result = null;
    
    switch (accrualType) {
      case 'monthly':
        result = await processMonthlyAccruals(processingDate);
        break;
      case 'quarterly':
        result = await processQuarterlyAccruals(processingDate);
        break;
      case 'annually':
        result = await processAnnualAccruals(processingDate);
        break;
    }
    
    if (result) {
      setResults(result);
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Leave Accrual Processing</CardTitle>
          <CardDescription>
            Process automatic leave accruals for employees based on configured rules
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Accrual Type</label>
              <Select value={accrualType} onValueChange={(value: any) => setAccrualType(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select accrual type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="monthly">Monthly Accrual</SelectItem>
                  <SelectItem value="quarterly">Quarterly Accrual</SelectItem>
                  <SelectItem value="annually">Annual Accrual</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Processing Date</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !processingDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {processingDate ? format(processingDate, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={processingDate}
                    onSelect={(date) => date && setProcessingDate(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="flex justify-end">
            <Button 
              onClick={handleProcessAccruals} 
              disabled={loading}
              className="min-w-[120px]"
            >
              {loading ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-foreground" />
                  Processing...
                </>
              ) : (
                <>
                  <Play className="mr-2 h-4 w-4" />
                  Process Accruals
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {results && (
        <Card>
          <CardHeader>
            <CardTitle>Processing Results</CardTitle>
            <CardDescription>
              Results from the latest accrual processing run
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{results.totalProcessed}</div>
                <div className="text-sm text-muted-foreground">Processed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{results.totalEligible}</div>
                <div className="text-sm text-muted-foreground">Eligible</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{results.totalErrors}</div>
                <div className="text-sm text-muted-foreground">Errors</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-600">{results.results?.length || 0}</div>
                <div className="text-sm text-muted-foreground">Total Records</div>
              </div>
            </div>

            <Separator />

            {results.errors && results.errors.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium text-red-600">Errors</h4>
                <div className="space-y-1">
                  {results.errors.map((error: string, index: number) => (
                    <Alert key={index} variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  ))}
                </div>
              </div>
            )}

            {results.results && results.results.length > 0 && (
              <div className="space-y-4">
                <h4 className="font-medium">Accrual Details</h4>
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {results.results.map((result: AccrualResult, index: number) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        {result.eligible ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : (
                          <AlertCircle className="h-4 w-4 text-yellow-600" />
                        )}
                        <div>
                          <div className="font-medium">Employee: {result.employeeId}</div>
                          <div className="text-sm text-muted-foreground">{result.reason}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge variant={result.eligible ? "default" : "secondary"}>
                          {result.accruedDays} days
                        </Badge>
                        <div className="text-xs text-muted-foreground mt-1">
                          {result.accrualPeriod}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
