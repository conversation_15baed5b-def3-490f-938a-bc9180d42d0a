"use client"

import React, { useState } from 'react';
import { LeaveTypesList } from '@/components/leave-types/leave-types-list';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Separator } from '@/components/ui/separator';
import { Settings, Database, Plus, Info, Loader2 } from 'lucide-react';

interface LeaveTypesPageProps {
  userId: string;
}

export function LeaveTypesPage({ userId }: LeaveTypesPageProps) {
  const { toast } = useToast();
  const [isSeeding, setIsSeeding] = useState(false);

  const handleSeedLeaveTypes = async () => {
    setIsSeeding(true);
    try {
      const response = await fetch('/api/admin/seed/leave-types', {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to seed leave types');
      }

      const result = await response.json();
      toast({
        title: "Success",
        description: result.message || "Leave types seeded successfully",
      });

      // Refresh the page to show new leave types
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to seed leave types",
        variant: "destructive",
      });
    } finally {
      setIsSeeding(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div>
          <p className="text-muted-foreground">
            Configure and manage different types of leave available to employees
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleSeedLeaveTypes}
            disabled={isSeeding}
          >
            {isSeeding ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Seeding...
              </>
            ) : (
              <>
                <Database className="h-4 w-4 mr-2" />
                Seed Default Types
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Info Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Leave Types</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Configure</div>
            <p className="text-xs text-muted-foreground">
              Set up different leave categories with specific rules and entitlements
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Entitlements</CardTitle>
            <Plus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Allocate</div>
            <p className="text-xs text-muted-foreground">
              Define default days, carry-over rules, and approval requirements
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Policies</CardTitle>
            <Info className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Enforce</div>
            <p className="text-xs text-muted-foreground">
              Set notice periods, consecutive day limits, and approval workflows
            </p>
          </CardContent>
        </Card>
      </div>
      {/* Common Leave Types Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            Common Leave Types
          </CardTitle>
          <CardDescription>
            Standard leave types that are commonly used in organizations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                <span className="font-medium">Annual Leave</span>
                <Badge variant="outline">Paid</Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                Yearly vacation leave for rest and recreation
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <span className="font-medium">Sick Leave</span>
                <Badge variant="outline">Paid</Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                Medical leave for illness or medical appointments
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-pink-500"></div>
                <span className="font-medium">Maternity Leave</span>
                <Badge variant="outline">Paid</Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                Leave for mothers before and after childbirth
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-cyan-500"></div>
                <span className="font-medium">Paternity Leave</span>
                <Badge variant="outline">Paid</Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                Leave for fathers after childbirth or adoption
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-amber-500"></div>
                <span className="font-medium">Emergency Leave</span>
                <Badge variant="secondary">Unpaid</Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                Urgent leave for family emergencies
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-gray-500"></div>
                <span className="font-medium">Bereavement Leave</span>
                <Badge variant="outline">Paid</Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                Leave for mourning the death of a family member
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Leave Types List */}
      <LeaveTypesList />
    </div>
  );
}
