// components\leave-management\leave-calendar.tsx
"use client"

import { cn } from "@/lib/utils"

import { useState, useMemo } from "react"
import { Calendar } from "@/components/ui/calendar"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import { useLeaveRequests } from "@/hooks/use-leave-requests"
import { format } from "date-fns"
import type { ILeaveRequestDB } from "@/types/leave-request"

export function LeaveCalendar() {
  const [month, setMonth] = useState<Date>(new Date())

  const {
    requests,
    loading,
    error,
  } = useLeaveRequests({
    initialParams: { page: 1, limit: 100, status: 'approved' },
    autoFetch: true
  });

  // Function to get all dates between start and end dates
  const getDatesInRange = (startDate: string, endDate: string) => {
    const start = new Date(startDate)
    const end = new Date(endDate)
    const dates = []
    const currentDate = new Date(start)

    while (currentDate <= end) {
      dates.push(new Date(currentDate))
      currentDate.setDate(currentDate.getDate() + 1)
    }

    return dates
  }

  // Get all leave dates from approved requests
  const leaveDates = useMemo(() => {
    return requests
      .filter((request: ILeaveRequestDB) => request.status === "approved")
      .flatMap((request: ILeaveRequestDB) => {
        const startDate = format(new Date(request.startDate), 'yyyy-MM-dd')
        const endDate = format(new Date(request.endDate), 'yyyy-MM-dd')
        const dates = getDatesInRange(startDate, endDate)

        const employeeName = `${(request.employeeId as any)?.firstName || ''} ${(request.employeeId as any)?.lastName || ''}`.trim() || 'Unknown Employee'
        const leaveTypeName = (request.leaveTypeId as any)?.name || 'Unknown Type'

        return dates.map((date) => ({
          date,
          type: leaveTypeName,
          employee: employeeName,
        }))
      })
  }, [requests])

  // Function to render the day content with badges for leave
  const renderDayContent = (day: Date) => {
    const matchingLeaves = leaveDates.filter((leave) => leave.date.toDateString() === day.toDateString())

    if (matchingLeaves.length === 0) return null

    // Group leaves by type
    const leavesByType = matchingLeaves.reduce(
      (acc, leave) => {
        acc[leave.type] = (acc[leave.type] || 0) + 1
        return acc
      },
      {} as Record<string, number>,
    )

    return (
      <div className="absolute bottom-0 left-0 right-0 flex flex-wrap gap-0.5 p-0.5">
        {Object.entries(leavesByType).map(([type, count]) => (
          <Badge
            key={type}
            variant="outline"
            className={cn(
              "h-1.5 w-1.5 rounded-full p-0 border-0",
              type === "Annual Leave" && "bg-primary",
              type === "Sick Leave" && "bg-amber-500",
              type === "Personal Leave" && "bg-purple-500",
              type === "Parental Leave" && "bg-green-500",
            )}
            title={`${type}: ${count} employee${count > 1 ? 's' : ''}`}
          />
        ))}
      </div>
    )
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-64 w-full" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent className="p-6">
        <Calendar
          mode="single"
          month={month}
          onMonthChange={setMonth}
          className="rounded-md border"
          components={{
            Day: ({ day, ...props }) => (
              <div {...props} className={cn(props.className, "relative")}>
                <span>{day.date.getDate()}</span>
                {renderDayContent(day.date)}
              </div>
            ),
          }}
        />
        <div className="mt-4 flex flex-wrap gap-4">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="h-3 w-3 rounded-full bg-primary p-0" />
            <span className="text-sm">Annual Leave</span>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="h-3 w-3 rounded-full bg-amber-500 p-0" />
            <span className="text-sm">Sick Leave</span>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="h-3 w-3 rounded-full bg-purple-500 p-0" />
            <span className="text-sm">Personal Leave</span>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="h-3 w-3 rounded-full bg-green-500 p-0" />
            <span className="text-sm">Parental Leave</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
