/**
 * Leave Types Status Component
 * 
 * Shows the current status of leave types in the system
 */

"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Loader2, 
  RefreshCw,
  Database,
  Plus
} from 'lucide-react';
import { useLeaveTypes } from '@/hooks/use-leave-types';
import { useToast } from '@/components/ui/use-toast';

export function LeaveTypesStatus() {
  const { leaveTypes, loading, error, refreshLeaveTypes } = useLeaveTypes({
    activeOnly: false,
    autoFetch: true
  });
  const { toast } = useToast();

  const handleRefresh = async () => {
    try {
      await refreshLeaveTypes();
      toast({
        title: "Success",
        description: "Leave types refreshed successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to refresh leave types",
        variant: "destructive",
      });
    }
  };

  const handleSeedLeaveTypes = async () => {
    try {
      const response = await fetch('/api/admin/seed/leave-types', {
        method: 'POST',
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to seed leave types');
      }

      toast({
        title: "Success",
        description: result.message,
      });

      // Refresh the leave types list
      await refreshLeaveTypes();
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to seed leave types',
        variant: "destructive",
      });
    }
  };

  const activeLeaveTypes = leaveTypes.filter(lt => lt.isActive);
  const inactiveLeaveTypes = leaveTypes.filter(lt => !lt.isActive);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Leave Types Status
            </CardTitle>
            <CardDescription>
              Current status of leave types in the system
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleSeedLeaveTypes}
              disabled={loading}
            >
              <Plus className="h-4 w-4 mr-2" />
              Seed Default Types
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Status Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-2">
            {loading ? (
              <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
            ) : error ? (
              <XCircle className="h-4 w-4 text-red-500" />
            ) : leaveTypes.length > 0 ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
            )}
            <span className="text-sm font-medium">
              {loading ? 'Loading...' : error ? 'Error' : leaveTypes.length > 0 ? 'Connected' : 'No Data'}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">Total Types:</span>
            <Badge variant="secondary">{leaveTypes.length}</Badge>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">Active Types:</span>
            <Badge variant="default">{activeLeaveTypes.length}</Badge>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertDescription>
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* No Data Warning */}
        {!loading && !error && leaveTypes.length === 0 && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              No leave types found. Click "Seed Default Types" to create the default leave types.
            </AlertDescription>
          </Alert>
        )}

        {/* Leave Types List */}
        {!loading && !error && leaveTypes.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Available Leave Types:</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {leaveTypes.map((leaveType) => (
                <div
                  key={leaveType._id.toString()}
                  className="flex items-center justify-between p-2 border rounded-lg"
                >
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: leaveType.color }}
                    />
                    <span className="text-sm font-medium">{leaveType.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {leaveType.code}
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Badge variant={leaveType.isActive ? "default" : "secondary"} className="text-xs">
                      {leaveType.isActive ? "Active" : "Inactive"}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {leaveType.defaultDays} days
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* API Status */}
        <div className="pt-4 border-t">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>API Endpoint: /api/leave/types</span>
            <span>
              {loading ? 'Fetching...' : error ? 'Error' : `Last updated: ${new Date().toLocaleTimeString()}`}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
