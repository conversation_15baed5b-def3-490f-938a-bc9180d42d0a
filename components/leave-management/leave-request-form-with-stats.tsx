"use client"

import React from 'react';
import { LeaveRequestForm } from './leave-request-form';
import { EmployeeLeaveStats } from './employee-leave-stats';
import { useEmployees, Employee } from '@/hooks/use-employees';
import { useEmployeeLeaveBalances } from '@/hooks/use-employee-leave-balances';

interface LeaveRequestFormWithStatsProps {
  employeeId?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
  className?: string;
}

export function LeaveRequestFormWithStats({ 
  employeeId, 
  onSuccess, 
  onCancel, 
  className 
}: LeaveRequestFormWithStatsProps) {
  const [selectedEmployee, setSelectedEmployee] = React.useState<Employee | null>(null);
  const { getEmployeeById } = useEmployees({ autoFetch: true, status: 'active' });
  const { balances, loading: balancesLoading, fetchBalances } = useEmployeeLeaveBalances();

  // Handle employee selection from the form
  const handleEmployeeChange = React.useCallback((newEmployeeId: string) => {
    if (newEmployeeId) {
      const employee = getEmployeeById(newEmployeeId);
      setSelectedEmployee(employee || null);
      if (employee) {
        fetchBalances(newEmployeeId);
      }
    } else {
      setSelectedEmployee(null);
    }
  }, [getEmployeeById, fetchBalances]);

  // Pre-select employee if provided via props
  React.useEffect(() => {
    if (employeeId) {
      const employee = getEmployeeById(employeeId);
      if (employee) {
        setSelectedEmployee(employee);
        fetchBalances(employeeId);
      }
    }
  }, [employeeId, getEmployeeById, fetchBalances]);

  return (
    <div className={`grid grid-cols-1 lg:grid-cols-3 gap-6 ${className}`}>
      {/* Main Form */}
      <div className="lg:col-span-2">
        <LeaveRequestForm
          employeeId={employeeId}
          onSuccess={onSuccess}
          onCancel={onCancel}
          onEmployeeChange={handleEmployeeChange}
        />
      </div>

      {/* Employee Stats Sidebar */}
      <div className="lg:col-span-1">
        <EmployeeLeaveStats
          employee={selectedEmployee}
          leaveBalances={balances}
          loading={balancesLoading}
        />
      </div>
    </div>
  );
}
