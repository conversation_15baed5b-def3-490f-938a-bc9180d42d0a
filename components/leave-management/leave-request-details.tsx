import { useState } from "react"
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { CalendarDays, Clock, FileText, MessageSquare, CheckCircle2, XCircle, Loader2 } from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import type { LeaveRequest } from "@/types/leave-request"

interface LeaveRequestDetailsProps {
  request: LeaveRequest
  onApprove?: (id: string) => Promise<void>
  onReject?: (id: string, reason: string) => Promise<void>
  isLoading?: boolean
}

export function LeaveRequestDetails({ request, onApprove, onReject, isLoading = false }: LeaveRequestDetailsProps) {
  const [rejectionReason, setRejectionReason] = useState("")
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)

  const handleApprove = async () => {
    if (!onApprove) return

    try {
      setIsProcessing(true)
      await onApprove(request.id)
    } catch (error) {
      console.error('Error approving request:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleReject = async () => {
    if (!onReject || !rejectionReason.trim()) return

    try {
      setIsProcessing(true)
      await onReject(request.id, rejectionReason.trim())
      setIsRejectDialogOpen(false)
      setRejectionReason("")
    } catch (error) {
      console.error('Error rejecting request:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  const isActionDisabled = isLoading || isProcessing

  return (
    <Card>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-4">
            <Avatar className="h-12 w-12 border border-primary/10">
              <AvatarImage src={request.employee.avatar || "/placeholder.svg"} alt={request.employee.name} />
              <AvatarFallback>{request.employee.name.charAt(0)}</AvatarFallback>
            </Avatar>
            <div>
              <CardTitle>{request.employee.name}</CardTitle>
              <CardDescription>{request.employee.position}</CardDescription>
            </div>
          </div>
          <Badge
            variant={
              request.status === "approved" ? "default" : request.status === "rejected" ? "destructive" : "outline"
            }
            className={
              request.status === "approved" ? "bg-green-500" : request.status === "rejected" ? "bg-red-500" : ""
            }
          >
            {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid gap-6 md:grid-cols-2">
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Leave Details</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <FileText className="h-4 w-4 text-muted-foreground" />
                <span>Type: {request.type}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <CalendarDays className="h-4 w-4 text-muted-foreground" />
                <span>
                  Duration: {request.startDate} to {request.endDate}
                </span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span>Days: {request.days}</span>
              </div>
            </div>
          </div>
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Request Information</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <CalendarDays className="h-4 w-4 text-muted-foreground" />
                <span>Requested on: {request.requestDate}</span>
              </div>
              {request.reviewedBy && (
                <div className="flex items-center gap-2 text-sm">
                  <Avatar className="h-4 w-4">
                    <AvatarImage src={request.reviewedBy.avatar || "/placeholder.svg"} alt={request.reviewedBy.name} />
                    <AvatarFallback>{request.reviewedBy.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <span>
                    Reviewed by: {request.reviewedBy.name} on {request.reviewDate}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <h3 className="text-sm font-medium">Reason</h3>
          <div className="rounded-md border p-4">
            <p className="text-sm">{request.reason}</p>
          </div>
        </div>

        {request.comments && (
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Comments</h3>
            <div className="rounded-md border p-4">
              <p className="text-sm">{request.comments}</p>
            </div>
          </div>
        )}

        {request.status === "pending" && onApprove && onReject && (
          <div className="flex justify-end gap-2">
            {/* Reject Button with Dialog */}
            <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1"
                  disabled={isActionDisabled}
                >
                  {isProcessing ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                  <span>Reject</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Reject Leave Request</DialogTitle>
                  <DialogDescription>
                    Please provide a reason for rejecting this leave request. This will be communicated to the employee.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid gap-2">
                    <Label htmlFor="reason">Rejection Reason</Label>
                    <Textarea
                      id="reason"
                      placeholder="Enter the reason for rejection..."
                      value={rejectionReason}
                      onChange={(e) => setRejectionReason(e.target.value)}
                      className="min-h-[100px]"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsRejectDialogOpen(false)
                      setRejectionReason("")
                    }}
                    disabled={isProcessing}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={handleReject}
                    disabled={!rejectionReason.trim() || isProcessing}
                  >
                    {isProcessing ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Rejecting...
                      </>
                    ) : (
                      <>
                        <XCircle className="mr-2 h-4 w-4" />
                        Reject Request
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {/* Approve Button with Confirmation */}
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  size="sm"
                  className="gap-1"
                  disabled={isActionDisabled}
                >
                  {isProcessing ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <CheckCircle2 className="h-4 w-4" />
                  )}
                  <span>Approve</span>
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Approve Leave Request</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to approve this leave request for{" "}
                    <strong>{request.employee.name}</strong> from{" "}
                    <strong>{new Date(request.startDate).toLocaleDateString()}</strong> to{" "}
                    <strong>{new Date(request.endDate).toLocaleDateString()}</strong>?
                    <br /><br />
                    This action cannot be undone and will update the employee's leave balance.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel disabled={isProcessing}>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleApprove}
                    disabled={isProcessing}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {isProcessing ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Approving...
                      </>
                    ) : (
                      <>
                        <CheckCircle2 className="mr-2 h-4 w-4" />
                        Approve Request
                      </>
                    )}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        )}
      </CardContent>
      <CardFooter className="border-t bg-muted/50 px-6 py-4">
        <div className="flex w-full items-center justify-between">
          <div className="text-sm text-muted-foreground">Request ID: {request.id}</div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <MessageSquare className="mr-2 h-4 w-4" />
              Add Comment
            </Button>
          </div>
        </div>
      </CardFooter>
    </Card>
  )
}
