// components\leave-management\leave-request-form.tsx
"use client"

import { useState, useEffect } from "react"
import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { format, differenceInBusinessDays, addDays } from "date-fns"
import { Calendar as CalendarIcon, Loader2 } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"
import { useLeaveTypes } from "@/hooks/use-leave-types"
import { useLeaveRequests } from "@/hooks/use-leave-requests"
import { ILeaveType, CreateLeaveRequestPayload } from "@/types/leave-request"

interface LeaveRequestFormProps {
  employeeId?: string
  onSuccess?: () => void
  onCancel?: () => void
  className?: string
}

const formSchema = z.object({
  leaveTypeId: z.string({
    required_error: "Please select a leave type",
  }),
  startDate: z.date({
    required_error: "Please select a start date",
  }),
  endDate: z.date({
    required_error: "Please select an end date",
  }).refine(date => date instanceof Date, {
    message: "Please select an end date",
  }),
  reason: z.string().min(5, {
    message: "Reason must be at least 5 characters",
  }).max(500, {
    message: "Reason must not exceed 500 characters",
  }),
  attachments: z.array(z.string()).optional(),
  notes: z.string().max(500, {
    message: "Notes must not exceed 500 characters",
  }).optional(),
})

export function LeaveRequestForm({ employeeId, onSuccess, onCancel, className }: LeaveRequestFormProps) {
  const [selectedLeaveType, setSelectedLeaveType] = useState<ILeaveType | null>(null)
  const [duration, setDuration] = useState<number | null>(null)
  const { toast } = useToast()

  const { leaveTypes, loading } = useLeaveTypes({
    activeOnly: true,
    autoFetch: true
  });

  const { createRequest, loading: submitting } = useLeaveRequests({ autoFetch: false });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      reason: "",
      notes: "",
      attachments: [],
    },
  })

  const { startDate, endDate } = form.watch()

  useEffect(() => {
    if (startDate && endDate) {
      // Calculate business days between start and end dates (inclusive)
      const days = differenceInBusinessDays(addDays(endDate, 1), startDate)
      setDuration(days > 0 ? days : 0)
    } else {
      setDuration(null)
    }
  }, [startDate, endDate])

  useEffect(() => {
    const leaveTypeId = form.watch('leaveTypeId')
    if (leaveTypeId) {
      const leaveType = leaveTypes.find(lt => lt._id.toString() === leaveTypeId)
      setSelectedLeaveType(leaveType || null)
    } else {
      setSelectedLeaveType(null)
    }
  }, [form.watch('leaveTypeId'), leaveTypes])

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    const payload: CreateLeaveRequestPayload = {
      leaveTypeId: values.leaveTypeId,
      startDate: format(values.startDate, 'yyyy-MM-dd'),
      endDate: format(values.endDate, 'yyyy-MM-dd'),
      reason: values.reason,
      notes: values.notes || undefined,
      attachments: values.attachments?.length ? values.attachments : undefined,
    };

    const result = await createRequest(payload);

    if (result) {
      if (onSuccess) {
        onSuccess();
      }
      form.reset();
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Request Leave</CardTitle>
        <CardDescription>Submit a new leave request</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="leaveTypeId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Leave Type</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={loading || submitting}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a leave type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {loading ? (
                        <div className="flex items-center justify-center py-2">
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          <span>Loading...</span>
                        </div>
                      ) : (
                        leaveTypes.map((leaveType) => (
                          <SelectItem
                            key={leaveType._id.toString()}
                            value={leaveType._id.toString()}
                          >
                            {leaveType.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  {selectedLeaveType && (
                    <FormDescription>
                      {selectedLeaveType.isPaid ? "Paid leave" : "Unpaid leave"}
                      {selectedLeaveType.maxConsecutiveDays > 0 &&
                        ` • Max ${selectedLeaveType.maxConsecutiveDays} consecutive days`}
                      {selectedLeaveType.minNoticeInDays > 0 &&
                        ` • ${selectedLeaveType.minNoticeInDays} days notice required`}
                    </FormDescription>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Start Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                            disabled={submitting}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < new Date(new Date().setHours(0, 0, 0, 0)) ||
                            date.getDay() === 0 ||
                            date.getDay() === 6
                          }
                        />
                      </PopoverContent>
                    </Popover>
                    <FormDescription>
                      Select the first day of your leave
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>End Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                            disabled={!startDate || submitting}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => {
                            if (startDate && date < startDate) return true
                            if (date.getDay() === 0 || date.getDay() === 6) return true
                            if (selectedLeaveType?.maxConsecutiveDays && startDate &&
                                differenceInBusinessDays(addDays(date, 1), startDate) > selectedLeaveType.maxConsecutiveDays) {
                              return true
                            }
                            return false
                          }}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormDescription>
                      Select the last day of your leave
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {duration !== null && (
              <div className="bg-muted p-3 rounded-md text-sm">
                Duration: <strong>{duration} working day{duration !== 1 ? 's' : ''}</strong>
              </div>
            )}

            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Reason</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Please provide a reason for your leave request"
                      className="resize-none"
                      {...field}
                      disabled={submitting}
                    />
                  </FormControl>
                  <FormDescription>
                    Briefly explain why you are requesting leave
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Any additional information"
                      className="resize-none"
                      {...field}
                      disabled={submitting}
                    />
                  </FormControl>
                  <FormDescription>
                    Any additional information that might be relevant
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* File upload functionality would go here */}
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={onCancel}
          disabled={submitting}
        >
          Cancel
        </Button>
        <Button
          onClick={form.handleSubmit(onSubmit)}
          disabled={submitting}
        >
          {submitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Submitting...
            </>
          ) : (
            "Submit Request"
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}
