"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import { cn } from "@/lib/utils"
import { useLeaveBalances } from "@/hooks/use-leave-balances"

interface LeaveBalancesProps {
  className?: string
  year?: number
}

export function LeaveBalances({ className, year }: LeaveBalancesProps) {
  const { balances, loading, error } = useLeaveBalances({
    year,
    autoFetch: true
  });

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i} className={cn("overflow-hidden", className)}>
            <CardContent className="p-6">
              <div className="flex flex-col gap-3">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-16" />
                </div>
                <Skeleton className="h-2 w-full" />
                <Skeleton className="h-3 w-32" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (balances.length === 0) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>No leave balances found. Contact HR to set up your leave entitlements.</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {balances.map((balance) => {
        const usagePercentage = balance.totalDays > 0 ? (balance.usedDays / balance.totalDays) * 100 : 0;
        const remainingPercentage = balance.totalDays > 0 ? (balance.remainingDays / balance.totalDays) * 100 : 0;

        return (
          <Card key={balance.leaveType._id} className={cn("overflow-hidden", className)}>
            <CardContent className="p-6">
              <div className="flex flex-col gap-3">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">{balance.leaveType.name}</h3>
                  <span className="text-sm text-muted-foreground">
                    {balance.usedDays} / {balance.totalDays} days
                  </span>
                </div>
                <div className="space-y-1">
                  <Progress
                    value={usagePercentage}
                    className="h-2"
                    style={{
                      '--progress-background': balance.leaveType.color || '#4CAF50'
                    } as React.CSSProperties}
                  />
                  {balance.pendingDays > 0 && (
                    <div className="text-xs text-amber-600">
                      {balance.pendingDays} days pending approval
                    </div>
                  )}
                </div>
                <div className="space-y-1">
                  <p className="text-xs text-muted-foreground">
                    {balance.remainingDays} days remaining ({Math.round(remainingPercentage)}%)
                  </p>
                  {balance.carryOverDays > 0 && (
                    <p className="text-xs text-blue-600">
                      Includes {balance.carryOverDays} carried over days
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  )
}
