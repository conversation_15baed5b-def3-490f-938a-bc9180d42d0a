"use client"

import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Calendar, Clock, User, Building, Mail, Phone, MapPin, Briefcase } from 'lucide-react';
import { Employee } from '@/hooks/use-employees';
import { format } from 'date-fns';

interface LeaveBalance {
  _id?: string;
  leaveType?: {
    _id: string;
    name: string;
    code: string;
    color: string;
  };
  leaveTypeId?: {
    _id: string;
    name: string;
    color?: string;
    isPaid: boolean;
  } | string;
  totalDays: number;
  usedDays: number;
  remainingDays: number;
  carryOverDays?: number;
  pendingDays?: number;
  year?: number;
}

interface EmployeeLeaveStatsProps {
  employee: Employee | null;
  leaveBalances?: LeaveBalance[];
  loading?: boolean;
  className?: string;
}

export function EmployeeLeaveStats({ 
  employee, 
  leaveBalances = [], 
  loading = false, 
  className 
}: EmployeeLeaveStatsProps) {
  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Employee Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!employee) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Employee Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <User className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              Select an employee to view their information and leave balances
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getEmployeeInitials = (emp: Employee) => {
    return `${emp.firstName.charAt(0)}${emp.lastName.charAt(0)}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'terminated':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const totalLeaveBalance = leaveBalances.reduce((sum, balance) => sum + balance.remainingDays, 0);
  const totalUsedDays = leaveBalances.reduce((sum, balance) => sum + balance.usedDays, 0);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Employee Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Employee Basic Info */}
        <div className="flex items-start gap-4">
          <Avatar className="h-16 w-16">
            <AvatarImage src={employee.profilePicture} />
            <AvatarFallback className="text-lg">
              {getEmployeeInitials(employee)}
            </AvatarFallback>
          </Avatar>
          
          <div className="flex-1 space-y-2">
            <div>
              <h3 className="text-lg font-semibold">
                {employee.firstName} {employee.lastName}
              </h3>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline" className={getStatusColor(employee.employmentStatus)}>
                  {employee.employmentStatus}
                </Badge>
                {employee.employeeId && (
                  <Badge variant="outline">
                    ID: {employee.employeeId}
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Employee Details */}
        <div className="grid grid-cols-1 gap-3 text-sm">
          {employee.position && (
            <div className="flex items-center gap-2">
              <Briefcase className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">Position:</span>
              <span className="font-medium">{employee.position}</span>
            </div>
          )}
          
          {employee.departmentId && (
            <div className="flex items-center gap-2">
              <Building className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">Department:</span>
              <span className="font-medium">{employee.departmentId.name}</span>
            </div>
          )}
          
          <div className="flex items-center gap-2">
            <Mail className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">Email:</span>
            <span className="font-medium text-xs">{employee.email}</span>
          </div>
          
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">Hire Date:</span>
            <span className="font-medium">{format(new Date(employee.hireDate), 'MMM dd, yyyy')}</span>
          </div>
        </div>

        <Separator />

        {/* Leave Balance Summary */}
        <div className="space-y-4">
          <h4 className="font-medium flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Leave Balance Summary
          </h4>
          
          {leaveBalances.length > 0 ? (
            <>
              {/* Overall Stats */}
              <div className="grid grid-cols-2 gap-4 p-3 bg-muted rounded-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{totalLeaveBalance}</div>
                  <div className="text-xs text-muted-foreground">Days Available</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{totalUsedDays}</div>
                  <div className="text-xs text-muted-foreground">Days Used</div>
                </div>
              </div>

              {/* Individual Leave Types */}
              <div className="space-y-2">
                {leaveBalances.map((balance, index) => {
                  // Handle multiple data structures
                  let leaveTypeName = 'Unknown Leave Type';
                  let leaveTypeColor = '#6B7280';
                  let isPaid = false;
                  let balanceId = balance._id || `balance-${index}`;

                  if (balance.leaveType) {
                    // New structure from LeaveService
                    leaveTypeName = balance.leaveType.name;
                    leaveTypeColor = balance.leaveType.color || '#6B7280';
                    isPaid = true; // Default assumption for new structure
                  } else if (balance.leaveTypeId && typeof balance.leaveTypeId === 'object') {
                    // Old structure with populated leaveTypeId
                    leaveTypeName = balance.leaveTypeId.name;
                    leaveTypeColor = balance.leaveTypeId.color || '#6B7280';
                    isPaid = balance.leaveTypeId.isPaid || false;
                  }

                  return (
                    <div key={balanceId} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex items-center gap-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: leaveTypeColor }}
                        />
                        <span className="text-sm font-medium">{leaveTypeName}</span>
                        {isPaid && (
                          <Badge variant="outline" className="text-xs">Paid</Badge>
                        )}
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">
                          {balance.remainingDays}/{balance.totalDays}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {balance.usedDays} used
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </>
          ) : (
            <div className="text-center py-4 text-muted-foreground">
              <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No leave balance information available</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
