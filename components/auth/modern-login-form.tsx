"use client";

import { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '../../lib/frontend/hooks/useAuth';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Alert, AlertDescription } from '../ui/alert';
import { Eye, EyeOff} from 'lucide-react';
import { LoginOverlay } from './login-overlay';
import { DeviceVerification } from './device-verification';
// import { Logo } from '../ui/logo';
// import { SystemModulesShowcase } from './system-modules-showcase';
import { AuthLogo } from '../ui/auth-logo';
import { AuthBackground } from './auth-background';

export function ModernLoginForm() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showOverlay, setShowOverlay] = useState(false);
  const [serverError, setServerError] = useState('');
  const [showDeviceVerification, setShowDeviceVerification] = useState(false);
  const [deviceVerificationData, setDeviceVerificationData] = useState<{
    sessionId: string;
    deviceInfo: {
      deviceName: string;
      browser: string;
      operatingSystem: string;
      ipAddress: string;
      location?: {
        country?: string;
        region?: string;
        city?: string;
      };
    };
  } | null>(null);
  const { login, error, resetAuthError } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setShowOverlay(true);
    resetAuthError();

    try {
      // Create the request payload
      const credentials = {
        email,
        password
      };

      console.log('Sending login data:', { email, password: '********' });

      // Make direct API call to get device verification info if needed
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      const data = await response.json();

      if (!response.ok) {
        // Check if this is a single device login restriction error
        if (data.error && data.error.includes('already logged in on another device')) {
          throw new Error(
            'This account is already logged in on another device. ' +
            'Please log out from the other device before logging in here.'
          );
        }
        throw new Error(data.error || 'Login failed');
      }

      // Check if device verification is required
      if (data.data.requiresDeviceVerification) {
        console.log('Device verification required');
        setShowOverlay(false);

        // Get session from the response
        const sessionId = data.data.sessionId;

        // Show device verification dialog
        setDeviceVerificationData({
          sessionId,
          deviceInfo: {
            deviceName: 'New Device', // This would come from the backend in a real implementation
            browser: 'Web Browser',
            operatingSystem: 'Unknown OS',
            ipAddress: '0.0.0.0',
            location: {}
          }
        });
        setShowDeviceVerification(true);
      } else {
        console.log('Login successful, redirecting to dashboard');

        // Use the login function from useAuth hook to update the frontend state
        await login(credentials);

        // Keep the overlay visible during redirection
        // The loading page will handle the actual redirection
        window.location.href = '/loading';
      }
    } catch (error: unknown) {
      console.error('Login error:', error);
      // Set the error message
      resetAuthError();
      setServerError(error instanceof Error ? error.message : 'An error occurred during login');
      setShowOverlay(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeviceVerified = async () => {
    setShowDeviceVerification(false);
    setShowOverlay(true);

    try {
      // Use the login function from useAuth hook to update the frontend state
      await login({ email, password });

      // Redirect to dashboard
      window.location.href = '/loading';
    } catch (error: unknown) {
      console.error('Login after device verification error:', error);
      setServerError(error instanceof Error ? error.message : 'An error occurred during login');
      setShowOverlay(false);
    }
  };

  const handleDeviceVerificationCancel = () => {
    setShowDeviceVerification(false);
    setServerError('Login canceled. Device not verified.');
  };

  return (
    <>
      {/* Login overlay */}
      <LoginOverlay isVisible={showOverlay} message="Logging you in to Kawandama Hills Plantation Management System..." />

      {/* Device verification dialog */}
      {showDeviceVerification && deviceVerificationData && (
        <DeviceVerification
          sessionId={deviceVerificationData.sessionId}
          deviceInfo={deviceVerificationData.deviceInfo}
          onVerify={handleDeviceVerified}
          onCancel={handleDeviceVerificationCancel}
        />
      )}

      <div className="flex min-h-screen w-full">
        {/* Left side - Login form */}
        <div className="flex w-full flex-col justify-center space-y-8 px-8 md:w-1/2 md:px-16 lg:px-32">
          <div className="mb-10">
            <AuthLogo size="md" />
          </div>


          <div className="space-y-2">
            <h1 className="text-2xl text-gray-800 font-bold tracking-tight md:text-3xl">Login to your account</h1>
            <p className="text-muted-foreground">Enter your credentials to access your account</p>

            
          </div>

          <div className="space-y-6">
            {(error || serverError) && (
              <Alert variant="destructive">
                <AlertDescription>{serverError || error}</AlertDescription>
              </Alert>
            )}

            <form onSubmit={handleSubmit} className="space-y-5">
              <div className="space-y-2">
                <label htmlFor="email" className="text-sm text-gray-400 font-medium">
                  Email
                </label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value);
                    resetAuthError();
                  }}
                  className="h-12 border-gray-300 focus:border-green-500 focus:ring-green-500"
                  required
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label htmlFor="password" className="text-sm text-gray-400 font-medium">
                    Password
                  </label>
                  <Link
                    href="/forgot-password"
                    className="text-sm text-green-600 hover:text-green-700 hover:underline"
                  >
                    Forgot password?
                  </Link>
                </div>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter your password"
                    value={password}
                    onChange={(e) => {
                      setPassword(e.target.value);
                      resetAuthError();
                    }}
                    className="h-12 pr-10 border-gray-300 focus:border-green-500 focus:ring-green-500"
                    required
                  />
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </div>

              <Button
                type="submit"
                className="w-full h-12 bg-gradient-to-r from-orange-500 to-green-600 hover:from-orange-600 hover:to-green-700 text-white"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></span>
                    Logging in...
                  </>
                ) : (
                  'Sign In'
                )}
              </Button>
            </form>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-200"></div>
              </div>
              
            </div>



            {/* <div className="text-center text-sm text-muted-foreground">
              Need help?{' '}
              <button
                onClick={() => window.open('/docs', '_blank', 'noopener,noreferrer')}
                className="text-green-600 hover:text-green-700 hover:underline font-medium"
              >
                View Documentation
              </button>
            </div> */}

            {/* <SystemModulesShowcase /> */}
          </div>
        </div>

        {/* Right side - Image and tagline */}
        <AuthBackground />
      </div>
    </>
  );
}
