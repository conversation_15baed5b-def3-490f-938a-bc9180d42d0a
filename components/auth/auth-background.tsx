"use client";

import React from 'react';
import Image from 'next/image';

interface AuthBackgroundProps {
  children?: React.ReactNode;
}

export function AuthBackground({ children }: AuthBackgroundProps) {
  return (
    <div className="hidden md:block md:w-1/2 relative overflow-hidden">
      {/* Background image */}
      <div className="absolute inset-0 w-full h-full">
        <Image
          src="/images/auth-background.jpg"
          alt="Professional office environment"
          fill
          style={{ objectFit: 'cover' }}
          priority
        />
      </div>

      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-orange-500/70 via-green-500/70 to-green-800/70 mix-blend-multiply"></div>

      {/* Content */}
      <div className="relative z-10 h-full flex flex-col items-center justify-center px-8 text-center">
        <div className="flex flex-wrap justify-center gap-3 mb-8 max-w-md mx-auto">
          {[
            { name: "HR Management", delay: "0s" },
            { name: "Accounting", delay: "0.1s" },
            { name: "CRM", delay: "0.2s" },
            { name: "Attendance", delay: "0.3s" },
            { name: "Tasks", delay: "0.4s" },
            { name: "Payroll", delay: "0.5s" },
            { name: "Recruitment", delay: "0.6s" },
            { name: "Inventory", delay: "0.7s" },
            { name: "Document Management", delay: "0.8s" },
            { name: "Project Management", delay: "0.9s" },
            { name: "Learning Management", delay: "1.0s" },
            { name: "Business Intelligence", delay: "1.1s" },
            { name: "Vendor Management", delay: "1.2s" },
            { name: "Asset Management", delay: "1.3s" },
            { name: "Compliance & Audit", delay: "1.4s" },
            // { name: "Communication", delay: "1.5s" },
            { name: "Knowledge Base", delay: "1.6s" },
            // { name: "E-commerce", delay: "1.7s" }
          ].map((module, index) => (
            <div
              key={index}
              className="rounded-full bg-white/90 px-5 py-2.5 text-sm font-medium shadow-md backdrop-blur-sm border border-white/20 transition-all hover:scale-105 hover:shadow-lg hover:bg-white/95 animate-float"
              style={{
                animationDelay: module.delay,
                animationDuration: '3s'
              }}
            >
              <span className="text-green-600">
                {module.name}
              </span>
            </div>
          ))}
        </div>



        {children || (
          <div className="mt-8 space-y-4 text-white">
            <h3 className="text-2xl font-semibold">Kawandama Hills Plantation Management System</h3>
            <p className="text-white/90 max-w-md mx-auto">
              Comprehensive plantation management platform with integrated modules covering HR, finance, operations, procurement, and agricultural management
            </p>
            <div className="pt-2 space-y-2">
              <p className="text-white/80 text-sm max-w-md mx-auto">
                Streamline plantation operations, enhance productivity, and make data-driven decisions with our comprehensive management solution
              </p>
              <div className="pt-2 flex flex-wrap justify-center gap-2">
                <div className="inline-flex rounded-full bg-white/20 px-4 py-1.5 text-white text-sm">
                  Trusted by agricultural enterprises
                </div>
                <div className="inline-flex rounded-full bg-white/20 px-4 py-1.5 text-white text-sm">
                  Secure & compliant
                </div>
                <div className="inline-flex rounded-full bg-white/20 px-4 py-1.5 text-white text-sm">
                  Customizable & scalable
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
