import React from 'react';
import {
  Building,
  Users,
  BarChart3,
  ShoppingCart,
  FileText,
  Briefcase,
  BookOpen,
  Shield,
  Package,
  UserCircle,
  Clock,
  CheckSquare,
  Wallet,
  UserPlus,
  MessageSquare,
  FileQuestion,
  Database,
  Settings,
} from 'lucide-react';

const modules = [
  { name: 'HR Management', icon: Users, color: 'text-blue-500' },
  { name: 'Accounting', icon: Wallet, color: 'text-green-500' },
  { name: 'CRM', icon: UserCircle, color: 'text-purple-500' },
  { name: 'Inventory', icon: Package, color: 'text-amber-500' },
  { name: 'Project Management', icon: Briefcase, color: 'text-indigo-500' },
  { name: 'Document Management', icon: FileText, color: 'text-red-500' },
  { name: 'Business Intelligence', icon: BarChart3, color: 'text-cyan-500' },
  { name: 'Learning Management', icon: BookOpen, color: 'text-emerald-500' },
  { name: 'Attendance', icon: Clock, color: 'text-orange-500' },
  { name: 'Tasks', icon: CheckSquare, color: 'text-pink-500' },
  { name: 'Recruitment', icon: UserPlus, color: 'text-violet-500' },
  { name: 'Vendor Management', icon: Building, color: 'text-yellow-500' },
  { name: 'Asset Management', icon: Database, color: 'text-lime-500' },
  { name: 'Compliance & Audit', icon: Shield, color: 'text-rose-500' },
  { name: 'Communication', icon: MessageSquare, color: 'text-sky-500' },
  { name: 'Knowledge Base', icon: FileQuestion, color: 'text-teal-500' },
  { name: 'E-commerce', icon: ShoppingCart, color: 'text-fuchsia-500' },
  { name: 'System Settings', icon: Settings, color: 'text-gray-500' },
];

export function SystemModulesShowcase() {
  return (
    <div className="mt-6 px-2">
      <div className="relative mb-4">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-200"></div>
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-white px-2 text-gray-500 font-semibold">Kawandama Hills Plantation Management Modules</span>
        </div>
      </div>
      
      <div className="grid grid-cols-3 gap-2">
        {modules.map((module, index) => (
          <div 
            key={index}
            className="flex flex-col items-center p-2 rounded-lg border border-gray-100 hover:border-gray-200 hover:bg-gray-50 transition-colors"
          >
            <module.icon className={`h-5 w-5 ${module.color} mb-1`} />
            <span className="text-xs text-center text-gray-700 font-medium">{module.name}</span>
          </div>
        ))}
      </div>
    </div>
  );
}
