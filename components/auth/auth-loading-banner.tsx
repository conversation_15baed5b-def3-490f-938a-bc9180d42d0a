"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
// import Image from 'next/image';

interface AuthLoadingBannerProps {
  redirectTo: string;
  message?: string;
  subMessage?: string;
  delayMs?: number;
  backgroundImage?: string;
}

export function AuthLoadingBanner({
  redirectTo,
  message = "Welcome to Kawandama Hills Plantation Management System",
  subMessage = "We are redirecting you shortly",
  delayMs = 2000,
  backgroundImage = "/images/office-background.jpg" // Default background image
}: AuthLoadingBannerProps) {
  const router = useRouter();
  const [dots, setDots] = useState('');
  const [imageLoaded, setImageLoaded] = useState(false);

  // Animate the loading dots
  useEffect(() => {
    const interval = setInterval(() => {
      setDots(prev => {
        if (prev.length >= 3) return '';
        return prev + '.';
      });
    }, 500);

    return () => clearInterval(interval);
  }, []);

  // Redirect after delay only if redirectTo is provided and not empty
  useEffect(() => {
    // Skip redirection if redirectTo is empty
    if (!redirectTo) return;

    const timeout = setTimeout(() => {
      router.push(redirectTo);
    }, delayMs);

    return () => clearTimeout(timeout);
  }, [router, redirectTo, delayMs]);

  // Preload the image
  useEffect(() => {
    const img = new window.Image();
    img.src = backgroundImage;
    img.onload = () => setImageLoaded(true);
  }, [backgroundImage]);

  return (
    <div className="fixed inset-0 flex flex-col items-center justify-center overflow-hidden">
      {/* Background image with overlay */}
      <div className="absolute inset-0 z-0">
        <div
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: `url(${backgroundImage})`,
            opacity: imageLoaded ? 1 : 0,
            transition: 'opacity 0.5s ease-in-out'
          }}
        />
        {/* Translucent overlay */}
        <div className="absolute inset-0 bg-blue-600/80 backdrop-blur-sm" />
      </div>

      {/* Content */}
      <div className="container relative z-10 max-w-3xl px-4 text-center text-white">
        <h1 className="mb-6 text-4xl font-bold leading-tight tracking-tight md:text-5xl lg:text-6xl">
          {message}
        </h1>
        <p className="mb-8 text-xl font-medium md:text-2xl">
          {subMessage}
        </p>
        <div className="flex justify-center">
          <div className="flex h-16 items-center justify-center">
            <div className="flex space-x-2">
              <div className="h-4 w-4 animate-pulse rounded-full bg-white"></div>
              <div className="h-4 w-4 animate-pulse rounded-full bg-white" style={{ animationDelay: '0.2s' }}></div>
              <div className="h-4 w-4 animate-pulse rounded-full bg-white" style={{ animationDelay: '0.4s' }}></div>
            </div>
            <span className="ml-4 text-xl font-medium">Loading{dots}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
