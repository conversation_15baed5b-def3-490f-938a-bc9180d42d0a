"use client"

import React from 'react'
import Image from "next/image"
import { Play } from "lucide-react"

interface AuthLayoutProps {
  children: React.ReactNode
  testimonialImage?: string
  testimonialName?: string
  testimonialRole?: string
}

export function AuthLayout({
  children,
  testimonialImage = "/confident-professional.png",
  testimonialName = "Sarah Amalija",
  testimonialRole = "Founder & Creative Director, BuildLabs™"
}: AuthLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-4">
      <div className="w-full max-w-5xl bg-white rounded-lg shadow-lg overflow-hidden flex flex-col md:flex-row">
        {/* Left side - Form */}
        <div className="w-full p-8 md:p-12">
          <div className="mb-8">
            <div className="inline-flex items-center gap-1 px-3 py-1 rounded-full border border-gray-200 text-sm font-medium">
              <span className="w-2 h-2 bg-black rounded-full"></span> Kawandama Hills Plantation
            </div>
          </div>

          <div className="space-y-6 max-w-md mx-auto">
            {children}
          </div>
        </div>

        {/* Right side - Image and testimonial */}
        <div className="w-full md:w-1/2 bg-gray-300 relative hidden md:block">
          <Image
            src={testimonialImage}
            alt="Testimonial"
            width={500}
            height={600}
            className="w-full h-full object-cover"
          />

          {/* Play button in the center */}
          <div className="absolute inset-0 flex items-center justify-center">
            <button className="w-10 h-10 bg-white/30 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/70">
              <Play className="w-4 h-4 text-white ml-0.5" />
            </button>
          </div>

          {/* Text bubbles at the top */}
          <div className="absolute top-0 left-0 right-0 p-8">
            <div className="flex flex-wrap gap-1 mb-4">
              <div className="bg-white/30 backdrop-blur-sm border border-white/70 rounded-full px-4 py-1 text-sm font-semibold text-white">We</div>
              <div className="bg-white/30 backdrop-blur-sm border border-white/70 rounded-full px-4 py-1 text-sm font-semibold text-white">couldn't</div>
              <div className="bg-white/30 backdrop-blur-sm border border-white/70 rounded-full px-4 py-1 text-sm font-semibold text-white">imagine</div>
              <div className="bg-white/30 backdrop-blur-sm border border-white/70 rounded-full px-4 py-1 text-sm font-semibold text-white">building</div>
              <div className="bg-white/30 backdrop-blur-sm border border-white/70 rounded-full px-4 py-1 text-sm font-semibold text-white">without</div>
              <div className="bg-white/30 backdrop-blur-sm border border-white/70 rounded-full px-4 py-1 text-sm font-semibold text-white">HRImpact</div>
            </div>
          </div>

          {/* Testimonial at the bottom */}
          <div className="absolute bottom-0 left-0 right-0 p-8">
            <div className="flex items-center mb-2">
              <button className="w-8 h-8 bg-white/30 backdrop-blur-sm rounded-full flex items-center justify-center mr-2 border border-white/70">
                <div className="w-3 h-3 bg-white rounded-full"></div>
              </button>
            </div>
            <h3 className="text-white text-xl font-semibold">{testimonialName}</h3>
            <p className="text-white/80 text-sm">{testimonialRole}</p>

            <div className="flex items-center justify-between mt-4">
              <div className="flex">
                {[1, 2, 3, 4, 5].map((star) => (
                  <svg key={star} className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>

              <div className="flex gap-2">
                <button className="w-8 h-8 bg-white/30 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/70">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <button className="w-8 h-8 bg-white/30 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/70">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
