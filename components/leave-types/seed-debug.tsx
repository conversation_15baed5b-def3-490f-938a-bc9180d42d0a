"use client"

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, Database, Bug } from 'lucide-react';

export function SeedDebug() {
  const { toast } = useToast();
  const [isSeeding, setIsSeeding] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string[]>([]);

  const addDebugInfo = (message: string) => {
    setDebugInfo(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testSeedEndpoint = async () => {
    setIsSeeding(true);
    setDebugInfo([]);
    
    try {
      addDebugInfo('Starting seed test...');
      
      // Test if endpoint exists
      addDebugInfo('Making request to /api/admin/seed/leave-types');
      
      const response = await fetch('/api/admin/seed/leave-types', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      addDebugInfo(`Response status: ${response.status}`);
      addDebugInfo(`Response ok: ${response.ok}`);

      const responseText = await response.text();
      addDebugInfo(`Response text length: ${responseText.length}`);
      
      let responseData;
      try {
        responseData = JSON.parse(responseText);
        addDebugInfo(`Response JSON: ${JSON.stringify(responseData, null, 2)}`);
      } catch (parseError) {
        addDebugInfo(`Failed to parse JSON: ${parseError}`);
        addDebugInfo(`Raw response: ${responseText}`);
      }

      if (!response.ok) {
        throw new Error(responseData?.error || `HTTP ${response.status}: ${responseText}`);
      }

      toast({
        title: "Success",
        description: responseData?.message || "Seed completed successfully",
      });

    } catch (error) {
      addDebugInfo(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to seed leave types",
        variant: "destructive",
      });
    } finally {
      setIsSeeding(false);
    }
  };

  const testDirectAPI = async () => {
    setIsSeeding(true);
    setDebugInfo([]);

    try {
      addDebugInfo('Testing direct API access...');

      // Test auth first
      const authResponse = await fetch('/api/auth/me');
      addDebugInfo(`Auth status: ${authResponse.status}`);

      if (authResponse.ok) {
        const authData = await authResponse.json();
        addDebugInfo(`User role: ${authData.data?.user?.role}`);
      }

      // Test leave types endpoint
      const leaveTypesResponse = await fetch('/api/leave/types');
      addDebugInfo(`Leave types status: ${leaveTypesResponse.status}`);

      if (leaveTypesResponse.ok) {
        const leaveTypesData = await leaveTypesResponse.json();
        addDebugInfo(`Current leave types count: ${leaveTypesData.data?.length || 0}`);
      }

    } catch (error) {
      addDebugInfo(`Direct API test error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSeeding(false);
    }
  };

  const testAuth = async () => {
    setIsSeeding(true);
    setDebugInfo([]);

    try {
      addDebugInfo('Testing authentication endpoint...');

      const response = await fetch('/api/admin/test-auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      addDebugInfo(`Auth test status: ${response.status}`);

      const responseText = await response.text();
      let responseData;

      try {
        responseData = JSON.parse(responseText);
        addDebugInfo(`Auth test response: ${JSON.stringify(responseData, null, 2)}`);
      } catch (parseError) {
        addDebugInfo(`Failed to parse auth response: ${responseText}`);
      }

    } catch (error) {
      addDebugInfo(`Auth test error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSeeding(false);
    }
  };

  const testSimpleSeed = async () => {
    setIsSeeding(true);
    setDebugInfo([]);

    try {
      addDebugInfo('Testing simple seed endpoint...');

      const response = await fetch('/api/admin/seed/leave-types-simple', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      addDebugInfo(`Simple seed status: ${response.status}`);

      const responseText = await response.text();
      let responseData;

      try {
        responseData = JSON.parse(responseText);
        addDebugInfo(`Simple seed response: ${JSON.stringify(responseData, null, 2)}`);
      } catch (parseError) {
        addDebugInfo(`Failed to parse simple seed response: ${responseText}`);
      }

      if (response.ok) {
        toast({
          title: "Success",
          description: responseData?.message || "Simple seed completed",
        });
      } else {
        toast({
          title: "Error",
          description: responseData?.error || "Simple seed failed",
          variant: "destructive",
        });
      }

    } catch (error) {
      addDebugInfo(`Simple seed error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSeeding(false);
    }
  };

  const clearDebug = () => {
    setDebugInfo([]);
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bug className="h-5 w-5" />
          Seed Debug Panel
        </CardTitle>
        <CardDescription>
          Debug the seed leave types functionality
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-wrap gap-2">
          <Button 
            onClick={testSeedEndpoint} 
            disabled={isSeeding}
            variant="default"
          >
            {isSeeding ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Testing...
              </>
            ) : (
              <>
                <Database className="h-4 w-4 mr-2" />
                Test Seed Endpoint
              </>
            )}
          </Button>
          
          <Button
            onClick={testDirectAPI}
            disabled={isSeeding}
            variant="outline"
          >
            Test API Access
          </Button>

          <Button
            onClick={testAuth}
            disabled={isSeeding}
            variant="secondary"
          >
            Test Auth
          </Button>

          <Button
            onClick={testSimpleSeed}
            disabled={isSeeding}
            className="bg-green-600 hover:bg-green-700"
          >
            Test Simple Seed
          </Button>
          
          <Button 
            onClick={clearDebug} 
            variant="ghost"
          >
            Clear Debug
          </Button>
        </div>

        <div className="bg-muted p-4 rounded-lg">
          <h3 className="font-semibold mb-2">Debug Output:</h3>
          <div className="space-y-1 max-h-60 overflow-y-auto">
            {debugInfo.length === 0 ? (
              <div className="text-muted-foreground text-sm">No debug info yet</div>
            ) : (
              debugInfo.map((info, index) => (
                <div key={index} className="text-sm font-mono">
                  {info}
                </div>
              ))
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
