"use client"

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { useToast } from '@/components/ui/use-toast';
import { useLeaveTypes, ILeaveType } from '@/hooks/use-leave-types';
import { Search, Plus, MoreH<PERSON>zon<PERSON>, Edit, Trash2, <PERSON>, RefreshCw, Loader2 } from 'lucide-react';
import { LeaveTypeFormDialog } from './leave-type-form-dialog';

interface LeaveTypesListProps {
  className?: string;
}

export function LeaveTypesList({ className }: LeaveTypesListProps) {
  const { toast } = useToast();
  const { leaveTypes, loading, fetchLeaveTypes, deleteLeaveType } = useLeaveTypes({
    autoFetch: true,
    activeOnly: false
  });

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLeaveType, setSelectedLeaveType] = useState<ILeaveType | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [leaveTypeToDelete, setLeaveTypeToDelete] = useState<ILeaveType | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Filter leave types based on search term
  const filteredLeaveTypes = leaveTypes.filter(leaveType =>
    leaveType.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    leaveType.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    leaveType.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleCreateNew = () => {
    setSelectedLeaveType(null);
    setIsFormOpen(true);
  };

  const handleEdit = (leaveType: ILeaveType) => {
    setSelectedLeaveType(leaveType);
    setIsFormOpen(true);
  };

  const handleDelete = (leaveType: ILeaveType) => {
    setLeaveTypeToDelete(leaveType);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!leaveTypeToDelete) return;

    setIsDeleting(true);
    try {
      const success = await deleteLeaveType(leaveTypeToDelete._id);
      if (success) {
        setIsDeleteDialogOpen(false);
        setLeaveTypeToDelete(null);
      }
    } catch (error) {
      console.error('Error deleting leave type:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleFormSuccess = () => {
    setIsFormOpen(false);
    setSelectedLeaveType(null);
    fetchLeaveTypes();
  };

  const getStatusBadge = (leaveType: ILeaveType) => {
    if (leaveType.isActive) {
      return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
    } else {
      return <Badge variant="secondary">Inactive</Badge>;
    }
  };

  const getPaidBadge = (isPaid: boolean) => {
    if (isPaid) {
      return <Badge variant="default" className="bg-blue-100 text-blue-800">Paid</Badge>;
    } else {
      return <Badge variant="outline">Unpaid</Badge>;
    }
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Leave Types</CardTitle>
              <CardDescription>
                Manage different types of leave available to employees
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchLeaveTypes()}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button onClick={handleCreateNew}>
                <Plus className="h-4 w-4 mr-2" />
                Add Leave Type
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Search */}
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search leave types..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Table */}
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>Loading leave types...</span>
            </div>
          ) : filteredLeaveTypes.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-muted-foreground">
                {searchTerm ? 'No leave types found matching your search.' : 'No leave types found.'}
              </div>
              {!searchTerm && (
                <Button onClick={handleCreateNew} className="mt-4">
                  <Plus className="h-4 w-4 mr-2" />
                  Create your first leave type
                </Button>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Code</TableHead>
                  <TableHead>Default Days</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Approval Required</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredLeaveTypes.map((leaveType) => (
                  <TableRow key={leaveType._id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: leaveType.color }}
                        />
                        <div>
                          <div className="font-medium">{leaveType.name}</div>
                          {leaveType.description && (
                            <div className="text-sm text-muted-foreground">
                              {leaveType.description}
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{leaveType.code}</Badge>
                    </TableCell>
                    <TableCell>{leaveType.defaultDays}</TableCell>
                    <TableCell>{getPaidBadge(leaveType.isPaid)}</TableCell>
                    <TableCell>{getStatusBadge(leaveType)}</TableCell>
                    <TableCell>
                      {leaveType.requiresApproval ? (
                        <Badge variant="default" className="bg-orange-100 text-orange-800">
                          Required
                        </Badge>
                      ) : (
                        <Badge variant="outline">Not Required</Badge>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEdit(leaveType)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDelete(leaveType)}
                            className="text-destructive"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Form Dialog */}
      <LeaveTypeFormDialog
        open={isFormOpen}
        onOpenChange={setIsFormOpen}
        leaveType={selectedLeaveType}
        onSuccess={handleFormSuccess}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Leave Type</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{leaveTypeToDelete?.name}"? This action cannot be undone.
              {leaveTypeToDelete && (
                <div className="mt-2 p-2 bg-muted rounded text-sm">
                  <strong>Code:</strong> {leaveTypeToDelete.code}
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
