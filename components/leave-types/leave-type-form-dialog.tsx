"use client"

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { useToast } from '@/components/ui/use-toast';
import { useLeaveTypes, ILeaveType } from '@/hooks/use-leave-types';
import { Loader2 } from 'lucide-react';

const leaveTypeSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  code: z.string().min(1, 'Code is required').max(10, 'Code must be less than 10 characters').regex(/^[A-Z0-9_]+$/, 'Code must contain only uppercase letters, numbers, and underscores'),
  description: z.string().max(500, 'Description must be less than 500 characters').optional(),
  defaultDays: z.number().min(0, 'Default days must be 0 or greater').max(365, 'Default days cannot exceed 365'),
  isPaid: z.boolean(),
  requiresApproval: z.boolean(),
  maxConsecutiveDays: z.number().min(0, 'Max consecutive days must be 0 or greater').max(365, 'Max consecutive days cannot exceed 365'),
  minNoticeInDays: z.number().min(0, 'Min notice days must be 0 or greater').max(365, 'Min notice days cannot exceed 365'),
  allowCarryOver: z.boolean(),
  maxCarryOverDays: z.number().min(0, 'Max carry over days must be 0 or greater').max(365, 'Max carry over days cannot exceed 365'),
  color: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Color must be a valid hex color'),
  isActive: z.boolean(),
});

type LeaveTypeFormData = z.infer<typeof leaveTypeSchema>;

interface LeaveTypeFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  leaveType?: ILeaveType | null;
  onSuccess?: () => void;
}

export function LeaveTypeFormDialog({ 
  open, 
  onOpenChange, 
  leaveType, 
  onSuccess 
}: LeaveTypeFormDialogProps) {
  const { toast } = useToast();
  const { createLeaveType, updateLeaveType } = useLeaveTypes({ autoFetch: false });

  const isEditing = !!leaveType;

  const form = useForm<LeaveTypeFormData>({
    resolver: zodResolver(leaveTypeSchema),
    defaultValues: {
      name: '',
      code: '',
      description: '',
      defaultDays: 0,
      isPaid: true,
      requiresApproval: true,
      maxConsecutiveDays: 0,
      minNoticeInDays: 0,
      allowCarryOver: false,
      maxCarryOverDays: 0,
      color: '#3B82F6',
      isActive: true,
    },
  });

  // Update form when leaveType changes
  React.useEffect(() => {
    if (leaveType) {
      form.reset({
        name: leaveType.name,
        code: leaveType.code,
        description: leaveType.description || '',
        defaultDays: leaveType.defaultDays,
        isPaid: leaveType.isPaid,
        requiresApproval: leaveType.requiresApproval,
        maxConsecutiveDays: leaveType.maxConsecutiveDays,
        minNoticeInDays: leaveType.minNoticeInDays,
        allowCarryOver: leaveType.allowCarryOver,
        maxCarryOverDays: leaveType.maxCarryOverDays,
        color: leaveType.color || '#3B82F6',
        isActive: leaveType.isActive,
      });
    } else {
      form.reset({
        name: '',
        code: '',
        description: '',
        defaultDays: 0,
        isPaid: true,
        requiresApproval: true,
        maxConsecutiveDays: 0,
        minNoticeInDays: 0,
        allowCarryOver: false,
        maxCarryOverDays: 0,
        color: '#3B82F6',
        isActive: true,
      });
    }
  }, [leaveType, form]);

  const onSubmit = async (data: LeaveTypeFormData) => {
    try {
      let result;
      if (isEditing && leaveType) {
        result = await updateLeaveType(leaveType._id, data);
      } else {
        result = await createLeaveType(data);
      }

      if (result) {
        onSuccess?.();
        onOpenChange(false);
      }
    } catch (error) {
      console.error('Error saving leave type:', error);
    }
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Leave Type' : 'Create New Leave Type'}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update the leave type details below.' 
              : 'Fill in the details to create a new leave type.'
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Annual Leave" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Code *</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="e.g., ANNUAL" 
                        {...field} 
                        onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                      />
                    </FormControl>
                    <FormDescription>
                      Unique identifier (uppercase letters, numbers, underscores only)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Brief description of this leave type"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="defaultDays"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Default Days *</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        min="0" 
                        max="365"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>
                      Default annual allocation
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="maxConsecutiveDays"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Max Consecutive Days</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        min="0" 
                        max="365"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>
                      0 = no limit
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="minNoticeInDays"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Min Notice (Days)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        min="0" 
                        max="365"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>
                      Required advance notice
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="color"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Color</FormLabel>
                    <FormControl>
                      <div className="flex items-center gap-2">
                        <Input 
                          type="color" 
                          className="w-16 h-10 p-1 border rounded"
                          {...field}
                        />
                        <Input 
                          placeholder="#3B82F6"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormDescription>
                      Color for calendar and visual identification
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="maxCarryOverDays"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Max Carry Over Days</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        min="0" 
                        max="365"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        disabled={!form.watch('allowCarryOver')}
                      />
                    </FormControl>
                    <FormDescription>
                      Days that can be carried to next year
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="isPaid"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel>Paid Leave</FormLabel>
                        <FormDescription>
                          Employee receives salary during this leave
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="requiresApproval"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel>Requires Approval</FormLabel>
                        <FormDescription>
                          Leave requests need manager approval
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="allowCarryOver"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel>Allow Carry Over</FormLabel>
                        <FormDescription>
                          Unused days can be carried to next year
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="isActive"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel>Active</FormLabel>
                        <FormDescription>
                          Leave type is available for use
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={form.formState.isSubmitting}>
                {form.formState.isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {isEditing ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  isEditing ? 'Update Leave Type' : 'Create Leave Type'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
