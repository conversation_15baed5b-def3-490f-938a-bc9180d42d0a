"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { useEffect, useState, useCallback } from "react"
import { toast } from "@/components/ui/use-toast"
import {
  CalendarDays,
  RefreshCw,
  AlertCircle,
  Clock,
  DollarSign,
  FileText,
  User,
  CheckCircle,
  XCircle,
  Eye,
  MessageSquare
} from "lucide-react"
import Link from "next/link"
import { EmptyState, useEmptyState } from "@/components/ui/empty-state"
import {
  DashboardApproval,
  ApprovalType,
  Priority,
  UrgencyLevel,
  ApprovalAction
} from "@/lib/types/dashboard-approvals"
import { apiGet, ApiError } from "@/lib/utils/api-error-handler"

interface UpcomingReviewsProps {
  className?: string
}

export function UpcomingReviews({ className }: UpcomingReviewsProps) {
  const [approvals, setApprovals] = useState<DashboardApproval[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())
  const { getEmptyState } = useEmptyState()

  const fetchPendingApprovals = useCallback(async (showToast = false) => {
    setIsLoading(true)
    setError(null)

    try {
      const data = await apiGet('/api/dashboard/approvals?limit=10')

      if (data.success && data.data?.approvals) {
        setApprovals(data.data.approvals)
        setLastRefresh(new Date())

        if (showToast) {
          toast({
            title: "Approvals Updated",
            description: `Loaded ${data.data.approvals.length} pending approvals`,
            variant: "default",
          })
        }
      } else {
        throw new ApiError('Invalid response format', 500)
      }

    } catch (err) {
      console.error('Error fetching pending approvals:', err)

      let errorMessage = 'Failed to load pending approvals'
      if (err instanceof ApiError) {
        errorMessage = err.message
      } else if (err instanceof Error) {
        errorMessage = err.message
      }

      setError(errorMessage)

      if (showToast) {
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        })
      }
    } finally {
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchPendingApprovals()

    // Set up auto-refresh every 60 seconds
    const interval = setInterval(() => {
      fetchPendingApprovals()
    }, 60000)

    return () => clearInterval(interval)
  }, [fetchPendingApprovals])

  const handleRefresh = () => {
    fetchPendingApprovals(true)
  }

  const getApprovalTypeIcon = (type: ApprovalType) => {
    switch (type) {
      case ApprovalType.LEAVE_REQUEST:
        return <CalendarDays className="h-4 w-4" />
      case ApprovalType.BUDGET:
        return <DollarSign className="h-4 w-4" />
      case ApprovalType.INCOME:
        return <DollarSign className="h-4 w-4 text-green-600" />
      case ApprovalType.EXPENDITURE:
        return <DollarSign className="h-4 w-4 text-red-600" />
      case ApprovalType.PROCUREMENT_REQUISITION:
        return <FileText className="h-4 w-4" />
      case ApprovalType.EMPLOYEE_REVIEW:
        return <User className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getPriorityColor = (priority: Priority) => {
    switch (priority) {
      case Priority.CRITICAL:
        return 'bg-red-100 text-red-800 border-red-200'
      case Priority.URGENT:
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case Priority.HIGH:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case Priority.MEDIUM:
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case Priority.LOW:
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getUrgencyColor = (urgency: UrgencyLevel) => {
    switch (urgency) {
      case UrgencyLevel.OVERDUE:
        return 'text-red-600'
      case UrgencyLevel.CRITICAL:
        return 'text-red-500'
      case UrgencyLevel.URGENT:
        return 'text-orange-500'
      case UrgencyLevel.NORMAL:
        return 'text-gray-500'
      default:
        return 'text-gray-500'
    }
  }

  const formatCurrency = (amount?: number) => {
    if (!amount) return ''
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const getApprovalLink = (approval: DashboardApproval) => {
    switch (approval.type) {
      case ApprovalType.LEAVE_REQUEST:
        return `/dashboard/hr/leave/requests/${approval.metadata.entityId}`
      case ApprovalType.BUDGET:
        return `/dashboard/accounting/budgets/${approval.metadata.entityId}`
      case ApprovalType.INCOME:
        return `/dashboard/accounting/income/${approval.metadata.entityId}`
      case ApprovalType.EXPENDITURE:
        return `/dashboard/accounting/expenditure/${approval.metadata.entityId}`
      default:
        return '#'
    }
  }

  const formatRelativeTime = (date: Date) => {
    const now = new Date()
    const diff = now.getTime() - new Date(date).getTime()

    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (minutes < 1) return 'Just now'
    if (minutes < 60) return `${minutes}m ago`
    if (hours < 24) return `${hours}h ago`
    if (days < 7) return `${days}d ago`

    return new Date(date).toLocaleDateString()
  }

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              Upcoming Reviews
              {approvals.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {approvals.length}
                </Badge>
              )}
              {approvals.filter(a => a.isOverdue).length > 0 && (
                <Badge variant="destructive" className="text-xs">
                  {approvals.filter(a => a.isOverdue).length} overdue
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Pending approvals requiring your attention
              {lastRefresh && (
                <span className="text-xs text-muted-foreground ml-2">
                  • Updated {lastRefresh.toLocaleTimeString()}
                </span>
              )}
            </CardDescription>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
            className="h-8 w-8 p-0"
          >
            <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="flex items-start gap-4 rounded-lg border p-3">
                <Skeleton className="h-9 w-9 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                  <Skeleton className="h-3 w-1/4" />
                </div>
              </div>
            ))}
          </div>
        ) : getEmptyState(
          isLoading,
          error,
          approvals.length > 0,
          {
            noDataTitle: "All caught up!",
            noDataDescription: "No pending approvals at the moment",
            onRetry: handleRefresh,
            icon: CheckCircle
          }
        ) || (
          <div className="space-y-3">
            {approvals.map((approval) => (
              <div
                key={approval.id}
                className="flex items-start gap-4 rounded-lg border p-3 hover:bg-muted/50 transition-colors group"
              >
                <div className="relative">
                  <Avatar className="h-9 w-9 border border-primary/10">
                    <AvatarImage
                      src={approval.submittedBy.avatar || "/placeholder.svg"}
                      alt={approval.submittedBy.name}
                    />
                    <AvatarFallback className="text-xs">
                      {approval.submittedBy.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="absolute -bottom-1 -right-1 bg-background rounded-full p-0.5">
                    {getApprovalTypeIcon(approval.type)}
                  </div>
                </div>

                <div className="flex-1 space-y-1 min-w-0">
                  <div className="flex items-center justify-between gap-2">
                    <Link
                      href={getApprovalLink(approval)}
                      className="text-sm font-medium leading-none hover:underline truncate"
                    >
                      {approval.title}
                    </Link>
                    <div className="flex items-center gap-1 flex-shrink-0">
                      {approval.priority !== Priority.LOW && (
                        <Badge className={cn("text-xs", getPriorityColor(approval.priority))}>
                          {approval.priority}
                        </Badge>
                      )}
                      {approval.isOverdue && (
                        <Badge variant="destructive" className="text-xs">
                          Overdue
                        </Badge>
                      )}
                    </div>
                  </div>

                  <p className="text-xs text-muted-foreground truncate">
                    {approval.description}
                    {approval.amount && (
                      <span className="font-medium ml-1">
                        • {formatCurrency(approval.amount)}
                      </span>
                    )}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3 text-xs text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {approval.submittedBy.name}
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className={cn("h-3 w-3", getUrgencyColor(approval.urgencyLevel))} />
                        {formatRelativeTime(approval.submittedAt)}
                      </span>
                      {approval.submittedBy.department && (
                        <span>{approval.submittedBy.department}</span>
                      )}
                    </div>

                    <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      {approval.availableActions.includes(ApprovalAction.APPROVE) && (
                        <Button size="sm" variant="ghost" className="h-6 w-6 p-0 text-green-600 hover:text-green-700">
                          <CheckCircle className="h-3 w-3" />
                        </Button>
                      )}
                      {approval.availableActions.includes(ApprovalAction.REJECT) && (
                        <Button size="sm" variant="ghost" className="h-6 w-6 p-0 text-red-600 hover:text-red-700">
                          <XCircle className="h-3 w-3" />
                        </Button>
                      )}
                      {approval.availableActions.includes(ApprovalAction.VIEW_DETAILS) && (
                        <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                          <Eye className="h-3 w-3" />
                        </Button>
                      )}
                      {approval.availableActions.includes(ApprovalAction.COMMENT) && (
                        <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                          <MessageSquare className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
