"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Loader2, 
  RefreshCw,
  Database,
  Users,
  Calendar,
  FileText,
  Eye,
  Bug
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useLeaveRequests } from '@/hooks/use-leave-requests';
import { format } from 'date-fns';

interface DebugTest {
  name: string;
  status: 'pending' | 'running' | 'success' | 'error';
  result?: string;
  error?: string;
  data?: any;
}

export function LeaveRequestsDebug() {
  const [tests, setTests] = useState<DebugTest[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const { toast } = useToast();

  const {
    requests,
    loading: hookLoading,
    error: hookError,
    pagination,
    fetchRequests,
    refreshRequests
  } = useLeaveRequests({
    initialParams: { page: 1, limit: 10 },
    autoFetch: false // Don't auto-fetch, we'll control it manually
  });

  const updateTest = (name: string, updates: Partial<DebugTest>) => {
    setTests(prev => prev.map(test => 
      test.name === name ? { ...test, ...updates } : test
    ));
  };

  const runTest = async (testName: string, testFn: () => Promise<any>) => {
    updateTest(testName, { status: 'running' });
    try {
      const result = await testFn();
      updateTest(testName, { 
        status: 'success', 
        result: typeof result === 'string' ? result : JSON.stringify(result, null, 2),
        data: result
      });
      return result;
    } catch (error) {
      updateTest(testName, { 
        status: 'error', 
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  };

  const initializeTests = () => {
    const testList: DebugTest[] = [
      { name: 'Database Connection', status: 'pending' },
      { name: 'Authentication Check', status: 'pending' },
      { name: 'Leave Requests API', status: 'pending' },
      { name: 'Leave Types API', status: 'pending' },
      { name: 'Employees API', status: 'pending' },
      { name: 'Leave Balances API', status: 'pending' },
      { name: 'Hook Integration', status: 'pending' },
      { name: 'Data Transformation', status: 'pending' },
    ];
    setTests(testList);
  };

  const testDatabaseConnection = async () => {
    const response = await fetch('/api/debug/database');
    const data = await response.json();
    if (!response.ok) throw new Error(data.error || 'Database connection failed');
    return `Connected to: ${data.database}`;
  };

  const testAuthentication = async () => {
    const response = await fetch('/api/auth/session');
    const data = await response.json();
    if (!response.ok) throw new Error('Authentication failed');
    return `Authenticated as: ${data.user?.email} (${data.user?.role})`;
  };

  const testLeaveRequestsAPI = async () => {
    const response = await fetch('/api/leave/requests?page=1&limit=5');
    const data = await response.json();
    if (!response.ok) throw new Error(data.error || 'Leave requests API failed');
    return {
      total: data.pagination?.total || 0,
      requests: data.data?.length || 0,
      sample: data.data?.[0] || null
    };
  };

  const testLeaveTypesAPI = async () => {
    const response = await fetch('/api/leave/types');
    const data = await response.json();
    if (!response.ok) throw new Error(data.error || 'Leave types API failed');
    return {
      total: data.data?.length || 0,
      types: data.data?.map((t: any) => t.name) || []
    };
  };

  const testEmployeesAPI = async () => {
    const response = await fetch('/api/employees?page=1&limit=5');
    const data = await response.json();
    if (!response.ok) throw new Error(data.error || 'Employees API failed');
    return {
      total: data.pagination?.total || 0,
      employees: data.data?.length || 0,
      sample: data.data?.[0] || null
    };
  };

  const testLeaveBalancesAPI = async () => {
    const response = await fetch('/api/leave/balances');
    const data = await response.json();
    if (!response.ok) throw new Error(data.error || 'Leave balances API failed');
    return {
      total: data.data?.length || 0,
      balances: data.data?.length || 0
    };
  };

  const testHookIntegration = async () => {
    // Test the hook by calling fetchRequests
    await fetchRequests({ page: 1, limit: 5 });
    return {
      hookLoading,
      hookError,
      requestsCount: requests.length,
      pagination
    };
  };

  const testDataTransformation = async () => {
    if (requests.length === 0) {
      throw new Error('No requests available for transformation test');
    }
    
    const sampleRequest = requests[0];
    return {
      hasId: !!sampleRequest._id,
      hasEmployee: !!(sampleRequest.employeeId),
      hasLeaveType: !!(sampleRequest.leaveTypeId),
      hasStatus: !!sampleRequest.status,
      hasDates: !!(sampleRequest.startDate && sampleRequest.endDate)
    };
  };

  const runAllTests = async () => {
    setIsRunning(true);
    try {
      await runTest('Database Connection', testDatabaseConnection);
      await runTest('Authentication Check', testAuthentication);
      await runTest('Leave Requests API', testLeaveRequestsAPI);
      await runTest('Leave Types API', testLeaveTypesAPI);
      await runTest('Employees API', testEmployeesAPI);
      await runTest('Leave Balances API', testLeaveBalancesAPI);
      await runTest('Hook Integration', testHookIntegration);
      await runTest('Data Transformation', testDataTransformation);
      
      toast({
        title: "Debug Tests Complete",
        description: "All tests have been executed. Check results below.",
      });
    } catch (error) {
      toast({
        title: "Debug Tests Failed",
        description: "Some tests failed. Check the results for details.",
        variant: "destructive",
      });
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: DebugTest['status']) => {
    switch (status) {
      case 'pending': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'running': return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error': return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  useEffect(() => {
    initializeTests();
  }, []);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Bug className="h-5 w-5" />
                Leave Requests Debug Console
              </CardTitle>
              <CardDescription>
                Comprehensive testing and debugging for leave requests functionality
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                onClick={runAllTests}
                disabled={isRunning}
                className="gap-2"
              >
                {isRunning ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
                Run All Tests
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="tests" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="tests">Test Results</TabsTrigger>
              <TabsTrigger value="data">Raw Data</TabsTrigger>
              <TabsTrigger value="hook">Hook Status</TabsTrigger>
            </TabsList>
            
            <TabsContent value="tests" className="space-y-4">
              <div className="grid gap-4">
                {tests.map((test) => (
                  <Card key={test.name}>
                    <CardContent className="pt-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(test.status)}
                          <span className="font-medium">{test.name}</span>
                        </div>
                        <Badge variant={
                          test.status === 'success' ? 'default' :
                          test.status === 'error' ? 'destructive' :
                          test.status === 'running' ? 'secondary' : 'outline'
                        }>
                          {test.status}
                        </Badge>
                      </div>
                      
                      {test.result && (
                        <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded text-sm">
                          <pre className="whitespace-pre-wrap">{test.result}</pre>
                        </div>
                      )}
                      
                      {test.error && (
                        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                          {test.error}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="data" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-4 w-4" />
                    Raw Leave Requests Data
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center gap-4">
                      <Badge variant="outline">Count: {requests.length}</Badge>
                      <Badge variant="outline">Loading: {hookLoading ? 'Yes' : 'No'}</Badge>
                      {hookError && <Badge variant="destructive">Error: {hookError}</Badge>}
                    </div>
                    
                    {requests.length > 0 ? (
                      <div className="space-y-2">
                        {requests.slice(0, 3).map((request, index) => (
                          <div key={index} className="p-3 border rounded-lg">
                            <div className="grid grid-cols-2 gap-2 text-sm">
                              <div><strong>ID:</strong> {request._id?.toString()}</div>
                              <div><strong>Status:</strong> {request.status}</div>
                              <div><strong>Employee:</strong> {JSON.stringify(request.employeeId)}</div>
                              <div><strong>Leave Type:</strong> {JSON.stringify(request.leaveTypeId)}</div>
                              <div><strong>Start:</strong> {request.startDate ? format(new Date(request.startDate), 'yyyy-MM-dd') : 'N/A'}</div>
                              <div><strong>End:</strong> {request.endDate ? format(new Date(request.endDate), 'yyyy-MM-dd') : 'N/A'}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                          No leave requests found. This could indicate:
                          <ul className="list-disc list-inside mt-2">
                            <li>No leave requests have been created yet</li>
                            <li>Database connection issues</li>
                            <li>Authentication/permission problems</li>
                            <li>API endpoint issues</li>
                          </ul>
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="hook" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    Hook Status & Controls
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">Loading:</span>
                        <Badge variant={hookLoading ? "secondary" : "outline"}>
                          {hookLoading ? "Yes" : "No"}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">Error:</span>
                        <Badge variant={hookError ? "destructive" : "outline"}>
                          {hookError || "None"}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">Requests:</span>
                        <Badge variant="outline">{requests.length}</Badge>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Button 
                        onClick={() => fetchRequests({ page: 1, limit: 10 })}
                        disabled={hookLoading}
                        size="sm"
                        className="w-full"
                      >
                        Manual Fetch
                      </Button>
                      <Button 
                        onClick={refreshRequests}
                        disabled={hookLoading}
                        size="sm"
                        variant="outline"
                        className="w-full"
                      >
                        Refresh
                      </Button>
                    </div>
                  </div>
                  
                  {pagination && (
                    <div className="p-3 border rounded-lg">
                      <h4 className="font-medium mb-2">Pagination Info:</h4>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>Page: {pagination.page}</div>
                        <div>Limit: {pagination.limit}</div>
                        <div>Total: {pagination.total}</div>
                        <div>Pages: {pagination.totalPages}</div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
