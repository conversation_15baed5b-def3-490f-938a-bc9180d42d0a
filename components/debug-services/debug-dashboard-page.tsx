/**
 * Debug Services - Main Dashboard Page Component
 * 
 * Comprehensive debugging dashboard with system overview and module access
 */

"use client";

import React, { useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Bug, 
  Database, 
  Shield, 
  Activity, 
  Zap, 
  Heart,
  Server,
  Users,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle
} from 'lucide-react';
import Link from 'next/link';
import { 
  DebugServices, 
  useDebugServices
} from '@/lib/debug-services';

interface DebugDashboardPageProps {
  userId: string;
}

export function DebugDashboardPage({ userId }: DebugDashboardPageProps) {
  const { isInitialized } = useDebugServices();

  // Initialize debug services
  useEffect(() => {
    try {
      DebugServices.quickSetup('development');
    } catch (error) {
      console.error('Failed to initialize debug services:', error);
    }
  }, []);

  const debugModules = [
    {
      title: 'System Tests',
      description: 'Database connectivity, server health, and environment validation',
      icon: <Database className="h-6 w-6" />,
      href: '/dashboard/debug/system',
      category: 'Infrastructure',
      color: 'bg-blue-500'
    },
    {
      title: 'Authentication',
      description: 'User sessions, permissions, and security validation',
      icon: <Shield className="h-6 w-6" />,
      href: '/dashboard/debug/auth',
      category: 'Security',
      color: 'bg-green-500'
    },
    {
      title: 'Leave Management',
      description: 'Leave types, requests, and policy debugging',
      icon: <Users className="h-6 w-6" />,
      href: '/dashboard/debug/leave',
      category: 'Application',
      color: 'bg-purple-500'
    },
    {
      title: 'Leave Dashboard',
      description: 'Debug main leave dashboard functionality and data flow',
      icon: <Activity className="h-6 w-6" />,
      href: '/dashboard/debug/leave-dashboard',
      category: 'Application',
      color: 'bg-indigo-500'
    },
    {
      title: 'Leave Requests',
      description: 'Debug leave requests page and API integration',
      icon: <Clock className="h-6 w-6" />,
      href: '/dashboard/debug/leave-requests',
      category: 'Application',
      color: 'bg-teal-500'
    },
    {
      title: 'Performance',
      description: 'Response times, load testing, and optimization',
      icon: <Activity className="h-6 w-6" />,
      href: '/dashboard/debug/performance',
      category: 'Performance',
      color: 'bg-orange-500'
    },
    {
      title: 'API Endpoints',
      description: 'REST API validation and integration testing',
      icon: <Zap className="h-6 w-6" />,
      href: '/dashboard/debug/api',
      category: 'Integration',
      color: 'bg-yellow-500'
    },
    {
      title: 'Health Checks',
      description: 'Comprehensive system health monitoring',
      icon: <Heart className="h-6 w-6" />,
      href: '/dashboard/debug/health',
      category: 'Monitoring',
      color: 'bg-red-500'
    }
  ];

  const systemMetrics = [
    {
      title: 'System Status',
      value: 'Healthy',
      icon: <CheckCircle className="h-5 w-5 text-green-500" />,
      description: 'All systems operational'
    },
    {
      title: 'Active Sessions',
      value: '0',
      icon: <Users className="h-5 w-5 text-blue-500" />,
      description: 'Debug sessions running'
    },
    {
      title: 'Last Check',
      value: 'Just now',
      icon: <Clock className="h-5 w-5 text-gray-500" />,
      description: 'System health verified'
    },
    {
      title: 'Services',
      value: '8/8',
      icon: <Server className="h-5 w-5 text-green-500" />,
      description: 'All services online'
    }
  ];

  return (
    <div className="space-y-6">
      {/* System Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {systemMetrics.map((metric, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {metric.title}
              </CardTitle>
              {metric.icon}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metric.value}</div>
              <p className="text-xs text-muted-foreground">
                {metric.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Debug Modules */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold tracking-tight">Debug Modules</h2>
          <Badge variant="outline" className="text-xs">
            {debugModules.length} modules available
          </Badge>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {debugModules.map((module, index) => (
            <Card key={index} className="group hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${module.color} text-white`}>
                    {module.icon}
                  </div>
                  <div className="flex-1">
                    <CardTitle className="text-lg">{module.title}</CardTitle>
                    <Badge variant="secondary" className="text-xs">
                      {module.category}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="mb-4">
                  {module.description}
                </CardDescription>
                <Button asChild className="w-full">
                  <Link href={module.href}>
                    Open Module
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bug className="h-5 w-5" />
            Quick Actions
          </CardTitle>
          <CardDescription>
            Common debugging tasks and system checks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2" asChild>
              <Link href="/dashboard/debug/health">
                <Heart className="h-6 w-6" />
                <span>System Health</span>
                <span className="text-xs text-muted-foreground">Check overall status</span>
              </Link>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2" asChild>
              <Link href="/dashboard/debug/leave-dashboard">
                <Activity className="h-6 w-6" />
                <span>Leave Dashboard</span>
                <span className="text-xs text-muted-foreground">Debug main dashboard</span>
              </Link>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2" asChild>
              <Link href="/dashboard/debug/leave-requests">
                <Clock className="h-6 w-6" />
                <span>Leave Requests</span>
                <span className="text-xs text-muted-foreground">Debug requests page</span>
              </Link>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2" asChild>
              <Link href="/dashboard/debug/system">
                <Database className="h-6 w-6" />
                <span>Database Check</span>
                <span className="text-xs text-muted-foreground">Verify connectivity</span>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Status Information */}
      <Card>
        <CardHeader>
          <CardTitle>Debug Services Status</CardTitle>
          <CardDescription>
            Current status of the debug services framework
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            {isInitialized ? (
              <>
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm">Debug services initialized and ready</span>
              </>
            ) : (
              <>
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
                <span className="text-sm">Initializing debug services...</span>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
