/**
 * Debug Services - System Debug Page Component
 * 
 * System debugging tools for database, server, and environment validation
 */

"use client";

import React, { useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  Database, 
  Server, 
  Settings, 
  ArrowLeft,
  Activity,
  HardDrive,
  Wifi,
  CheckCircle,
  XCircle,
  AlertTriangle
} from 'lucide-react';
import Link from 'next/link';
import { 
  DebugServices, 
  DebugPanel,
  DebugTestBuilder,
  DebugTestSuiteBuilder,
  CommonTests,
  DebugTestType,
  DebugTestStatus,
  useDebugServices
} from '@/lib/debug-services';

interface SystemDebugPageProps {
  userId: string;
}

export function SystemDebugPage({ userId }: SystemDebugPageProps) {
  const { service, isInitialized } = useDebugServices();

  useEffect(() => {
    if (!isInitialized) {
      try {
        // Initialize debug services
        const debugService = DebugServices.initialize({
          environment: 'development',
          uiEnabled: true,
          allowedRoles: ['super_admin', 'system_admin'],
          maxLogEntries: 2000,
          defaultTimeout: 30000
        });

        // Create system-specific test suite
        const systemTestSuite = createSystemTestSuite();
        
        // Register the test suite
        debugService.registerTestSuite(systemTestSuite);
      } catch (error) {
        console.error('Failed to initialize system debug services:', error);
      }
    }
  }, [isInitialized]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/debug">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Debug Dashboard
            </Link>
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <Badge variant="outline" className="text-xs">
            System Module
          </Badge>
        </div>
      </div>

      {/* System Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Database className="h-4 w-4" />
              Database
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Connected</div>
            <p className="text-xs text-muted-foreground">MongoDB status</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Server className="h-4 w-4" />
              Server
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Healthy</div>
            <p className="text-xs text-muted-foreground">Node.js runtime</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <HardDrive className="h-4 w-4" />
              Memory
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Normal</div>
            <p className="text-xs text-muted-foreground">Usage within limits</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Wifi className="h-4 w-4" />
              Network
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Online</div>
            <p className="text-xs text-muted-foreground">External connectivity</p>
          </CardContent>
        </Card>
      </div>

      {/* Debug Panel */}
      <DebugPanel
        title="System Debug Tests"
        defaultExpanded={true}
        onTestComplete={(result) => {
          console.log('System Test Completed:', result);
        }}
        onSessionComplete={(session) => {
          console.log('System Debug Session Completed:', session);
        }}
      />

      {/* Test Categories */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Database Tests
            </CardTitle>
            <CardDescription>
              MongoDB connectivity and performance validation
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Connection Test</p>
                <p className="text-xs text-muted-foreground">Basic connectivity check</p>
              </div>
              <Badge variant="outline">Database</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Query Performance</p>
                <p className="text-xs text-muted-foreground">Response time validation</p>
              </div>
              <Badge variant="outline">Performance</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Index Health</p>
                <p className="text-xs text-muted-foreground">Database optimization</p>
              </div>
              <Badge variant="outline">Optimization</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Server className="h-5 w-5" />
              Server Tests
            </CardTitle>
            <CardDescription>
              Node.js runtime and system resource validation
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Memory Usage</p>
                <p className="text-xs text-muted-foreground">RAM consumption check</p>
              </div>
              <Badge variant="outline">Resources</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Process Health</p>
                <p className="text-xs text-muted-foreground">Node.js process status</p>
              </div>
              <Badge variant="outline">Runtime</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Environment Config</p>
                <p className="text-xs text-muted-foreground">Configuration validation</p>
              </div>
              <Badge variant="outline">Config</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Quick System Checks
          </CardTitle>
          <CardDescription>
            Run common system validation tests
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Database className="h-6 w-6" />
              <span>Database Check</span>
              <span className="text-xs text-muted-foreground">Test connectivity</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Server className="h-6 w-6" />
              <span>Server Health</span>
              <span className="text-xs text-muted-foreground">Check resources</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Settings className="h-6 w-6" />
              <span>Config Validation</span>
              <span className="text-xs text-muted-foreground">Verify settings</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function createSystemTestSuite() {
  // Database connection test
  const dbConnectionTest = CommonTests.databaseConnectionTest('System Database Connection');

  // Server health test
  const serverHealthTest = DebugTestBuilder
    .create('Server Health Check')
    .description('Comprehensive server health and resource validation')
    .type(DebugTestType.HEALTH_CHECK)
    .category('server')
    .execute(async (context) => {
      try {
        context.logger.info('Running server health check');
        
        const memoryUsage = process.memoryUsage();
        const uptime = process.uptime();
        
        const memoryPercentage = Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100);
        
        let status = DebugTestStatus.SUCCESS;
        let message = `Server healthy - Memory: ${memoryPercentage}%, Uptime: ${Math.round(uptime)}s`;
        
        if (memoryPercentage > 90) {
          status = DebugTestStatus.FAILED;
          message = `Critical memory usage: ${memoryPercentage}%`;
        } else if (memoryPercentage > 75) {
          status = DebugTestStatus.WARNING;
          message = `High memory usage: ${memoryPercentage}%`;
        }
        
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status,
          message,
          details: {
            memory: memoryUsage,
            uptime,
            nodeVersion: process.version,
            platform: process.platform
          },
          timestamp: new Date()
        };
      } catch (error) {
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.FAILED,
          message: error instanceof Error ? error.message : 'Server health check failed',
          timestamp: new Date(),
          error: error instanceof Error ? error : new Error(String(error))
        };
      }
    });

  // Environment configuration test
  const envConfigTest = DebugTestBuilder
    .create('Environment Configuration')
    .description('Validate environment variables and configuration')
    .type(DebugTestType.HEALTH_CHECK)
    .category('config')
    .execute(async (context) => {
      try {
        context.logger.info('Checking environment configuration');
        
        const requiredEnvVars = [
          'MONGODB_URI',
          'NEXTAUTH_SECRET',
          'NODE_ENV'
        ];
        
        const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
        
        if (missingVars.length > 0) {
          return {
            id: context.test.id,
            name: context.test.name,
            type: context.test.type,
            status: DebugTestStatus.FAILED,
            message: `Missing environment variables: ${missingVars.join(', ')}`,
            details: { missingVars, requiredVars: requiredEnvVars },
            timestamp: new Date()
          };
        }
        
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.SUCCESS,
          message: 'All required environment variables are configured',
          details: { 
            configuredVars: requiredEnvVars.length,
            environment: process.env.NODE_ENV 
          },
          timestamp: new Date()
        };
      } catch (error) {
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.FAILED,
          message: error instanceof Error ? error.message : 'Environment configuration check failed',
          timestamp: new Date(),
          error: error instanceof Error ? error : new Error(String(error))
        };
      }
    });

  // Build the test suite
  return DebugTestSuiteBuilder
    .create('System Debug Tests')
    .description('Comprehensive system health and configuration validation')
    .category('system')
    .addTest(dbConnectionTest)
    .addTest(serverHealthTest)
    .addTest(envConfigTest)
    .build();
}
