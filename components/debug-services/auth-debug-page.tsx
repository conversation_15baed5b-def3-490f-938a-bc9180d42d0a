/**
 * Debug Services - Authentication Debug Page Component
 */

"use client";

import React, { useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Shield, ArrowLeft, Activity, Users, Lock, Key } from 'lucide-react';
import Link from 'next/link';
import { DebugServices, DebugPanel, useDebugServices } from '@/lib/debug-services';

interface AuthDebugPageProps {
  userId: string;
}

export function AuthDebugPage({ userId }: AuthDebugPageProps) {
  const { service, isInitialized } = useDebugServices();

  useEffect(() => {
    if (!isInitialized) {
      try {
        DebugServices.initialize({
          environment: 'development',
          uiEnabled: true,
          allowedRoles: ['super_admin', 'system_admin'],
          maxLogEntries: 2000,
          defaultTimeout: 30000
        });
      } catch (error) {
        console.error('Failed to initialize auth debug services:', error);
      }
    }
  }, [isInitialized]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/debug">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Debug Dashboard
            </Link>
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <Badge variant="outline" className="text-xs">
            Authentication Module
          </Badge>
        </div>
      </div>

      {/* Auth Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Sessions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Active</div>
            <p className="text-xs text-muted-foreground">User sessions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Users className="h-4 w-4" />
              Permissions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Valid</div>
            <p className="text-xs text-muted-foreground">Role-based access</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Lock className="h-4 w-4" />
              Security
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Secure</div>
            <p className="text-xs text-muted-foreground">Token validation</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Key className="h-4 w-4" />
              Auth Flow
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Working</div>
            <p className="text-xs text-muted-foreground">Login/logout flow</p>
          </CardContent>
        </Card>
      </div>

      {/* Debug Panel */}
      <DebugPanel
        title="Authentication Debug Tests"
        defaultExpanded={true}
        onTestComplete={(result) => {
          console.log('Auth Test Completed:', result);
        }}
        onSessionComplete={(session) => {
          console.log('Auth Debug Session Completed:', session);
        }}
      />

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Quick Authentication Tests
          </CardTitle>
          <CardDescription>
            Run common authentication debugging tasks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Shield className="h-6 w-6" />
              <span>Test Sessions</span>
              <span className="text-xs text-muted-foreground">Validate user sessions</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Users className="h-6 w-6" />
              <span>Check Permissions</span>
              <span className="text-xs text-muted-foreground">Role validation</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Lock className="h-6 w-6" />
              <span>Security Audit</span>
              <span className="text-xs text-muted-foreground">Security checks</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
