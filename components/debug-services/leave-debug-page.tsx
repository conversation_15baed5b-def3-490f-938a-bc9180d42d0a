/**
 * Debug Services - Leave Management Debug Page Component
 * 
 * Debug tools for leave types, requests, and policy validation
 */

"use client";

import React, { useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  Users, 
  Calendar, 
  Settings, 
  ArrowLeft,
  Activity,
  Database,
  CheckCircle,
  XCircle,
  AlertTriangle,
  FileText
} from 'lucide-react';
import Link from 'next/link';
import { 
  DebugServices, 
  DebugPanel,
  useDebugServices
} from '@/lib/debug-services';

interface LeaveDebugPageProps {
  userId: string;
}

export function LeaveDebugPage({ userId }: LeaveDebugPageProps) {
  const { service, isInitialized } = useDebugServices();

  useEffect(() => {
    if (!isInitialized) {
      try {
        // Initialize debug services
        DebugServices.initialize({
          environment: 'development',
          uiEnabled: true,
          allowedRoles: ['super_admin', 'system_admin', 'hr_director', 'hr_manager'],
          maxLogEntries: 2000,
          defaultTimeout: 30000
        });
      } catch (error) {
        console.error('Failed to initialize leave debug services:', error);
      }
    }
  }, [isInitialized]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/debug">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Debug Dashboard
            </Link>
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <Badge variant="outline" className="text-xs">
            Leave Management Module
          </Badge>
        </div>
      </div>

      {/* Leave Management Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Leave Types
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">API Ready</div>
            <p className="text-xs text-muted-foreground">Endpoint status</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Requests
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Functional</div>
            <p className="text-xs text-muted-foreground">Request processing</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Database className="h-4 w-4" />
              Database
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Connected</div>
            <p className="text-xs text-muted-foreground">Data access</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Policies
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Active</div>
            <p className="text-xs text-muted-foreground">Policy engine</p>
          </CardContent>
        </Card>
      </div>

      {/* Debug Panel */}
      <DebugPanel
        title="Leave Management Debug Tests"
        defaultExpanded={true}
        onTestComplete={(result) => {
          console.log('Leave Management Test Completed:', result);
        }}
        onSessionComplete={(session) => {
          console.log('Leave Management Debug Session Completed:', session);
        }}
      />

      {/* Test Categories */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Leave Types Tests
            </CardTitle>
            <CardDescription>
              Debug leave types API, seeding, and data validation
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">API Endpoint Test</p>
                <p className="text-xs text-muted-foreground">Test /api/leave/types</p>
              </div>
              <Badge variant="outline">API</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Seed Functionality</p>
                <p className="text-xs text-muted-foreground">Test leave types seeding</p>
              </div>
              <Badge variant="outline">Database</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Data Validation</p>
                <p className="text-xs text-muted-foreground">Schema and rules validation</p>
              </div>
              <Badge variant="outline">Validation</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Request Processing Tests
            </CardTitle>
            <CardDescription>
              Debug leave request creation, approval, and workflow
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Request Creation</p>
                <p className="text-xs text-muted-foreground">Test request submission</p>
              </div>
              <Badge variant="outline">Workflow</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Approval Process</p>
                <p className="text-xs text-muted-foreground">Test approval workflow</p>
              </div>
              <Badge variant="outline">Business Logic</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Balance Calculation</p>
                <p className="text-xs text-muted-foreground">Test leave balance logic</p>
              </div>
              <Badge variant="outline">Calculation</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Quick Leave Management Tests
          </CardTitle>
          <CardDescription>
            Run common leave management debugging tasks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <FileText className="h-6 w-6" />
              <span>Test Leave Types</span>
              <span className="text-xs text-muted-foreground">Debug seeding issue</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Calendar className="h-6 w-6" />
              <span>Test Requests</span>
              <span className="text-xs text-muted-foreground">Validate workflow</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Database className="h-6 w-6" />
              <span>Check Database</span>
              <span className="text-xs text-muted-foreground">Verify data integrity</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Leave Types Specific Debugging */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-500" />
            Leave Types Debugging
          </CardTitle>
          <CardDescription>
            Specific tools for debugging the leave types seeding issue
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h4 className="font-medium text-sm mb-2">Common Issues & Solutions</h4>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• Check if leave types API endpoint is accessible</li>
              <li>• Verify database connection and permissions</li>
              <li>• Test leave types seeding with proper authentication</li>
              <li>• Validate leave type schema and required fields</li>
            </ul>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button variant="outline" className="justify-start">
              <CheckCircle className="h-4 w-4 mr-2" />
              Test Leave Types API
            </Button>
            <Button variant="outline" className="justify-start">
              <Database className="h-4 w-4 mr-2" />
              Run Seed Test
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
