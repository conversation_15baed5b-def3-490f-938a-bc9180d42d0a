/**
 * Simple Leave Management Debug Page
 *
 * Direct debugging tools without complex framework dependencies
 */

"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Calendar,
  ArrowLeft,
  Activity,
  Database,
  CheckCircle,
  XCircle,
  AlertTriangle,
  FileText,
  Loader2,
  RefreshCw,
  Eye
} from 'lucide-react';
import Link from 'next/link';
import { useToast } from '@/components/ui/use-toast';
import { useLeaveTypes } from '@/hooks/use-leave-types';
import { useLeaveRequests } from '@/hooks/use-leave-requests';

interface LeaveDebugPageProps {
  userId: string;
}

interface DebugTest {
  name: string;
  status: 'pending' | 'running' | 'success' | 'error';
  result?: string;
  error?: string;
}

export function LeaveDebugPage({ }: LeaveDebugPageProps) {
  const [tests, setTests] = useState<DebugTest[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const { toast } = useToast();

  const {
    leaveTypes,
    loading: typesLoading,
    error: typesError,
    refreshLeaveTypes
  } = useLeaveTypes({
    activeOnly: false,
    autoFetch: false
  });

  const {
    requests,
    loading: requestsLoading,
    error: requestsError,
    refreshRequests
  } = useLeaveRequests({
    initialParams: { page: 1, limit: 10 },
    autoFetch: false
  });

  const updateTest = (name: string, updates: Partial<DebugTest>) => {
    setTests(prev => prev.map(test =>
      test.name === name ? { ...test, ...updates } : test
    ));
  };

  const runTest = async (testName: string, testFn: () => Promise<any>) => {
    updateTest(testName, { status: 'running' });
    try {
      const result = await testFn();
      updateTest(testName, {
        status: 'success',
        result: typeof result === 'string' ? result : JSON.stringify(result, null, 2)
      });
      return result;
    } catch (error) {
      updateTest(testName, {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  };

  const initializeTests = () => {
    const testList: DebugTest[] = [
      { name: 'Database Connection', status: 'pending' },
      { name: 'Authentication Check', status: 'pending' },
      { name: 'Leave Types API', status: 'pending' },
      { name: 'Leave Requests API', status: 'pending' },
      { name: 'Employees API', status: 'pending' },
      { name: 'Seed Leave Types', status: 'pending' },
    ];
    setTests(testList);
  };

  const testDatabaseConnection = async () => {
    const response = await fetch('/api/debug/database');
    const data = await response.json();
    if (!response.ok) throw new Error(data.error || 'Database connection failed');
    return `Connected to: ${data.database}`;
  };

  const testAuthentication = async () => {
    const response = await fetch('/api/auth/session');
    const data = await response.json();
    if (!response.ok) throw new Error('Authentication failed');
    return `Authenticated as: ${data.user?.email} (${data.user?.role})`;
  };

  const testLeaveTypesAPI = async () => {
    const response = await fetch('/api/leave/types');
    const data = await response.json();
    if (!response.ok) throw new Error(data.error || 'Leave types API failed');
    return `Found ${data.data?.length || 0} leave types`;
  };

  const testLeaveRequestsAPI = async () => {
    const response = await fetch('/api/leave/requests?page=1&limit=5');
    const data = await response.json();
    if (!response.ok) throw new Error(data.error || 'Leave requests API failed');
    return `Found ${data.data?.length || 0} leave requests (Total: ${data.pagination?.total || 0})`;
  };

  const testEmployeesAPI = async () => {
    const response = await fetch('/api/employees?page=1&limit=5');
    const data = await response.json();
    if (!response.ok) throw new Error(data.error || 'Employees API failed');
    return `Found ${data.data?.length || 0} employees (Total: ${data.pagination?.total || 0})`;
  };

  const testSeedLeaveTypes = async () => {
    const response = await fetch('/api/admin/seed/leave-types', {
      method: 'POST',
    });
    const data = await response.json();
    if (!response.ok) throw new Error(data.error || 'Seeding failed');
    return data.message || 'Leave types seeded successfully';
  };

  const runAllTests = async () => {
    setIsRunning(true);
    try {
      await runTest('Database Connection', testDatabaseConnection);
      await runTest('Authentication Check', testAuthentication);
      await runTest('Leave Types API', testLeaveTypesAPI);
      await runTest('Leave Requests API', testLeaveRequestsAPI);
      await runTest('Employees API', testEmployeesAPI);
      await runTest('Seed Leave Types', testSeedLeaveTypes);

      toast({
        title: "Debug Tests Complete",
        description: "All tests have been executed. Check results below.",
      });
    } catch (error) {
      toast({
        title: "Debug Tests Failed",
        description: "Some tests failed. Check the results for details.",
        variant: "destructive",
      });
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: DebugTest['status']) => {
    switch (status) {
      case 'pending': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'running': return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error': return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  useEffect(() => {
    initializeTests();
  }, []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/debug">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Debug Dashboard
            </Link>
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <Badge variant="outline" className="text-xs">
            Leave Management Module
          </Badge>
        </div>
        <Button
          onClick={runAllTests}
          disabled={isRunning}
          className="gap-2"
        >
          {isRunning ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4" />
          )}
          Run All Tests
        </Button>
      </div>

      {/* Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Leave Types
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{leaveTypes.length}</div>
            <p className="text-xs text-muted-foreground">
              {typesLoading ? 'Loading...' : typesError ? 'Error' : 'Available types'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Requests
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{requests.length}</div>
            <p className="text-xs text-muted-foreground">
              {requestsLoading ? 'Loading...' : requestsError ? 'Error' : 'Total requests'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Database className="h-4 w-4" />
              Database
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {typesError || requestsError ? 'Error' : 'OK'}
            </div>
            <p className="text-xs text-muted-foreground">Connection status</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Tests
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {tests.filter(t => t.status === 'success').length}/{tests.length}
            </div>
            <p className="text-xs text-muted-foreground">Passed tests</p>
          </CardContent>
        </Card>
      </div>

      {/* Test Results */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Debug Test Results
          </CardTitle>
          <CardDescription>
            Comprehensive testing of leave management functionality
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {tests.map((test) => (
              <Card key={test.name}>
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(test.status)}
                      <span className="font-medium">{test.name}</span>
                    </div>
                    <Badge variant={
                      test.status === 'success' ? 'default' :
                      test.status === 'error' ? 'destructive' :
                      test.status === 'running' ? 'secondary' : 'outline'
                    }>
                      {test.status}
                    </Badge>
                  </div>

                  {test.result && (
                    <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded text-sm">
                      <pre className="whitespace-pre-wrap">{test.result}</pre>
                    </div>
                  )}

                  {test.error && (
                    <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                      {test.error}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Data Inspection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Data Inspection
          </CardTitle>
          <CardDescription>
            Inspect current data from hooks and APIs
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="types" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="types">Leave Types</TabsTrigger>
              <TabsTrigger value="requests">Requests</TabsTrigger>
              <TabsTrigger value="controls">Controls</TabsTrigger>
            </TabsList>

            <TabsContent value="types" className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Count: {leaveTypes.length}</Badge>
                  <Badge variant={typesLoading ? "secondary" : "outline"}>
                    {typesLoading ? "Loading" : "Ready"}
                  </Badge>
                  {typesError && <Badge variant="destructive">Error</Badge>}
                </div>
                <Button onClick={refreshLeaveTypes} disabled={typesLoading} size="sm">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>

              {typesError && (
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertDescription>{typesError}</AlertDescription>
                </Alert>
              )}

              {leaveTypes.length > 0 ? (
                <div className="space-y-2">
                  {leaveTypes.slice(0, 5).map((type) => (
                    <div key={type._id.toString()} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: type.color }}
                          />
                          <span className="font-medium">{type.name}</span>
                          <Badge variant="outline">{type.code}</Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={type.isActive ? "default" : "secondary"}>
                            {type.isActive ? "Active" : "Inactive"}
                          </Badge>
                          <Badge variant="outline">{type.defaultDays} days</Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                  {leaveTypes.length > 5 && (
                    <p className="text-sm text-muted-foreground text-center">
                      ... and {leaveTypes.length - 5} more
                    </p>
                  )}
                </div>
              ) : (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    No leave types found. Try running the "Seed Leave Types" test.
                  </AlertDescription>
                </Alert>
              )}
            </TabsContent>

            <TabsContent value="requests" className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Count: {requests.length}</Badge>
                  <Badge variant={requestsLoading ? "secondary" : "outline"}>
                    {requestsLoading ? "Loading" : "Ready"}
                  </Badge>
                  {requestsError && <Badge variant="destructive">Error</Badge>}
                </div>
                <Button onClick={refreshRequests} disabled={requestsLoading} size="sm">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>

              {requestsError && (
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertDescription>{requestsError}</AlertDescription>
                </Alert>
              )}

              {requests.length > 0 ? (
                <div className="space-y-2">
                  {requests.slice(0, 3).map((request) => (
                    <div key={request._id.toString()} className="p-3 border rounded-lg">
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div><strong>Status:</strong> {request.status}</div>
                        <div><strong>Duration:</strong> {request.duration} days</div>
                        <div><strong>Employee:</strong> {JSON.stringify(request.employeeId).slice(0, 50)}...</div>
                        <div><strong>Leave Type:</strong> {JSON.stringify(request.leaveTypeId).slice(0, 50)}...</div>
                      </div>
                    </div>
                  ))}
                  {requests.length > 3 && (
                    <p className="text-sm text-muted-foreground text-center">
                      ... and {requests.length - 3} more
                    </p>
                  )}
                </div>
              ) : (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    No leave requests found. This could indicate the database is empty or there are API issues.
                  </AlertDescription>
                </Alert>
              )}
            </TabsContent>

            <TabsContent value="controls" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button
                  onClick={() => refreshLeaveTypes()}
                  disabled={typesLoading}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center gap-2"
                >
                  <FileText className="h-6 w-6" />
                  <span>Refresh Leave Types</span>
                  <span className="text-xs text-muted-foreground">Reload from API</span>
                </Button>

                <Button
                  onClick={() => refreshRequests()}
                  disabled={requestsLoading}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center gap-2"
                >
                  <Calendar className="h-6 w-6" />
                  <span>Refresh Requests</span>
                  <span className="text-xs text-muted-foreground">Reload from API</span>
                </Button>
              </div>

              <div className="grid grid-cols-1 gap-4">
                <Button
                  onClick={runAllTests}
                  disabled={isRunning}
                  className="h-auto p-4 flex flex-col items-center gap-2"
                >
                  {isRunning ? (
                    <Loader2 className="h-6 w-6 animate-spin" />
                  ) : (
                    <Activity className="h-6 w-6" />
                  )}
                  <span>Run All Debug Tests</span>
                  <span className="text-xs text-muted-foreground">
                    {isRunning ? 'Running tests...' : 'Test all functionality'}
                  </span>
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

    </div>
  );
}
