"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Loader2, 
  RefreshCw,
  Database,
  Users,
  Calendar,
  FileText,
  BarChart3,
  Bug
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useLeaveRequests } from '@/hooks/use-leave-requests';
import { useLeaveTypes } from '@/hooks/use-leave-types';
import { useEmployeeLeaveBalances } from '@/hooks/use-employee-leave-balances';

interface DebugTest {
  name: string;
  status: 'pending' | 'running' | 'success' | 'error';
  result?: string;
  error?: string;
  data?: any;
}

export function LeaveDashboardDebug() {
  const [tests, setTests] = useState<DebugTest[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const { toast } = useToast();

  // Test all the hooks used in the main dashboard
  const {
    requests,
    loading: requestsLoading,
    error: requestsError,
    refreshRequests
  } = useLeaveRequests({
    initialParams: { page: 1, limit: 20 },
    autoFetch: false
  });

  const {
    leaveTypes,
    loading: typesLoading,
    error: typesError,
    refreshLeaveTypes
  } = useLeaveTypes({
    activeOnly: false,
    autoFetch: false
  });

  const {
    balances,
    loading: balancesLoading,
    error: balancesError,
    refreshBalances
  } = useEmployeeLeaveBalances({
    autoFetch: false
  });

  const updateTest = (name: string, updates: Partial<DebugTest>) => {
    setTests(prev => prev.map(test => 
      test.name === name ? { ...test, ...updates } : test
    ));
  };

  const runTest = async (testName: string, testFn: () => Promise<any>) => {
    updateTest(testName, { status: 'running' });
    try {
      const result = await testFn();
      updateTest(testName, { 
        status: 'success', 
        result: typeof result === 'string' ? result : JSON.stringify(result, null, 2),
        data: result
      });
      return result;
    } catch (error) {
      updateTest(testName, { 
        status: 'error', 
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  };

  const initializeTests = () => {
    const testList: DebugTest[] = [
      { name: 'Dashboard API Endpoint', status: 'pending' },
      { name: 'Leave Requests Hook', status: 'pending' },
      { name: 'Leave Types Hook', status: 'pending' },
      { name: 'Leave Balances Hook', status: 'pending' },
      { name: 'Dashboard Statistics', status: 'pending' },
      { name: 'Recent Requests', status: 'pending' },
      { name: 'Leave Calendar Data', status: 'pending' },
      { name: 'Data Consistency', status: 'pending' },
    ];
    setTests(testList);
  };

  const testDashboardAPI = async () => {
    const response = await fetch('/api/dashboard/leave');
    const data = await response.json();
    if (!response.ok) throw new Error(data.error || 'Dashboard API failed');
    return {
      hasStats: !!data.stats,
      hasRequests: !!data.recentRequests,
      hasCalendar: !!data.calendarData,
      statsCount: Object.keys(data.stats || {}).length,
      requestsCount: data.recentRequests?.length || 0
    };
  };

  const testLeaveRequestsHook = async () => {
    await refreshRequests();
    return {
      loading: requestsLoading,
      error: requestsError,
      requestsCount: requests.length,
      hasData: requests.length > 0,
      sampleRequest: requests[0] || null
    };
  };

  const testLeaveTypesHook = async () => {
    await refreshLeaveTypes();
    return {
      loading: typesLoading,
      error: typesError,
      typesCount: leaveTypes.length,
      hasData: leaveTypes.length > 0,
      activeTypes: leaveTypes.filter(t => t.isActive).length,
      sampleType: leaveTypes[0] || null
    };
  };

  const testLeaveBalancesHook = async () => {
    await refreshBalances();
    return {
      loading: balancesLoading,
      error: balancesError,
      balancesCount: balances.length,
      hasData: balances.length > 0,
      sampleBalance: balances[0] || null
    };
  };

  const testDashboardStatistics = async () => {
    // Test the statistics calculation
    const pendingRequests = requests.filter(r => r.status === 'pending').length;
    const approvedRequests = requests.filter(r => r.status === 'approved').length;
    const rejectedRequests = requests.filter(r => r.status === 'rejected').length;
    
    return {
      totalRequests: requests.length,
      pendingRequests,
      approvedRequests,
      rejectedRequests,
      activeLeaveTypes: leaveTypes.filter(t => t.isActive).length,
      totalLeaveTypes: leaveTypes.length,
      employeesWithBalances: balances.length
    };
  };

  const testRecentRequests = async () => {
    // Test recent requests logic
    const sortedRequests = [...requests].sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
    const recentRequests = sortedRequests.slice(0, 5);
    
    return {
      totalRequests: requests.length,
      recentCount: recentRequests.length,
      hasRecent: recentRequests.length > 0,
      oldestRecent: recentRequests[recentRequests.length - 1]?.createdAt || null
    };
  };

  const testLeaveCalendarData = async () => {
    // Test calendar data preparation
    const approvedRequests = requests.filter(r => r.status === 'approved');
    const calendarEvents = approvedRequests.map(request => ({
      id: request._id,
      title: `${(request.employeeId as any)?.firstName || 'Unknown'} - ${(request.leaveTypeId as any)?.name || 'Leave'}`,
      start: request.startDate,
      end: request.endDate,
      type: (request.leaveTypeId as any)?.name || 'Unknown'
    }));
    
    return {
      totalRequests: requests.length,
      approvedRequests: approvedRequests.length,
      calendarEvents: calendarEvents.length,
      hasEvents: calendarEvents.length > 0,
      sampleEvent: calendarEvents[0] || null
    };
  };

  const testDataConsistency = async () => {
    // Test data consistency across hooks
    const issues = [];
    
    // Check if requests reference valid leave types
    const leaveTypeIds = new Set(leaveTypes.map(t => t._id.toString()));
    const invalidTypeRefs = requests.filter(r => {
      const typeId = typeof r.leaveTypeId === 'object' ? r.leaveTypeId._id : r.leaveTypeId;
      return !leaveTypeIds.has(typeId?.toString());
    });
    
    if (invalidTypeRefs.length > 0) {
      issues.push(`${invalidTypeRefs.length} requests reference invalid leave types`);
    }
    
    // Check for missing employee data
    const missingEmployeeData = requests.filter(r => !r.employeeId);
    if (missingEmployeeData.length > 0) {
      issues.push(`${missingEmployeeData.length} requests missing employee data`);
    }
    
    return {
      totalRequests: requests.length,
      totalTypes: leaveTypes.length,
      totalBalances: balances.length,
      issues: issues,
      isConsistent: issues.length === 0
    };
  };

  const runAllTests = async () => {
    setIsRunning(true);
    try {
      await runTest('Dashboard API Endpoint', testDashboardAPI);
      await runTest('Leave Requests Hook', testLeaveRequestsHook);
      await runTest('Leave Types Hook', testLeaveTypesHook);
      await runTest('Leave Balances Hook', testLeaveBalancesHook);
      await runTest('Dashboard Statistics', testDashboardStatistics);
      await runTest('Recent Requests', testRecentRequests);
      await runTest('Leave Calendar Data', testLeaveCalendarData);
      await runTest('Data Consistency', testDataConsistency);
      
      toast({
        title: "Dashboard Debug Complete",
        description: "All dashboard tests have been executed.",
      });
    } catch (error) {
      toast({
        title: "Dashboard Debug Failed",
        description: "Some tests failed. Check the results for details.",
        variant: "destructive",
      });
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: DebugTest['status']) => {
    switch (status) {
      case 'pending': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'running': return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error': return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  useEffect(() => {
    initializeTests();
  }, []);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Bug className="h-5 w-5" />
                Leave Dashboard Debug Console
              </CardTitle>
              <CardDescription>
                Comprehensive testing and debugging for the main leave dashboard
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                onClick={runAllTests}
                disabled={isRunning}
                className="gap-2"
              >
                {isRunning ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
                Run All Tests
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="tests" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="tests">Test Results</TabsTrigger>
              <TabsTrigger value="hooks">Hook Status</TabsTrigger>
              <TabsTrigger value="data">Raw Data</TabsTrigger>
              <TabsTrigger value="stats">Statistics</TabsTrigger>
            </TabsList>
            
            <TabsContent value="tests" className="space-y-4">
              <div className="grid gap-4">
                {tests.map((test) => (
                  <Card key={test.name}>
                    <CardContent className="pt-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(test.status)}
                          <span className="font-medium">{test.name}</span>
                        </div>
                        <Badge variant={
                          test.status === 'success' ? 'default' :
                          test.status === 'error' ? 'destructive' :
                          test.status === 'running' ? 'secondary' : 'outline'
                        }>
                          {test.status}
                        </Badge>
                      </div>
                      
                      {test.result && (
                        <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded text-sm">
                          <pre className="whitespace-pre-wrap">{test.result}</pre>
                        </div>
                      )}
                      
                      {test.error && (
                        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                          {test.error}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="hooks" className="space-y-4">
              <div className="grid gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Leave Requests Hook
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Badge variant={requestsLoading ? "secondary" : "outline"}>
                          Loading: {requestsLoading ? "Yes" : "No"}
                        </Badge>
                        <Badge variant={requestsError ? "destructive" : "outline"}>
                          Error: {requestsError || "None"}
                        </Badge>
                        <Badge variant="outline">Count: {requests.length}</Badge>
                      </div>
                      <Button onClick={refreshRequests} disabled={requestsLoading} size="sm">
                        Refresh Requests
                      </Button>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Leave Types Hook
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Badge variant={typesLoading ? "secondary" : "outline"}>
                          Loading: {typesLoading ? "Yes" : "No"}
                        </Badge>
                        <Badge variant={typesError ? "destructive" : "outline"}>
                          Error: {typesError || "None"}
                        </Badge>
                        <Badge variant="outline">Count: {leaveTypes.length}</Badge>
                      </div>
                      <Button onClick={refreshLeaveTypes} disabled={typesLoading} size="sm">
                        Refresh Types
                      </Button>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart3 className="h-4 w-4" />
                      Leave Balances Hook
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Badge variant={balancesLoading ? "secondary" : "outline"}>
                          Loading: {balancesLoading ? "Yes" : "No"}
                        </Badge>
                        <Badge variant={balancesError ? "destructive" : "outline"}>
                          Error: {balancesError || "None"}
                        </Badge>
                        <Badge variant="outline">Count: {balances.length}</Badge>
                      </div>
                      <Button onClick={refreshBalances} disabled={balancesLoading} size="sm">
                        Refresh Balances
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="data" className="space-y-4">
              <Alert>
                <Database className="h-4 w-4" />
                <AlertDescription>
                  Raw data from all hooks used in the dashboard. This shows exactly what data is being fetched.
                </AlertDescription>
              </Alert>
              
              <div className="grid gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Leave Requests ({requests.length})</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {requests.length > 0 ? (
                      <pre className="text-xs bg-gray-50 p-2 rounded overflow-auto max-h-40">
                        {JSON.stringify(requests.slice(0, 2), null, 2)}
                      </pre>
                    ) : (
                      <p className="text-muted-foreground">No requests found</p>
                    )}
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Leave Types ({leaveTypes.length})</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {leaveTypes.length > 0 ? (
                      <pre className="text-xs bg-gray-50 p-2 rounded overflow-auto max-h-40">
                        {JSON.stringify(leaveTypes.slice(0, 3), null, 2)}
                      </pre>
                    ) : (
                      <p className="text-muted-foreground">No leave types found</p>
                    )}
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Leave Balances ({balances.length})</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {balances.length > 0 ? (
                      <pre className="text-xs bg-gray-50 p-2 rounded overflow-auto max-h-40">
                        {JSON.stringify(balances.slice(0, 2), null, 2)}
                      </pre>
                    ) : (
                      <p className="text-muted-foreground">No balances found</p>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="stats" className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="pt-4">
                    <div className="text-2xl font-bold">{requests.length}</div>
                    <p className="text-xs text-muted-foreground">Total Requests</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-4">
                    <div className="text-2xl font-bold">{requests.filter(r => r.status === 'pending').length}</div>
                    <p className="text-xs text-muted-foreground">Pending</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-4">
                    <div className="text-2xl font-bold">{leaveTypes.length}</div>
                    <p className="text-xs text-muted-foreground">Leave Types</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-4">
                    <div className="text-2xl font-bold">{balances.length}</div>
                    <p className="text-xs text-muted-foreground">Balances</p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
