/**
 * Debug Services - Health Debug Page Component
 */

"use client";

import React, { useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Heart, ArrowLeft, Activity, Database, Server, Wifi } from 'lucide-react';
import Link from 'next/link';
import { DebugServices, DebugPanel, useDebugServices } from '@/lib/debug-services';

interface HealthDebugPageProps {
  userId: string;
}

export function HealthDebugPage({ userId }: HealthDebugPageProps) {
  const { service, isInitialized } = useDebugServices();

  useEffect(() => {
    if (!isInitialized) {
      try {
        DebugServices.initialize({
          environment: 'development',
          uiEnabled: true,
          allowedRoles: ['super_admin', 'system_admin', 'hr_director', 'hr_manager'],
          maxLogEntries: 2000,
          defaultTimeout: 30000
        });
      } catch (error) {
        console.error('Failed to initialize health debug services:', error);
      }
    }
  }, [isInitialized]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/debug">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Debug Dashboard
            </Link>
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <Badge variant="outline" className="text-xs">
            Health Module
          </Badge>
        </div>
      </div>

      {/* Health Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Heart className="h-4 w-4" />
              System Health
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Healthy</div>
            <p className="text-xs text-muted-foreground">Overall status</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Database className="h-4 w-4" />
              Database
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Connected</div>
            <p className="text-xs text-muted-foreground">DB health</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Server className="h-4 w-4" />
              Server
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Running</div>
            <p className="text-xs text-muted-foreground">Server health</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Wifi className="h-4 w-4" />
              Services
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Online</div>
            <p className="text-xs text-muted-foreground">Service health</p>
          </CardContent>
        </Card>
      </div>

      {/* Debug Panel */}
      <DebugPanel
        title="Health Check Debug Tests"
        defaultExpanded={true}
        onTestComplete={(result) => {
          console.log('Health Check Test Completed:', result);
        }}
        onSessionComplete={(session) => {
          console.log('Health Check Debug Session Completed:', session);
        }}
      />

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Quick Health Checks
          </CardTitle>
          <CardDescription>
            Run comprehensive system health diagnostics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Heart className="h-6 w-6" />
              <span>System Health</span>
              <span className="text-xs text-muted-foreground">Overall status</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Database className="h-6 w-6" />
              <span>Database Check</span>
              <span className="text-xs text-muted-foreground">DB connectivity</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Server className="h-6 w-6" />
              <span>Resource Monitor</span>
              <span className="text-xs text-muted-foreground">Memory & CPU</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
