"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, <PERSON>, Tooltip } from "recharts"
import { <PERSON><PERSON> } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { useEffect, useState, useCallback } from "react"
import { toast } from "@/components/ui/use-toast"
import {
  RefreshCw,
  AlertCircle,
  TrendingUp,
  TrendingDown,
  Minus,
  Users,
  Building
} from "lucide-react"
import { EmptyState, useEmptyState } from "@/components/ui/empty-state"
import { EmployeeDistributionAnalytics, DepartmentBreakdownData } from "@/lib/services/dashboard/department-analytics-service"
import { apiGet, ApiError } from "@/lib/utils/api-error-handler"

interface DepartmentBreakdownProps {
  className?: string
}

// Remove static data - will be replaced with real API data

export function DepartmentBreakdown({ className }: DepartmentBreakdownProps) {
  const [departmentData, setDepartmentData] = useState<EmployeeDistributionAnalytics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())
  const { getEmptyState } = useEmptyState()

  const fetchDepartmentData = useCallback(async (showToast = false) => {
    setIsLoading(true)
    setError(null)

    try {
      const data = await apiGet('/api/dashboard/organization?type=departments')

      if (data.success && data.data?.departments) {
        setDepartmentData(data.data.departments)
        setLastRefresh(new Date())

        if (showToast) {
          toast({
            title: "Department Data Updated",
            description: `Loaded data for ${data.data.departments.totalDepartments} departments`,
            variant: "default",
          })
        }
      } else {
        throw new ApiError('Invalid response format', 500)
      }

    } catch (err) {
      console.error('Error fetching department data:', err)

      let errorMessage = 'Failed to load department data'
      if (err instanceof ApiError) {
        errorMessage = err.message
      } else if (err instanceof Error) {
        errorMessage = err.message
      }

      setError(errorMessage)

      if (showToast) {
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        })
      }
    } finally {
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchDepartmentData()

    // Set up auto-refresh every 5 minutes
    const interval = setInterval(() => {
      fetchDepartmentData()
    }, 300000)

    return () => clearInterval(interval)
  }, [fetchDepartmentData])

  const handleRefresh = () => {
    fetchDepartmentData(true)
  }

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-3 w-3 text-green-600" />
      case 'down':
        return <TrendingDown className="h-3 w-3 text-red-600" />
      case 'stable':
        return <Minus className="h-3 w-3 text-gray-600" />
      default:
        return <Minus className="h-3 w-3 text-gray-600" />
    }
  }

  const formatChartData = (departments: DepartmentBreakdownData[]) => {
    return departments.map(dept => ({
      name: dept.name,
      value: dept.employeeCount,
      color: dept.color,
      percentage: dept.percentage
    }))
  }

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              Department Breakdown
              {departmentData && (
                <Badge variant="secondary" className="text-xs">
                  {departmentData.totalDepartments} departments
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Employee distribution across departments
              {lastRefresh && (
                <span className="text-xs text-muted-foreground ml-2">
                  • Updated {lastRefresh.toLocaleTimeString()}
                </span>
              )}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {departmentData && (
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  <span>{departmentData.totalEmployees} employees</span>
                </div>
                <div className="flex items-center gap-1">
                  <Building className="h-3 w-3" />
                  <span>Avg {departmentData.averageEmployeesPerDepartment}/dept</span>
                </div>
              </div>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-[240px] w-full" />
            <div className="grid grid-cols-2 gap-4 sm:grid-cols-3">
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Skeleton className="h-3 w-3 rounded-full" />
                  <div className="flex flex-col gap-1">
                    <Skeleton className="h-3 w-16" />
                    <Skeleton className="h-2 w-12" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : getEmptyState(
          isLoading,
          error,
          departmentData && departmentData.departmentBreakdown.length > 0,
          {
            noDataTitle: "No department data found",
            noDataDescription: "Department information will appear here when available",
            onRetry: handleRefresh,
            icon: Building
          }
        ) || (
          <>
            <div className="h-[240px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={formatChartData(departmentData.departmentBreakdown)}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={90}
                    paddingAngle={2}
                    dataKey="value"
                    label={({ name, percentage }) => `${name} ${percentage}%`}
                    labelLine={false}
                  >
                    {departmentData.departmentBreakdown.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value) => [`${value} employees`, "Count"]}
                    contentStyle={{
                      backgroundColor: "hsl(var(--background))",
                      borderColor: "hsl(var(--border))",
                      borderRadius: "0.5rem",
                      boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
                    }}
                  />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="mt-4 grid grid-cols-2 gap-4 sm:grid-cols-3">
              {departmentData.departmentBreakdown.map((dept) => (
                <div key={dept.id} className="flex items-center gap-2 p-2 rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="h-3 w-3 rounded-full" style={{ backgroundColor: dept.color }}></div>
                  <div className="flex flex-col flex-1">
                    <div className="flex items-center gap-1">
                      <span className="text-xs font-medium">{dept.name}</span>
                      {dept.growth.trend !== 'stable' && (
                        <div className="flex items-center gap-1">
                          {getTrendIcon(dept.growth.trend)}
                          <span className={cn(
                            "text-xs",
                            dept.growth.trend === 'up' ? "text-green-600" : "text-red-600"
                          )}>
                            {dept.growth.value > 0 ? '+' : ''}{dept.growth.value}
                          </span>
                        </div>
                      )}
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {dept.employeeCount} employee{dept.employeeCount !== 1 ? 's' : ''} ({dept.percentage}%)
                    </span>
                    {dept.averageTenure > 0 && (
                      <span className="text-xs text-muted-foreground">
                        Avg tenure: {dept.averageTenure} months
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Additional insights */}
            {departmentData.recentHires.thisMonth > 0 || departmentData.recentTerminations.thisMonth > 0 ? (
              <div className="mt-4 pt-4 border-t">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div className="space-y-1">
                    <div className="text-sm font-medium text-green-600">New Hires</div>
                    <div className="text-lg font-bold">{departmentData.recentHires.thisMonth}</div>
                    <div className="text-xs text-muted-foreground">This month</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm font-medium text-blue-600">Net Growth</div>
                    <div className="text-lg font-bold">
                      {departmentData.recentHires.thisMonth - departmentData.recentTerminations.thisMonth > 0 ? '+' : ''}
                      {departmentData.recentHires.thisMonth - departmentData.recentTerminations.thisMonth}
                    </div>
                    <div className="text-xs text-muted-foreground">This month</div>
                  </div>
                </div>
              </div>
            ) : null}
          </>
        )}
      </CardContent>
    </Card>
  )
}
