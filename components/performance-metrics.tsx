"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from "recharts"
import { Bad<PERSON> } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { useEffect, useState, useCallback } from "react"
import { toast } from "@/components/ui/use-toast"
import {
  RefreshCw,
  AlertCircle,
  TrendingUp,
  TrendingDown,
  Minus,
  Award,
  Target,
  AlertTriangle,
  Info,
  CheckCircle
} from "lucide-react"
import { EmptyState, useEmptyState } from "@/components/ui/empty-state"
import { PerformanceMetrics as PerformanceMetricsData, TopPerformer } from "@/lib/services/dashboard/performance-metrics-service"
import { apiRequest, ApiError } from "@/lib/utils/api-error-handler"

interface PerformanceMetricsProps {
  className?: string
}

// Remove static data - will be replaced with real API data

export function PerformanceMetrics({ className }: PerformanceMetricsProps) {
  const [performanceData, setPerformanceData] = useState<PerformanceMetricsData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())
  const { getEmptyState } = useEmptyState()

  const fetchPerformanceData = useCallback(async (showToast = false) => {
    setIsLoading(true)
    setError(null)

    try {
      const data = await apiRequest('/api/dashboard/organization/performance', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' }
      })

      if (data.success && data.data) {
        setPerformanceData(data.data)
        setLastRefresh(new Date())

        if (showToast) {
          toast({
            title: "Performance Data Updated",
            description: `Overall score: ${data.data.overallScore.current.toFixed(1)}%`,
            variant: "default",
          })
        }
      } else {
        throw new ApiError('Invalid response format', 500)
      }

    } catch (err) {
      console.error('Error fetching performance data:', err)

      let errorMessage = 'Failed to load performance data'
      if (err instanceof ApiError) {
        errorMessage = err.message
      } else if (err instanceof Error) {
        errorMessage = err.message
      }

      setError(errorMessage)

      if (showToast) {
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        })
      }
    } finally {
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchPerformanceData()

    // Set up auto-refresh every 15 minutes
    const interval = setInterval(() => {
      fetchPerformanceData()
    }, 900000)

    return () => clearInterval(interval)
  }, [fetchPerformanceData])

  const handleRefresh = () => {
    fetchPerformanceData(true)
  }

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-600" />
      case 'stable':
        return <Minus className="h-4 w-4 text-gray-600" />
      default:
        return <Minus className="h-4 w-4 text-gray-600" />
    }
  }

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'critical':
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-600" />
      case 'info':
        return <Info className="h-4 w-4 text-blue-600" />
      default:
        return <Info className="h-4 w-4 text-blue-600" />
    }
  }

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              Performance Metrics
              {performanceData && (
                <Badge variant="secondary" className="text-xs">
                  {performanceData.overallScore.current.toFixed(1)}% overall
                </Badge>
              )}
              {performanceData && performanceData.alerts.length > 0 && (
                <Badge variant="destructive" className="text-xs">
                  {performanceData.alerts.length} alert{performanceData.alerts.length !== 1 ? 's' : ''}
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Track employee and organizational performance
              {lastRefresh && (
                <span className="text-xs text-muted-foreground ml-2">
                  • Updated {lastRefresh.toLocaleTimeString()}
                </span>
              )}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {performanceData && (
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Target className="h-3 w-3" />
                  <span>{performanceData.kpis.employeeRetention.rate.toFixed(1)}% retention</span>
                </div>
                <div className="flex items-center gap-1">
                  <Award className="h-3 w-3" />
                  <span>{performanceData.topPerformers.length} top performers</span>
                </div>
              </div>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="trends" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="trends">Performance Trends</TabsTrigger>
            <TabsTrigger value="top">Top Performers</TabsTrigger>
            <TabsTrigger value="kpis">KPIs & Alerts</TabsTrigger>
          </TabsList>

          <TabsContent value="trends" className="space-y-4">
            {isLoading ? (
              <div className="space-y-4">
                <Skeleton className="h-[240px] w-full" />
                <div className="grid grid-cols-3 gap-4">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <Skeleton key={index} className="h-20 w-full" />
                  ))}
                </div>
              </div>
            ) : getEmptyState(
              isLoading,
              error,
              !!performanceData,
              {
                noDataTitle: "No performance data found",
                noDataDescription: "Performance metrics will appear here when available",
                onRetry: handleRefresh,
                icon: Target
              }
            ) || (
              <>
                <div className="h-[240px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={performanceData.trends}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                      <XAxis dataKey="month" />
                      <YAxis domain={[60, 100]} />
                      <Tooltip
                        formatter={(value) => [`${value}%`, "Score"]}
                        contentStyle={{
                          backgroundColor: "hsl(var(--background))",
                          borderColor: "hsl(var(--border))",
                          borderRadius: "0.5rem",
                          boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
                        }}
                      />
                      <Legend />
                      <Line type="monotone" dataKey="teamAvg" stroke="#3b82f6" strokeWidth={2} name="Team" />
                      <Line type="monotone" dataKey="departmentAvg" stroke="#8b5cf6" strokeWidth={2} name="Department" />
                      <Line type="monotone" dataKey="companyAvg" stroke="#ef4444" strokeWidth={2} name="Company" />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
                <div className="mt-4 grid grid-cols-3 gap-4">
                  <div className="rounded-lg border p-3">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">Overall Score</div>
                      {getTrendIcon(performanceData.overallScore.trend)}
                    </div>
                    <div className="text-2xl font-bold">{performanceData.overallScore.current.toFixed(1)}%</div>
                    <div className={cn(
                      "text-xs",
                      performanceData.overallScore.change > 0 ? "text-green-600" :
                      performanceData.overallScore.change < 0 ? "text-red-600" : "text-gray-600"
                    )}>
                      {performanceData.overallScore.change > 0 ? '+' : ''}{performanceData.overallScore.change.toFixed(1)}% from last period
                    </div>
                  </div>
                  <div className="rounded-lg border p-3">
                    <div className="text-sm font-medium">Retention Rate</div>
                    <div className="text-2xl font-bold">{performanceData.kpis.employeeRetention.rate.toFixed(1)}%</div>
                    <div className={cn(
                      "text-xs",
                      performanceData.kpis.employeeRetention.change > 0 ? "text-green-600" :
                      performanceData.kpis.employeeRetention.change < 0 ? "text-red-600" : "text-gray-600"
                    )}>
                      {performanceData.kpis.employeeRetention.change > 0 ? '+' : ''}{performanceData.kpis.employeeRetention.change.toFixed(1)}% change
                    </div>
                  </div>
                  <div className="rounded-lg border p-3">
                    <div className="text-sm font-medium">Avg Tenure</div>
                    <div className="text-2xl font-bold">{performanceData.kpis.averageTenure.months.toFixed(0)} mo</div>
                    <div className={cn(
                      "text-xs",
                      performanceData.kpis.averageTenure.change > 0 ? "text-green-600" :
                      performanceData.kpis.averageTenure.change < 0 ? "text-red-600" : "text-gray-600"
                    )}>
                      {performanceData.kpis.averageTenure.change > 0 ? '+' : ''}{performanceData.kpis.averageTenure.change.toFixed(1)} months
                    </div>
                  </div>
                </div>
              </>
            )}
          </TabsContent>

          <TabsContent value="top">
            {isLoading ? (
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, index) => (
                  <div key={index} className="flex items-center justify-between rounded-lg border p-3">
                    <div className="space-y-1">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-3 w-24" />
                    </div>
                    <div className="flex items-center gap-2">
                      <Skeleton className="h-8 w-12" />
                      <Skeleton className="h-6 w-16" />
                    </div>
                  </div>
                ))}
              </div>
            ) : getEmptyState(
              isLoading,
              error,
              performanceData && performanceData.topPerformers.length > 0,
              {
                noDataTitle: "No top performers data found",
                noDataDescription: "Top performers will appear here when available",
                onRetry: handleRefresh,
                icon: Award
              }
            ) || (
              <div className="space-y-4">
                {performanceData.topPerformers.map((performer) => (
                  <div key={performer.id} className="flex items-center justify-between rounded-lg border p-3 hover:bg-muted/50 transition-colors">
                    <div className="space-y-0.5">
                      <div className="font-medium">{performer.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {performer.position} • {performer.department}
                      </div>
                      {performer.achievements.length > 0 && (
                        <div className="flex gap-1 mt-1">
                          {performer.achievements.slice(0, 2).map((achievement, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {achievement}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-center">
                        <div className="text-sm font-medium">{performer.score.toFixed(0)}%</div>
                        <div className="text-xs text-muted-foreground">Score</div>
                      </div>
                      <div className="flex flex-col gap-1">
                        <Badge variant="default" className="bg-green-500">
                          Top
                        </Badge>
                        {performer.improvement > 0 && (
                          <Badge variant="outline" className="text-xs text-green-600">
                            +{performer.improvement.toFixed(1)}%
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="kpis">
            {isLoading ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  {Array.from({ length: 4 }).map((_, index) => (
                    <Skeleton key={index} className="h-20 w-full" />
                  ))}
                </div>
                <Skeleton className="h-32 w-full" />
              </div>
            ) : getEmptyState(
              isLoading,
              error,
              !!performanceData,
              {
                noDataTitle: "No KPI data found",
                noDataDescription: "Key performance indicators will appear here when available",
                onRetry: handleRefresh,
                icon: Target
              }
            ) || (
              <div className="space-y-6">
                {/* KPIs Grid */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="rounded-lg border p-4">
                    <div className="text-sm font-medium text-muted-foreground">Employee Satisfaction</div>
                    <div className="text-2xl font-bold">{performanceData.kpis.employeeSatisfaction.score}%</div>
                    <div className="text-xs text-muted-foreground">
                      Target: {performanceData.kpis.employeeSatisfaction.target}%
                    </div>
                  </div>
                  <div className="rounded-lg border p-4">
                    <div className="text-sm font-medium text-muted-foreground">Productivity Index</div>
                    <div className="text-2xl font-bold">{performanceData.kpis.productivityIndex.score}%</div>
                    <div className={cn(
                      "text-xs",
                      performanceData.kpis.productivityIndex.change > 0 ? "text-green-600" : "text-red-600"
                    )}>
                      {performanceData.kpis.productivityIndex.change > 0 ? '+' : ''}{performanceData.kpis.productivityIndex.change}% change
                    </div>
                  </div>
                  <div className="rounded-lg border p-4">
                    <div className="text-sm font-medium text-muted-foreground">Leave Utilization</div>
                    <div className="text-2xl font-bold">{performanceData.kpis.leaveUtilization.percentage.toFixed(1)}%</div>
                    <div className={cn(
                      "text-xs",
                      performanceData.kpis.leaveUtilization.change > 0 ? "text-green-600" : "text-red-600"
                    )}>
                      {performanceData.kpis.leaveUtilization.change > 0 ? '+' : ''}{performanceData.kpis.leaveUtilization.change.toFixed(1)}% change
                    </div>
                  </div>
                  <div className="rounded-lg border p-4">
                    <div className="text-sm font-medium text-muted-foreground">Avg Tenure</div>
                    <div className="text-2xl font-bold">{performanceData.kpis.averageTenure.months.toFixed(0)} mo</div>
                    <div className={cn(
                      "text-xs",
                      performanceData.kpis.averageTenure.change > 0 ? "text-green-600" : "text-red-600"
                    )}>
                      {performanceData.kpis.averageTenure.change > 0 ? '+' : ''}{performanceData.kpis.averageTenure.change.toFixed(1)} months
                    </div>
                  </div>
                </div>

                {/* Alerts */}
                {performanceData.alerts.length > 0 && (
                  <div className="space-y-3">
                    <h4 className="text-sm font-medium">Performance Alerts</h4>
                    {performanceData.alerts.map((alert) => (
                      <div key={alert.id} className="flex items-start gap-3 rounded-lg border p-3">
                        {getAlertIcon(alert.type)}
                        <div className="flex-1">
                          <div className="font-medium text-sm">{alert.title}</div>
                          <div className="text-xs text-muted-foreground mt-1">{alert.description}</div>
                          {alert.department && (
                            <Badge variant="outline" className="text-xs mt-2">
                              {alert.department}
                            </Badge>
                          )}
                        </div>
                        {alert.actionRequired && (
                          <Badge variant="destructive" className="text-xs">
                            Action Required
                          </Badge>
                        )}
                      </div>
                    ))}
                  </div>
                )}

                {/* Insights */}
                {performanceData.insights.length > 0 && (
                  <div className="space-y-3">
                    <h4 className="text-sm font-medium">Performance Insights</h4>
                    {performanceData.insights.map((insight) => (
                      <div key={insight.id} className="rounded-lg border p-3">
                        <div className="flex items-center gap-2 mb-2">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <div className="font-medium text-sm">{insight.title}</div>
                          <Badge variant="outline" className={cn(
                            "text-xs",
                            insight.impact === 'high' ? "border-red-200 text-red-700" :
                            insight.impact === 'medium' ? "border-yellow-200 text-yellow-700" :
                            "border-blue-200 text-blue-700"
                          )}>
                            {insight.impact} impact
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground mb-2">{insight.description}</div>
                        <div className="text-xs font-medium text-blue-600">{insight.recommendation}</div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
