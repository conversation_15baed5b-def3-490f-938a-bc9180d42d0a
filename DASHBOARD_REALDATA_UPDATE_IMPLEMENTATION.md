# 📊 **DASHBOARD REAL DATA UPDATE IMPLEMENTATION**
## Kawandama Hills Plantation Management System

---

## 🎯 **OVERVIEW**

This document outlines the comprehensive plan to replace all static/mock data in the main dashboard (`https://www.kawandamahrsystem.com/dashboard`) with real-time, database-driven data. The goal is to create a fully functional, live dashboard that reflects actual system activities and requires user attention.

---

## 📋 **CURRENT DASHBOARD ANALYSIS**

### **Dashboard Structure** (`components/dashboard-page.tsx`)
```
📊 Main Dashboard
├── 🔢 Key Metrics Section
│   └── EmployeeOverview (4 cards)
├── 🏢 Organization Overview Section  
│   ├── DepartmentBreakdown
│   └── RecruitmentPipeline
├── 📈 Recent Updates Section
│   ├── RecentActivities
│   └── UpcomingReviews
└── ⏰ Performance & Time Off Section
    ├── PerformanceMetrics
    └── LeaveManagement
```

---

## ✅ **CURRENT IMPLEMENTATION STATUS**

### **🟢 FULLY IMPLEMENTED (Real Data)**
1. **Total Employees** (`components/employee-overview.tsx`)
   - ✅ Uses `useEmployeeStore` with real API data
   - ✅ Fetches from `/api/hr/employees`
   - ✅ Shows actual employee count from database

2. **Departments Count** (`components/employee-overview.tsx`)
   - ✅ Uses `useDepartmentStore` with real API data
   - ✅ Fetches from `/api/hr/departments`
   - ✅ Shows actual department count from database

### **🟡 PARTIALLY IMPLEMENTED (Mixed Data)**
1. **Employee Overview Cards** (`components/employee-overview.tsx`)
   - ✅ Total Employees: Real data
   - ✅ Departments: Real data
   - ❌ New Hires: Static data (hardcoded "12")
   - ❌ Terminations: Static data (hardcoded "3")

### **🔴 NOT IMPLEMENTED (Static/Mock Data)**
1. **Department Breakdown** (`components/department-breakdown.tsx`)
   - ❌ Uses hardcoded data array
   - ❌ Static employee distribution
   - ❌ Hardcoded colors and values

2. **Recent Activities** (`components/recent-activities.tsx`)
   - ❌ Uses mock data array
   - ❌ Fake user activities
   - ❌ No real audit trail integration

3. **Upcoming Reviews** (`components/upcoming-reviews.tsx`)
   - ❌ Completely static data
   - ❌ Hardcoded employee reviews
   - ❌ No integration with approval workflows

4. **Leave Management** (`components/leave-management.tsx`)
   - ❌ Static leave requests array
   - ❌ Hardcoded "4 Pending Requests" badge
   - ❌ No real leave data integration

5. **Recruitment Pipeline** (Component not analyzed yet)
   - ❌ Likely static data

6. **Performance Metrics** (Component not analyzed yet)
   - ❌ Likely static data

---

## 🛠️ **IMPLEMENTATION PLAN**

### **Phase 1: Real-Time Activity Tracking System**

#### **1.1 Enhanced Audit Service** 
**Status**: 🟡 **PARTIALLY EXISTS**
- ✅ **Existing**: `lib/services/payroll/audit-service.ts`
- ✅ **Existing**: `models/audit/DeletedItems.ts`
- ✅ **Existing**: `lib/middleware/audit-middleware.ts`
- ❌ **Missing**: Dashboard-specific activity aggregation
- ❌ **Missing**: Real-time activity feed API

**Implementation Required**:
```typescript
// lib/services/dashboard/activity-service.ts
export class DashboardActivityService {
  async getRecentActivities(limit: number = 10): Promise<Activity[]>
  async getActivityStats(): Promise<ActivityStats>
  async subscribeToActivities(callback: (activity: Activity) => void): void
}
```

#### **1.2 Activity Tracking Models**
**Status**: 🟡 **PARTIALLY EXISTS**
- ✅ **Existing**: Audit logging infrastructure
- ❌ **Missing**: Dashboard-optimized activity model
- ❌ **Missing**: Activity categorization for dashboard

**Implementation Required**:
```typescript
// models/dashboard/DashboardActivity.ts
interface IDashboardActivity {
  id: string;
  type: 'employee_created' | 'employee_updated' | 'leave_approved' | 'budget_submitted';
  user: UserInfo;
  target: TargetInfo;
  description: string;
  timestamp: Date;
  module: string;
  priority: 'low' | 'medium' | 'high';
}
```

#### **1.3 Real-Time WebSocket Integration**
**Status**: 🟡 **PARTIALLY EXISTS**
- ✅ **Existing**: `lib/services/websocket-service.ts`
- ✅ **Existing**: WebSocket event types defined
- ❌ **Missing**: Dashboard-specific event handlers
- ❌ **Missing**: Activity broadcasting

### **Phase 2: Approval Workflow System**

#### **2.1 Approval Queue Service**
**Status**: ❌ **NOT IMPLEMENTED**

**Implementation Required**:
```typescript
// lib/services/dashboard/approval-service.ts
export class ApprovalService {
  async getPendingApprovals(userId: string): Promise<PendingApproval[]>
  async getUpcomingReviews(userId: string): Promise<UpcomingReview[]>
  async getApprovalStats(): Promise<ApprovalStats>
}
```

**Approval Sources to Integrate**:
- ✅ **Budget Approvals**: Already exists in budget system
- ✅ **Leave Requests**: Already exists in leave system
- ❌ **Procurement Approvals**: Needs integration
- ❌ **Employee Reviews**: Needs implementation
- ❌ **Voucher Approvals**: Needs integration

#### **2.2 Approval Models**
**Status**: ❌ **NOT IMPLEMENTED**

**Implementation Required**:
```typescript
// models/dashboard/PendingApproval.ts
interface IPendingApproval {
  id: string;
  type: 'budget' | 'leave' | 'procurement' | 'voucher' | 'review';
  title: string;
  description: string;
  submittedBy: UserInfo;
  submittedAt: Date;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  dueDate?: Date;
  amount?: number;
  status: 'pending' | 'overdue';
}
```

### **Phase 3: Leave Management Integration**

#### **3.1 Leave Dashboard Service**
**Status**: 🟢 **FULLY EXISTS**
- ✅ **Existing**: `lib/backend/services/hr/LeaveService.ts`
- ✅ **Existing**: `models/leave/Leave.ts`
- ✅ **Existing**: `app/api/hr/leave/route.ts`
- ✅ **Existing**: Leave approval APIs

**Integration Required**:
```typescript
// lib/services/dashboard/leave-dashboard-service.ts
export class LeaveDashboardService {
  async getPendingLeaveRequests(): Promise<LeaveRequest[]>
  async getLeaveStats(): Promise<LeaveStats>
  async getUpcomingLeaves(): Promise<UpcomingLeave[]>
}
```

### **Phase 4: Department & Employee Analytics**

#### **4.1 Department Analytics Service**
**Status**: 🟡 **PARTIALLY EXISTS**
- ✅ **Existing**: `lib/frontend/departmentStore.ts`
- ✅ **Existing**: `lib/backend/services/hr/EmployeeService.ts`
- ❌ **Missing**: Department breakdown analytics
- ❌ **Missing**: Employee distribution calculations

**Implementation Required**:
```typescript
// lib/services/dashboard/department-analytics-service.ts
export class DepartmentAnalyticsService {
  async getDepartmentBreakdown(): Promise<DepartmentBreakdown[]>
  async getEmployeeDistribution(): Promise<EmployeeDistribution>
  async getNewHiresStats(period: string): Promise<NewHiresStats>
  async getTerminationStats(period: string): Promise<TerminationStats>
}
```

#### **4.2 Employee Metrics Enhancement**
**Status**: 🟡 **PARTIALLY EXISTS**
- ✅ **Existing**: Total employees count
- ❌ **Missing**: New hires calculation
- ❌ **Missing**: Terminations calculation
- ❌ **Missing**: Employee status analytics

---

## 🚀 **IMPLEMENTATION ROADMAP**

### **Sprint 1: Activity Tracking Foundation** (Week 1-2)
1. ✅ Create `DashboardActivityService`
2. ✅ Enhance audit logging for dashboard activities
3. ✅ Create activity aggregation APIs
4. ✅ Update `RecentActivities` component

### **Sprint 2: Approval Workflow Integration** (Week 3-4)
1. ✅ Create `ApprovalService`
2. ✅ Integrate existing approval systems
3. ✅ Create pending approvals API
4. ✅ Update `UpcomingReviews` component

### **Sprint 3: Leave Management Integration** (Week 5)
1. ✅ Create `LeaveDashboardService`
2. ✅ Integrate with existing leave APIs
3. ✅ Update `LeaveManagement` component

### **Sprint 4: Department & Employee Analytics** (Week 6-7)
1. ✅ Create `DepartmentAnalyticsService`
2. ✅ Implement employee distribution calculations
3. ✅ Update `DepartmentBreakdown` component
4. ✅ Enhance `EmployeeOverview` component

### **Sprint 5: Real-Time Features & Polish** (Week 8)
1. ✅ Implement WebSocket integration
2. ✅ Add real-time updates
3. ✅ Performance optimization
4. ✅ Testing and bug fixes

---

## 📁 **FILES TO CREATE/MODIFY**

### **New Files to Create**
```
lib/services/dashboard/
├── activity-service.ts
├── approval-service.ts
├── leave-dashboard-service.ts
├── department-analytics-service.ts
└── dashboard-data-service.ts

models/dashboard/
├── DashboardActivity.ts
├── PendingApproval.ts
└── DashboardMetrics.ts

app/api/dashboard/
├── activities/route.ts
├── approvals/route.ts
├── leave-stats/route.ts
├── department-breakdown/route.ts
└── metrics/route.ts

lib/stores/
└── dashboard-store.ts
```

### **Files to Modify**
```
components/
├── recent-activities.tsx (Replace mock data)
├── upcoming-reviews.tsx (Replace static data)
├── leave-management.tsx (Integrate real data)
├── department-breakdown.tsx (Add real analytics)
├── employee-overview.tsx (Add new hires/terminations)
└── dashboard-page.tsx (Add real-time updates)
```

---

## 🔧 **TECHNICAL REQUIREMENTS**

### **Database Collections Needed**
- ✅ **Existing**: `users`, `employees`, `departments`
- ✅ **Existing**: `leaves`, `auditlogs`, `deleted_items`
- ❌ **New**: `dashboard_activities`
- ❌ **New**: `pending_approvals`

### **API Endpoints Needed**
- ❌ `GET /api/dashboard/activities` - Recent activities
- ❌ `GET /api/dashboard/approvals` - Pending approvals
- ❌ `GET /api/dashboard/leave-stats` - Leave statistics
- ❌ `GET /api/dashboard/department-breakdown` - Department analytics
- ❌ `GET /api/dashboard/metrics` - Overall dashboard metrics

### **Real-Time Features**
- ❌ WebSocket events for new activities
- ❌ Live approval notifications
- ❌ Real-time metric updates

---

## 📊 **SUCCESS METRICS**

### **Functional Requirements**
- ✅ All dashboard data comes from database
- ✅ Real-time activity tracking
- ✅ Accurate approval queues
- ✅ Live leave management data
- ✅ Dynamic department analytics

### **Performance Requirements**
- ✅ Dashboard loads in < 2 seconds
- ✅ Real-time updates with < 1 second delay
- ✅ Efficient data caching
- ✅ Optimized database queries

### **User Experience Requirements**
- ✅ Intuitive real-time notifications
- ✅ Accurate data representation
- ✅ Responsive design maintained
- ✅ Smooth transitions and updates

---

## 🎯 **NEXT STEPS**

1. **Review and Approve Plan**: Stakeholder review of implementation plan
2. **Sprint Planning**: Detailed task breakdown for each sprint
3. **Development Start**: Begin with Sprint 1 - Activity Tracking Foundation
4. **Iterative Development**: Weekly reviews and adjustments
5. **Testing & Deployment**: Comprehensive testing before production deployment

---

## 📚 **DETAILED IMPLEMENTATION GUIDES**

### **Guide 1: Activity Tracking Implementation**

#### **Step 1: Create Dashboard Activity Service**
```typescript
// lib/services/dashboard/activity-service.ts
import { connectToDatabase } from '@/lib/backend/database';
import { AuditLog } from '@/models/audit/AuditLog';
import User from '@/models/User';
import Employee from '@/models/Employee';

export class DashboardActivityService {
  async getRecentActivities(limit: number = 10): Promise<Activity[]> {
    await connectToDatabase();

    const activities = await AuditLog.find({
      action: { $in: ['CREATE', 'UPDATE', 'DELETE'] },
      module: { $in: ['Employee', 'Leave', 'Budget', 'Procurement'] }
    })
    .populate('userId', 'firstName lastName email')
    .sort({ timestamp: -1 })
    .limit(limit)
    .lean();

    return activities.map(this.transformToActivity);
  }

  private transformToActivity(auditLog: any): Activity {
    return {
      id: auditLog._id,
      user: {
        name: `${auditLog.userId.firstName} ${auditLog.userId.lastName}`,
        avatar: auditLog.userId.avatar || '/placeholder-user.jpg',
        initials: `${auditLog.userId.firstName[0]}${auditLog.userId.lastName[0]}`
      },
      action: this.getActionDescription(auditLog),
      department: auditLog.metadata?.department || 'System',
      time: this.getRelativeTime(auditLog.timestamp),
      type: this.getActivityType(auditLog),
      timestamp: auditLog.timestamp
    };
  }
}
```

#### **Step 2: Create Activity API Endpoint**
```typescript
// app/api/dashboard/activities/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { DashboardActivityService } from '@/lib/services/dashboard/activity-service';

export async function GET(req: NextRequest) {
  try {
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const searchParams = req.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '10');

    const activityService = new DashboardActivityService();
    const activities = await activityService.getRecentActivities(limit);

    return NextResponse.json({ activities });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch activities' },
      { status: 500 }
    );
  }
}
```

#### **Step 3: Update Recent Activities Component**
```typescript
// components/recent-activities.tsx (Updated)
"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface Activity {
  id: string
  user: { name: string; avatar: string; initials: string }
  action: string
  department: string
  time: string
  type: string
  timestamp: string | Date
}

export function RecentActivities({ className }: { className?: string }) {
  const [activities, setActivities] = useState<Activity[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchActivities = async () => {
      try {
        const response = await fetch('/api/dashboard/activities?limit=10')
        const data = await response.json()
        setActivities(data.activities || [])
      } catch (error) {
        console.error('Error fetching activities:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchActivities()

    // Set up real-time updates
    const interval = setInterval(fetchActivities, 30000) // Refresh every 30 seconds
    return () => clearInterval(interval)
  }, [])

  // Rest of component implementation...
}
```

### **Guide 2: Approval Workflow Implementation**

#### **Step 1: Create Approval Service**
```typescript
// lib/services/dashboard/approval-service.ts
import { connectToDatabase } from '@/lib/backend/database';
import Leave from '@/models/leave/Leave';
import { Budget } from '@/models/accounting/Budget';

export class ApprovalService {
  async getPendingApprovals(userId: string): Promise<PendingApproval[]> {
    await connectToDatabase();

    const approvals: PendingApproval[] = [];

    // Get pending leave requests
    const pendingLeaves = await Leave.find({ status: 'pending' })
      .populate('employeeId', 'firstName lastName')
      .populate('createdBy', 'firstName lastName')
      .sort({ createdAt: -1 })
      .limit(10);

    approvals.push(...pendingLeaves.map(this.transformLeaveToApproval));

    // Get pending budgets
    const pendingBudgets = await Budget.find({
      status: { $in: ['draft', 'submitted'] }
    })
      .populate('createdBy', 'firstName lastName')
      .sort({ createdAt: -1 })
      .limit(10);

    approvals.push(...pendingBudgets.map(this.transformBudgetToApproval));

    return approvals.sort((a, b) =>
      new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime()
    );
  }

  private transformLeaveToApproval(leave: any): PendingApproval {
    return {
      id: leave._id,
      type: 'leave',
      title: `Leave Request - ${leave.employeeId.firstName} ${leave.employeeId.lastName}`,
      description: `${leave.leaveType} from ${leave.startDate.toDateString()} to ${leave.endDate.toDateString()}`,
      submittedBy: {
        name: `${leave.createdBy.firstName} ${leave.createdBy.lastName}`,
        avatar: leave.createdBy.avatar || '/placeholder-user.jpg'
      },
      submittedAt: leave.createdAt,
      priority: this.calculatePriority(leave.createdAt),
      dueDate: this.calculateDueDate(leave.createdAt, 'leave'),
      status: this.getApprovalStatus(leave.createdAt)
    };
  }
}
```

### **Guide 3: Leave Management Integration**

#### **Step 1: Create Leave Dashboard Service**
```typescript
// lib/services/dashboard/leave-dashboard-service.ts
import { LeaveService } from '@/lib/backend/services/hr/LeaveService';
import Leave from '@/models/leave/Leave';
import LeaveBalance from '@/models/leave/LeaveBalance';

export class LeaveDashboardService {
  private leaveService = new LeaveService();

  async getLeaveStats(): Promise<LeaveStats> {
    const currentYear = new Date().getFullYear();

    const [
      pendingCount,
      approvedThisMonth,
      totalThisYear,
      upcomingLeaves
    ] = await Promise.all([
      Leave.countDocuments({ status: 'pending' }),
      Leave.countDocuments({
        status: 'approved',
        approvalDate: {
          $gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
        }
      }),
      Leave.countDocuments({
        status: 'approved',
        startDate: {
          $gte: new Date(currentYear, 0, 1),
          $lte: new Date(currentYear, 11, 31)
        }
      }),
      this.getUpcomingLeaves()
    ]);

    return {
      pendingRequests: pendingCount,
      approvedThisMonth,
      totalThisYear,
      upcomingLeaves: upcomingLeaves.length
    };
  }

  async getPendingLeaveRequests(limit: number = 5): Promise<LeaveRequest[]> {
    const pendingLeaves = await Leave.find({ status: 'pending' })
      .populate('employeeId', 'firstName lastName avatar')
      .populate('leaveTypeId', 'name color')
      .sort({ createdAt: -1 })
      .limit(limit);

    return pendingLeaves.map(this.transformLeaveRequest);
  }
}
```

### **Guide 4: Department Analytics Implementation**

#### **Step 1: Create Department Analytics Service**
```typescript
// lib/services/dashboard/department-analytics-service.ts
import { connectToDatabase } from '@/lib/backend/database';
import Employee from '@/models/Employee';
import Department from '@/models/Department';

export class DepartmentAnalyticsService {
  async getDepartmentBreakdown(): Promise<DepartmentBreakdown[]> {
    await connectToDatabase();

    const pipeline = [
      {
        $lookup: {
          from: 'departments',
          localField: 'departmentId',
          foreignField: '_id',
          as: 'department'
        }
      },
      {
        $unwind: '$department'
      },
      {
        $group: {
          _id: '$department._id',
          name: { $first: '$department.name' },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ];

    const results = await Employee.aggregate(pipeline);
    const total = results.reduce((sum, dept) => sum + dept.count, 0);

    return results.map((dept, index) => ({
      name: dept.name,
      value: dept.count,
      color: this.getDepartmentColor(index),
      percentage: Math.round((dept.count / total) * 100)
    }));
  }

  async getNewHiresStats(period: string = 'month'): Promise<NewHiresStats> {
    const dateRange = this.getDateRange(period);

    const newHires = await Employee.countDocuments({
      dateOfJoining: {
        $gte: dateRange.start,
        $lte: dateRange.end
      }
    });

    const previousPeriodRange = this.getPreviousDateRange(period);
    const previousNewHires = await Employee.countDocuments({
      dateOfJoining: {
        $gte: previousPeriodRange.start,
        $lte: previousPeriodRange.end
      }
    });

    return {
      current: newHires,
      previous: previousNewHires,
      change: newHires - previousNewHires,
      changePercentage: previousNewHires > 0
        ? Math.round(((newHires - previousNewHires) / previousNewHires) * 100)
        : 0
    };
  }
}
```

---

## 🔄 **INTEGRATION CHECKLIST**

### **Phase 1 Checklist: Activity Tracking**
- [ ] Create `DashboardActivityService`
- [ ] Create `/api/dashboard/activities` endpoint
- [ ] Update `RecentActivities` component
- [ ] Test real-time activity updates
- [ ] Verify audit log integration

### **Phase 2 Checklist: Approval Workflow**
- [ ] Create `ApprovalService`
- [ ] Create `/api/dashboard/approvals` endpoint
- [ ] Update `UpcomingReviews` component
- [ ] Integrate leave approval system
- [ ] Integrate budget approval system
- [ ] Test approval notifications

### **Phase 3 Checklist: Leave Management**
- [ ] Create `LeaveDashboardService`
- [ ] Create `/api/dashboard/leave-stats` endpoint
- [ ] Update `LeaveManagement` component
- [ ] Test leave request integration
- [ ] Verify leave balance calculations

### **Phase 4 Checklist: Department Analytics**
- [ ] Create `DepartmentAnalyticsService`
- [ ] Create `/api/dashboard/department-breakdown` endpoint
- [ ] Update `DepartmentBreakdown` component
- [ ] Update `EmployeeOverview` component
- [ ] Test employee distribution calculations

### **Phase 5 Checklist: Real-Time Features**
- [ ] Implement WebSocket integration
- [ ] Add real-time activity broadcasting
- [ ] Test live dashboard updates
- [ ] Performance optimization
- [ ] Final testing and deployment

---

**Document Version**: 1.0
**Last Updated**: June 15, 2025
**Status**: Ready for Implementation
