// lib/types/dashboard-approvals.ts
import mongoose from 'mongoose';

/**
 * Unified approval item interface for dashboard display
 */
export interface DashboardApproval {
  id: string;
  type: ApprovalType;
  title: string;
  description: string;
  amount?: number;
  currency?: string;
  
  // Submitter information
  submittedBy: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
    department?: string;
  };
  
  // Employee information (for leave requests)
  employee?: {
    id: string;
    name: string;
    avatar?: string;
    department?: string;
  };
  
  // Timing information
  submittedAt: Date;
  dueDate?: Date;
  isOverdue: boolean;
  urgencyLevel: UrgencyLevel;
  
  // Approval workflow
  currentLevel: number;
  totalLevels: number;
  currentApprover?: {
    id: string;
    name: string;
    role: string;
  };
  
  // Status and priority
  status: ApprovalStatus;
  priority: Priority;
  
  // Additional metadata
  metadata: {
    module: string;
    entityId: string;
    entityType: string;
    category?: string;
    tags?: string[];
    attachments?: number;
    comments?: number;
  };
  
  // Actions available to current user
  availableActions: ApprovalAction[];
  
  // Quick preview data
  preview?: {
    startDate?: Date;
    endDate?: Date;
    duration?: number;
    budgetCategory?: string;
    supplier?: string;
    department?: string;
  };
}

/**
 * Types of approvals in the system
 */
export enum ApprovalType {
  LEAVE_REQUEST = 'leave_request',
  BUDGET = 'budget',
  INCOME = 'income',
  EXPENDITURE = 'expenditure',
  PROCUREMENT_REQUISITION = 'procurement_requisition',
  PURCHASE_ORDER = 'purchase_order',
  CONTRACT = 'contract',
  VOUCHER = 'voucher',
  EMPLOYEE_REVIEW = 'employee_review',
  SALARY_ADJUSTMENT = 'salary_adjustment',
  OVERTIME_REQUEST = 'overtime_request',
  TRAVEL_REQUEST = 'travel_request',
  EXPENSE_CLAIM = 'expense_claim'
}

/**
 * Approval status
 */
export enum ApprovalStatus {
  PENDING = 'pending',
  IN_REVIEW = 'in_review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired'
}

/**
 * Priority levels
 */
export enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
  CRITICAL = 'critical'
}

/**
 * Urgency levels based on timing
 */
export enum UrgencyLevel {
  NORMAL = 'normal',
  URGENT = 'urgent',
  OVERDUE = 'overdue',
  CRITICAL = 'critical'
}

/**
 * Available actions for approvals
 */
export enum ApprovalAction {
  APPROVE = 'approve',
  REJECT = 'reject',
  REQUEST_INFO = 'request_info',
  ESCALATE = 'escalate',
  DELEGATE = 'delegate',
  VIEW_DETAILS = 'view_details',
  DOWNLOAD = 'download',
  COMMENT = 'comment'
}

/**
 * Approval statistics for dashboard metrics
 */
export interface ApprovalStats {
  totalPending: number;
  totalOverdue: number;
  totalToday: number;
  totalThisWeek: number;
  
  // By type
  byType: Array<{
    type: ApprovalType;
    count: number;
    percentage: number;
  }>;
  
  // By priority
  byPriority: Array<{
    priority: Priority;
    count: number;
    percentage: number;
  }>;
  
  // By urgency
  byUrgency: Array<{
    urgency: UrgencyLevel;
    count: number;
    percentage: number;
  }>;
  
  // Recent trends
  trends: Array<{
    date: string;
    submitted: number;
    approved: number;
    rejected: number;
  }>;
  
  // Performance metrics
  averageApprovalTime: number; // in hours
  approvalRate: number; // percentage
  overdueRate: number; // percentage
}

/**
 * Approval filters for API requests
 */
export interface ApprovalFilters {
  types?: ApprovalType[];
  statuses?: ApprovalStatus[];
  priorities?: Priority[];
  urgencyLevels?: UrgencyLevel[];
  submittedBy?: string;
  currentApprover?: string;
  department?: string;
  amountRange?: {
    min?: number;
    max?: number;
  };
  dateRange?: {
    start?: Date;
    end?: Date;
  };
  search?: string;
  tags?: string[];
}

/**
 * Approval action request
 */
export interface ApprovalActionRequest {
  approvalId: string;
  action: ApprovalAction;
  comments?: string;
  delegateTo?: string;
  attachments?: string[];
  metadata?: Record<string, any>;
}

/**
 * Approval action response
 */
export interface ApprovalActionResponse {
  success: boolean;
  message: string;
  approval?: DashboardApproval;
  nextApprover?: {
    id: string;
    name: string;
    role: string;
  };
  isCompleted?: boolean;
  error?: string;
}

/**
 * Approval notification data
 */
export interface ApprovalNotification {
  id: string;
  type: 'new_approval' | 'approval_approved' | 'approval_rejected' | 'approval_overdue';
  approvalId: string;
  approvalType: ApprovalType;
  title: string;
  message: string;
  recipient: {
    id: string;
    name: string;
    email: string;
  };
  sender?: {
    id: string;
    name: string;
  };
  createdAt: Date;
  readAt?: Date;
  actionUrl?: string;
  metadata?: Record<string, any>;
}

/**
 * Approval workflow configuration
 */
export interface ApprovalWorkflowConfig {
  type: ApprovalType;
  levels: Array<{
    level: number;
    role: string;
    minAmount?: number;
    maxAmount?: number;
    required: boolean;
    timeLimit?: number; // hours
    escalationRule?: string;
  }>;
  autoApprovalRules?: {
    maxAmount?: number;
    trustedUsers?: string[];
    categories?: string[];
    conditions?: Record<string, any>;
  };
  notifications: {
    onSubmission: boolean;
    onApproval: boolean;
    onRejection: boolean;
    onEscalation: boolean;
    reminderHours: number[];
  };
}

/**
 * Helper type for approval source data
 */
export interface ApprovalSourceData {
  // Leave request data
  leaveRequest?: {
    leaveId: string;
    employeeId: mongoose.Types.ObjectId;
    leaveTypeId: mongoose.Types.ObjectId;
    startDate: Date;
    endDate: Date;
    duration: number;
    reason?: string;
    status: string;
  };
  
  // Budget data
  budget?: {
    budgetId: string;
    name: string;
    fiscalYear: string;
    totalAmount: number;
    status: string;
    departmentId?: mongoose.Types.ObjectId;
  };
  
  // Income data
  income?: {
    incomeId: string;
    source: string;
    amount: number;
    category: string;
    status: string;
  };
  
  // Expenditure data
  expenditure?: {
    expenditureId: string;
    description: string;
    amount: number;
    category: string;
    status: string;
  };
  
  // Procurement data
  procurement?: {
    requisitionId: string;
    description: string;
    totalAmount: number;
    supplier?: string;
    category: string;
    status: string;
  };
  
  // Generic data for other types
  generic?: {
    entityId: string;
    title: string;
    description: string;
    amount?: number;
    status: string;
    metadata?: Record<string, any>;
  };
}
