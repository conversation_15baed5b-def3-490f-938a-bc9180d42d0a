import { connectToDatabase } from '@/lib/backend/database';
import LeaveType from '@/models/leave/LeaveType';
import User from '@/models/User';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

interface DefaultLeaveType {
  name: string;
  code: string;
  description: string;
  defaultDays: number;
  isPaid: boolean;
  requiresApproval: boolean;
  maxConsecutiveDays: number;
  minNoticeInDays: number;
  allowCarryOver: boolean;
  maxCarryOverDays: number;
  color: string;
  isActive: boolean;
}

const defaultLeaveTypes: DefaultLeaveType[] = [
  {
    name: 'Annual Leave',
    code: 'ANNUAL',
    description: 'Yearly vacation leave for rest and recreation',
    defaultDays: 21,
    isPaid: true,
    requiresApproval: true,
    maxConsecutiveDays: 14,
    minNoticeInDays: 7,
    allowCarryOver: true,
    maxCarryOverDays: 5,
    color: '#3B82F6',
    isActive: true
  },
  {
    name: 'Sick Leave',
    code: 'SICK',
    description: 'Medical leave for illness or medical appointments',
    defaultDays: 10,
    isPaid: true,
    requiresApproval: false,
    maxConsecutiveDays: 5,
    minNoticeInDays: 0,
    allowCarryOver: false,
    maxCarryOverDays: 0,
    color: '#EF4444',
    isActive: true
  },
  {
    name: 'Maternity Leave',
    code: 'MATERNITY',
    description: 'Leave for mothers before and after childbirth',
    defaultDays: 90,
    isPaid: true,
    requiresApproval: true,
    maxConsecutiveDays: 90,
    minNoticeInDays: 30,
    allowCarryOver: false,
    maxCarryOverDays: 0,
    color: '#EC4899',
    isActive: true
  },
  {
    name: 'Paternity Leave',
    code: 'PATERNITY',
    description: 'Leave for fathers after childbirth or adoption',
    defaultDays: 14,
    isPaid: true,
    requiresApproval: true,
    maxConsecutiveDays: 14,
    minNoticeInDays: 14,
    allowCarryOver: false,
    maxCarryOverDays: 0,
    color: '#06B6D4',
    isActive: true
  },
  {
    name: 'Emergency Leave',
    code: 'EMERGENCY',
    description: 'Urgent leave for family emergencies or unforeseen circumstances',
    defaultDays: 3,
    isPaid: false,
    requiresApproval: true,
    maxConsecutiveDays: 3,
    minNoticeInDays: 0,
    allowCarryOver: false,
    maxCarryOverDays: 0,
    color: '#F59E0B',
    isActive: true
  },
  {
    name: 'Bereavement Leave',
    code: 'BEREAVEMENT',
    description: 'Leave for mourning the death of a family member',
    defaultDays: 5,
    isPaid: true,
    requiresApproval: true,
    maxConsecutiveDays: 5,
    minNoticeInDays: 0,
    allowCarryOver: false,
    maxCarryOverDays: 0,
    color: '#6B7280',
    isActive: true
  },
  {
    name: 'Study Leave',
    code: 'STUDY',
    description: 'Leave for educational purposes and professional development',
    defaultDays: 5,
    isPaid: false,
    requiresApproval: true,
    maxConsecutiveDays: 5,
    minNoticeInDays: 14,
    allowCarryOver: true,
    maxCarryOverDays: 2,
    color: '#8B5CF6',
    isActive: true
  },
  {
    name: 'Compassionate Leave',
    code: 'COMPASSIONATE',
    description: 'Leave for caring for seriously ill family members',
    defaultDays: 7,
    isPaid: false,
    requiresApproval: true,
    maxConsecutiveDays: 7,
    minNoticeInDays: 1,
    allowCarryOver: false,
    maxCarryOverDays: 0,
    color: '#10B981',
    isActive: true
  },
  {
    name: 'Public Holiday',
    code: 'HOLIDAY',
    description: 'National and public holidays',
    defaultDays: 12,
    isPaid: true,
    requiresApproval: false,
    maxConsecutiveDays: 1,
    minNoticeInDays: 0,
    allowCarryOver: false,
    maxCarryOverDays: 0,
    color: '#F97316',
    isActive: true
  },
  {
    name: 'Sabbatical Leave',
    code: 'SABBATICAL',
    description: 'Extended leave for personal projects or rest (unpaid)',
    defaultDays: 0,
    isPaid: false,
    requiresApproval: true,
    maxConsecutiveDays: 365,
    minNoticeInDays: 90,
    allowCarryOver: false,
    maxCarryOverDays: 0,
    color: '#84CC16',
    isActive: true
  }
];

export async function seedLeaveTypes(): Promise<{ created: number; skipped: number; total: number }> {
  try {
    console.log('🔌 Connecting to database...');
    // Connect to database
    await connectToDatabase();
    console.log('✅ Database connected');

    // Check if leave types already exist
    const existingCount = await LeaveType.countDocuments();
    console.log('📊 Existing leave types count:', existingCount);
    if (existingCount > 0) {
      logger.info(`Leave types already exist (${existingCount} found). Will only add missing types.`, LogCategory.SYSTEM);
    }

    // Find a system admin user to use as creator
    console.log('👨‍💼 Finding system admin...');
    const systemAdmin = await User.findOne({
      role: { $in: ['SUPER_ADMIN', 'SYSTEM_ADMIN'] }
    });
    console.log('👨‍💼 System admin found:', systemAdmin ? systemAdmin.email : 'none');

    if (!systemAdmin) {
      throw new Error('No system admin user found. Please create a system admin user first.');
    }

    console.log('🚀 Starting leave types seeding...');
    logger.info('Starting leave types seeding...', LogCategory.SYSTEM);

    // Create leave types
    const createdLeaveTypes = [];
    let skippedCount = 0;

    for (const leaveTypeData of defaultLeaveTypes) {
      try {
        // Check if leave type with this code already exists
        const existing = await LeaveType.findOne({ code: leaveTypeData.code });
        if (existing) {
          logger.info(`Leave type ${leaveTypeData.code} already exists, skipping...`, LogCategory.SYSTEM);
          skippedCount++;
          continue;
        }

        const leaveType = new LeaveType({
          ...leaveTypeData,
          createdBy: systemAdmin._id
        });

        await leaveType.save();
        createdLeaveTypes.push(leaveType);

        logger.info(`Created leave type: ${leaveTypeData.name} (${leaveTypeData.code})`, LogCategory.SYSTEM);
      } catch (error) {
        logger.error(`Error creating leave type ${leaveTypeData.code}`, LogCategory.SYSTEM, error);
      }
    }

    const result = {
      created: createdLeaveTypes.length,
      skipped: skippedCount,
      total: defaultLeaveTypes.length
    };

    if (createdLeaveTypes.length === 0) {
      logger.info('No new leave types were created. All default leave types already exist.', LogCategory.SYSTEM);
    } else {
      logger.info(`Leave types seeding completed. Created ${createdLeaveTypes.length} new leave types.`, LogCategory.SYSTEM);
    }

    return result;

  } catch (error) {
    logger.error('Error seeding leave types', LogCategory.SYSTEM, error);
    throw error;
  }
}

// Function to run seeder directly
export async function runLeaveTypesSeeder(): Promise<void> {
  try {
    await seedLeaveTypes();
    console.log('✅ Leave types seeding completed successfully');
  } catch (error) {
    console.error('❌ Leave types seeding failed:', error);
    process.exit(1);
  }
}

// Run seeder if this file is executed directly
if (require.main === module) {
  runLeaveTypesSeeder();
}
