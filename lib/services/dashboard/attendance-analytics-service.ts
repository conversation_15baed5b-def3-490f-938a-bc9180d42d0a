import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import Attendance from '@/models/attendance/Attendance';

export interface AttendanceAnalytics {
  attendanceRate: number;
  totalWorkingDays: number;
  totalPresentDays: number;
  totalAbsentDays: number;
  totalLateDays: number;
  totalHalfDays: number;
  averageWorkHours: number;
  totalOvertime: number;
  departmentBreakdown: Array<{
    department: string;
    attendanceRate: number;
    totalEmployees: number;
    presentDays: number;
    workingDays: number;
  }>;
  statusDistribution: Array<{
    status: string;
    count: number;
    percentage: number;
  }>;
  trends: {
    dailyAttendance: Array<{
      date: string;
      attendanceRate: number;
      presentCount: number;
      totalCount: number;
    }>;
    weeklyAverage: number;
    monthlyAverage: number;
  };
}

export interface AttendanceComparison {
  currentPeriod: AttendanceAnalytics;
  previousPeriod: AttendanceAnalytics;
  changePercentage: number;
  trend: 'up' | 'down' | 'stable';
}

/**
 * Service for calculating attendance analytics and metrics
 */
export class AttendanceAnalyticsService {
  /**
   * Calculate attendance rate for a given period
   */
  async calculateAttendanceRate(
    startDate: Date,
    endDate: Date,
    employeeIds?: string[]
  ): Promise<AttendanceAnalytics> {
    try {
      await connectToDatabase();
      
      logger.info('Calculating attendance rate', LogCategory.ANALYTICS, {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        employeeCount: employeeIds?.length || 'all'
      });

      // Build base query
      const baseQuery: any = {
        date: {
          $gte: startDate,
          $lte: endDate
        }
      };

      // Add employee filter if provided
      if (employeeIds && employeeIds.length > 0) {
        baseQuery.employeeId = {
          $in: employeeIds.map(id => new mongoose.Types.ObjectId(id))
        };
      }

      // Get all attendance records for the period
      const attendanceRecords = await Attendance.aggregate([
        { $match: baseQuery },
        {
          $lookup: {
            from: 'employees',
            localField: 'employeeId',
            foreignField: '_id',
            as: 'employee'
          }
        },
        {
          $unwind: {
            path: '$employee',
            preserveNullAndEmptyArrays: true
          }
        },
        {
          $lookup: {
            from: 'departments',
            localField: 'employee.departmentId',
            foreignField: '_id',
            as: 'department'
          }
        },
        {
          $unwind: {
            path: '$department',
            preserveNullAndEmptyArrays: true
          }
        }
      ]);

      // Calculate basic metrics
      const totalRecords = attendanceRecords.length;
      const presentRecords = attendanceRecords.filter(record => 
        ['present', 'late', 'half-day'].includes(record.status)
      );
      const absentRecords = attendanceRecords.filter(record => record.status === 'absent');
      const lateRecords = attendanceRecords.filter(record => record.status === 'late');
      const halfDayRecords = attendanceRecords.filter(record => record.status === 'half-day');

      // Exclude weekends and holidays from working days calculation
      const workingDayRecords = attendanceRecords.filter(record => 
        !['weekend', 'holiday'].includes(record.status)
      );

      const totalWorkingDays = workingDayRecords.length;
      const totalPresentDays = presentRecords.length;
      const attendanceRate = totalWorkingDays > 0 ? (totalPresentDays / totalWorkingDays) * 100 : 0;

      // Calculate average work hours
      const recordsWithWorkHours = attendanceRecords.filter(record => record.workHours);
      const totalWorkHours = recordsWithWorkHours.reduce((sum, record) => sum + (record.workHours || 0), 0);
      const averageWorkHours = recordsWithWorkHours.length > 0 ? totalWorkHours / recordsWithWorkHours.length : 0;

      // Calculate total overtime
      const totalOvertime = attendanceRecords.reduce((sum, record) => sum + (record.overtime || 0), 0);

      // Calculate department breakdown
      const departmentMap = new Map();
      attendanceRecords.forEach(record => {
        const deptName = record.department?.name || 'No Department';
        if (!departmentMap.has(deptName)) {
          departmentMap.set(deptName, {
            department: deptName,
            totalRecords: 0,
            presentRecords: 0,
            workingRecords: 0,
            employees: new Set()
          });
        }
        
        const dept = departmentMap.get(deptName);
        dept.totalRecords++;
        dept.employees.add(record.employeeId.toString());
        
        if (!['weekend', 'holiday'].includes(record.status)) {
          dept.workingRecords++;
        }
        
        if (['present', 'late', 'half-day'].includes(record.status)) {
          dept.presentRecords++;
        }
      });

      const departmentBreakdown = Array.from(departmentMap.values()).map(dept => ({
        department: dept.department,
        attendanceRate: dept.workingRecords > 0 ? (dept.presentRecords / dept.workingRecords) * 100 : 0,
        totalEmployees: dept.employees.size,
        presentDays: dept.presentRecords,
        workingDays: dept.workingRecords
      }));

      // Calculate status distribution
      const statusMap = new Map();
      attendanceRecords.forEach(record => {
        const status = record.status;
        statusMap.set(status, (statusMap.get(status) || 0) + 1);
      });

      const statusDistribution = Array.from(statusMap.entries()).map(([status, count]) => ({
        status,
        count,
        percentage: totalRecords > 0 ? (count / totalRecords) * 100 : 0
      }));

      // Calculate daily trends
      const dailyMap = new Map();
      attendanceRecords.forEach(record => {
        const dateStr = record.date.toISOString().split('T')[0];
        if (!dailyMap.has(dateStr)) {
          dailyMap.set(dateStr, {
            date: dateStr,
            totalCount: 0,
            presentCount: 0
          });
        }
        
        const day = dailyMap.get(dateStr);
        if (!['weekend', 'holiday'].includes(record.status)) {
          day.totalCount++;
        }
        if (['present', 'late', 'half-day'].includes(record.status)) {
          day.presentCount++;
        }
      });

      const dailyAttendance = Array.from(dailyMap.values())
        .map(day => ({
          ...day,
          attendanceRate: day.totalCount > 0 ? (day.presentCount / day.totalCount) * 100 : 0
        }))
        .sort((a, b) => a.date.localeCompare(b.date));

      // Calculate weekly and monthly averages
      const weeklyRates = [];
      const monthlyRates = [];
      
      for (let i = 0; i < dailyAttendance.length; i += 7) {
        const weekData = dailyAttendance.slice(i, i + 7);
        const weekAvg = weekData.reduce((sum, day) => sum + day.attendanceRate, 0) / weekData.length;
        weeklyRates.push(weekAvg);
      }

      for (let i = 0; i < dailyAttendance.length; i += 30) {
        const monthData = dailyAttendance.slice(i, i + 30);
        const monthAvg = monthData.reduce((sum, day) => sum + day.attendanceRate, 0) / monthData.length;
        monthlyRates.push(monthAvg);
      }

      const weeklyAverage = weeklyRates.length > 0 ? weeklyRates.reduce((sum, rate) => sum + rate, 0) / weeklyRates.length : 0;
      const monthlyAverage = monthlyRates.length > 0 ? monthlyRates.reduce((sum, rate) => sum + rate, 0) / monthlyRates.length : 0;

      const analytics: AttendanceAnalytics = {
        attendanceRate: parseFloat(attendanceRate.toFixed(2)),
        totalWorkingDays,
        totalPresentDays,
        totalAbsentDays: absentRecords.length,
        totalLateDays: lateRecords.length,
        totalHalfDays: halfDayRecords.length,
        averageWorkHours: parseFloat(averageWorkHours.toFixed(2)),
        totalOvertime: parseFloat(totalOvertime.toFixed(2)),
        departmentBreakdown,
        statusDistribution,
        trends: {
          dailyAttendance,
          weeklyAverage: parseFloat(weeklyAverage.toFixed(2)),
          monthlyAverage: parseFloat(monthlyAverage.toFixed(2))
        }
      };

      logger.info('Attendance rate calculated successfully', LogCategory.ANALYTICS, {
        attendanceRate: analytics.attendanceRate,
        totalWorkingDays: analytics.totalWorkingDays,
        totalPresentDays: analytics.totalPresentDays
      });

      return analytics;
    } catch (error) {
      logger.error('Error calculating attendance rate', LogCategory.ANALYTICS, error);
      throw error;
    }
  }

  /**
   * Compare attendance between two periods
   */
  async compareAttendancePeriods(
    currentStart: Date,
    currentEnd: Date,
    previousStart: Date,
    previousEnd: Date,
    employeeIds?: string[]
  ): Promise<AttendanceComparison> {
    try {
      const [currentPeriod, previousPeriod] = await Promise.all([
        this.calculateAttendanceRate(currentStart, currentEnd, employeeIds),
        this.calculateAttendanceRate(previousStart, previousEnd, employeeIds)
      ]);

      const changePercentage = previousPeriod.attendanceRate > 0 
        ? ((currentPeriod.attendanceRate - previousPeriod.attendanceRate) / previousPeriod.attendanceRate) * 100
        : 0;

      let trend: 'up' | 'down' | 'stable' = 'stable';
      if (Math.abs(changePercentage) > 0.5) { // Consider changes > 0.5% as significant
        trend = changePercentage > 0 ? 'up' : 'down';
      }

      return {
        currentPeriod,
        previousPeriod,
        changePercentage: parseFloat(changePercentage.toFixed(2)),
        trend
      };
    } catch (error) {
      logger.error('Error comparing attendance periods', LogCategory.ANALYTICS, error);
      throw error;
    }
  }

  /**
   * Get attendance rate for dashboard (current month vs previous month)
   */
  async getDashboardAttendanceRate(): Promise<{
    attendanceRate: number;
    changePercentage: number;
    trend: 'up' | 'down' | 'stable';
  }> {
    try {
      const currentDate = new Date();
      const currentMonthStart = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
      const currentMonthEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
      
      const previousMonthStart = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
      const previousMonthEnd = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0);

      const comparison = await this.compareAttendancePeriods(
        currentMonthStart,
        currentMonthEnd,
        previousMonthStart,
        previousMonthEnd
      );

      return {
        attendanceRate: comparison.currentPeriod.attendanceRate,
        changePercentage: comparison.changePercentage,
        trend: comparison.trend
      };
    } catch (error) {
      logger.error('Error getting dashboard attendance rate', LogCategory.ANALYTICS, error);
      // Return fallback data if calculation fails
      return {
        attendanceRate: 0,
        changePercentage: 0,
        trend: 'stable'
      };
    }
  }
}

// Create and export service instance
export const attendanceAnalyticsService = new AttendanceAnalyticsService();
