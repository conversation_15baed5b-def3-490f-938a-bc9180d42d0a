// lib/services/dashboard/activity-service.ts
import { connectToDatabase } from '@/lib/backend/database';
import AuditLog, { IAuditLog } from '@/models/payroll/AuditLog';
import User from '@/models/User';
import Employee from '@/models/Employee';
import Department from '@/models/Department';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import mongoose from 'mongoose';

/**
 * Interface for dashboard activity
 */
export interface DashboardActivity {
  id: string;
  user: {
    name: string;
    avatar: string;
    initials: string;
    email?: string;
  };
  employee?: {
    name: string;
    id: string;
  };
  action: string;
  department: string;
  time: string;
  type: string;
  timestamp: Date;
  module: string;
  entityType: string;
  entityId: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  metadata?: Record<string, any>;
}

/**
 * Interface for activity statistics
 */
export interface ActivityStats {
  totalActivities: number;
  todayActivities: number;
  weekActivities: number;
  moduleBreakdown: Array<{ module: string; count: number; percentage: number }>;
  actionBreakdown: Array<{ action: string; count: number; percentage: number }>;
  userBreakdown: Array<{ userId: string; userName: string; count: number }>;
  recentTrends: Array<{ date: string; count: number }>;
}

/**
 * Service for managing dashboard activities
 */
export class DashboardActivityService {
  /**
   * Get recent activities for dashboard display
   */
  async getRecentActivities(
    limit: number = 10,
    filters?: {
      modules?: string[];
      actions?: string[];
      severity?: string[];
      userId?: string;
      startDate?: Date;
      endDate?: Date;
    }
  ): Promise<DashboardActivity[]> {
    try {
      await connectToDatabase();
      
      logger.info('Fetching recent activities for dashboard', LogCategory.DASHBOARD, { 
        limit, 
        filters 
      });

      // Build query
      const query: any = {
        isArchived: false,
        isError: false, // Only show successful operations for dashboard
      };

      // Apply filters
      if (filters) {
        if (filters.modules && filters.modules.length > 0) {
          query.module = { $in: filters.modules };
        }
        
        if (filters.actions && filters.actions.length > 0) {
          query.action = { $in: filters.actions };
        }
        
        if (filters.severity && filters.severity.length > 0) {
          query.severity = { $in: filters.severity };
        }
        
        if (filters.userId) {
          query.userId = new mongoose.Types.ObjectId(filters.userId);
        }
        
        if (filters.startDate || filters.endDate) {
          query.timestamp = {};
          if (filters.startDate) {
            query.timestamp.$gte = filters.startDate;
          }
          if (filters.endDate) {
            query.timestamp.$lte = filters.endDate;
          }
        }
      }

      // Fetch audit logs with user information
      const auditLogs = await AuditLog.find(query)
        .populate('userId', 'firstName lastName email avatar')
        .sort({ timestamp: -1 })
        .limit(limit)
        .lean();

      // Transform audit logs to dashboard activities
      const activities = await Promise.all(
        auditLogs.map(log => this.transformAuditLogToActivity(log))
      );

      logger.info('Successfully fetched recent activities', LogCategory.DASHBOARD, {
        count: activities.length,
        limit
      });

      return activities.filter(activity => activity !== null) as DashboardActivity[];
    } catch (error) {
      logger.error('Error fetching recent activities', LogCategory.DASHBOARD, error);
      throw error;
    }
  }

  /**
   * Get activity statistics for dashboard metrics
   */
  async getActivityStats(
    period: 'day' | 'week' | 'month' = 'week'
  ): Promise<ActivityStats> {
    try {
      await connectToDatabase();
      
      logger.info('Generating activity statistics', LogCategory.DASHBOARD, { period });

      const now = new Date();
      const startDate = this.getStartDateForPeriod(period, now);

      // Get total activities
      const totalActivities = await AuditLog.countDocuments({
        isArchived: false,
        timestamp: { $gte: startDate }
      });

      // Get today's activities
      const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const todayActivities = await AuditLog.countDocuments({
        isArchived: false,
        timestamp: { $gte: todayStart }
      });

      // Get week's activities
      const weekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const weekActivities = await AuditLog.countDocuments({
        isArchived: false,
        timestamp: { $gte: weekStart }
      });

      // Get module breakdown
      const moduleBreakdown = await this.getModuleBreakdown(startDate);
      
      // Get action breakdown
      const actionBreakdown = await this.getActionBreakdown(startDate);
      
      // Get user breakdown
      const userBreakdown = await this.getUserBreakdown(startDate);
      
      // Get recent trends
      const recentTrends = await this.getRecentTrends(period, now);

      const stats: ActivityStats = {
        totalActivities,
        todayActivities,
        weekActivities,
        moduleBreakdown,
        actionBreakdown,
        userBreakdown,
        recentTrends
      };

      logger.info('Successfully generated activity statistics', LogCategory.DASHBOARD, {
        totalActivities,
        todayActivities,
        weekActivities
      });

      return stats;
    } catch (error) {
      logger.error('Error generating activity statistics', LogCategory.DASHBOARD, error);
      throw error;
    }
  }

  /**
   * Transform audit log to dashboard activity
   */
  private async transformAuditLogToActivity(auditLog: any): Promise<DashboardActivity | null> {
    try {
      if (!auditLog.userId) {
        return null;
      }

      const user = auditLog.userId;
      const activity: DashboardActivity = {
        id: auditLog._id.toString(),
        user: {
          name: `${user.firstName} ${user.lastName}`,
          avatar: user.avatar || '/placeholder-user.jpg',
          initials: `${user.firstName[0]}${user.lastName[0]}`,
          email: user.email
        },
        action: this.getActionDescription(auditLog),
        department: await this.getDepartmentName(auditLog),
        time: this.getRelativeTime(auditLog.timestamp),
        type: this.getActivityType(auditLog),
        timestamp: auditLog.timestamp,
        module: auditLog.module,
        entityType: auditLog.entityType,
        entityId: auditLog.entityId,
        description: auditLog.description || this.generateDescription(auditLog),
        severity: auditLog.severity,
        metadata: auditLog.metadata
      };

      // Add employee information if relevant
      if (auditLog.entityType === 'Employee' && auditLog.entityId) {
        try {
          const employee = await Employee.findById(auditLog.entityId)
            .select('firstName lastName')
            .lean();
          
          if (employee) {
            activity.employee = {
              name: `${employee.firstName} ${employee.lastName}`,
              id: auditLog.entityId
            };
          }
        } catch (err) {
          // Employee might be deleted, continue without employee info
        }
      }

      return activity;
    } catch (error) {
      logger.warn('Error transforming audit log to activity', LogCategory.DASHBOARD, {
        auditLogId: auditLog._id,
        error
      });
      return null;
    }
  }

  /**
   * Get human-readable action description
   */
  private getActionDescription(auditLog: any): string {
    const { action, entityType, module } = auditLog;
    
    const actionMap: Record<string, string> = {
      'CREATE': 'created',
      'UPDATE': 'updated',
      'DELETE': 'deleted',
      'LOGIN': 'logged in',
      'LOGOUT': 'logged out',
      'APPROVE': 'approved',
      'REJECT': 'rejected',
      'SUBMIT': 'submitted',
      'CANCEL': 'cancelled'
    };

    const actionText = actionMap[action] || action.toLowerCase();
    
    if (entityType === 'Employee') {
      return `${actionText} employee record`;
    } else if (entityType === 'Budget') {
      return `${actionText} budget`;
    } else if (entityType === 'Income') {
      return `${actionText} income entry`;
    } else if (entityType === 'Expenditure') {
      return `${actionText} expenditure`;
    } else if (entityType === 'Leave') {
      return `${actionText} leave request`;
    } else {
      return `${actionText} ${entityType.toLowerCase()}`;
    }
  }

  /**
   * Get activity type for styling
   */
  private getActivityType(auditLog: any): string {
    const { action, entityType } = auditLog;
    
    if (action === 'CREATE') {
      if (entityType === 'Employee') return 'new-employee';
      return 'create';
    } else if (action === 'UPDATE') {
      if (entityType === 'Employee') return 'update-employee';
      return 'update';
    } else if (action === 'DELETE') {
      return 'delete';
    } else if (action === 'APPROVE') {
      return 'approve';
    } else if (action === 'REJECT') {
      return 'reject';
    }
    
    return 'general';
  }

  /**
   * Get department name from audit log
   */
  private async getDepartmentName(auditLog: any): Promise<string> {
    try {
      // Check if department is in metadata
      if (auditLog.metadata?.department) {
        return auditLog.metadata.department;
      }

      // If it's an employee-related action, get department from employee
      if (auditLog.entityType === 'Employee' && auditLog.entityId) {
        const employee = await Employee.findById(auditLog.entityId)
          .populate('departmentId', 'name')
          .select('departmentId')
          .lean();
        
        if (employee?.departmentId?.name) {
          return employee.departmentId.name;
        }
      }

      // Default based on module
      const moduleMap: Record<string, string> = {
        'employees': 'Human Resources',
        'payroll': 'Human Resources',
        'accounting': 'Finance',
        'income': 'Finance',
        'expenditure': 'Finance',
        'budget': 'Finance',
        'hr': 'Human Resources',
        'auth': 'System',
        'system': 'System'
      };

      return moduleMap[auditLog.module] || 'General';
    } catch (error) {
      return 'Unknown';
    }
  }

  /**
   * Get relative time string
   */
  private getRelativeTime(timestamp: Date): string {
    const now = new Date();
    const diff = now.getTime() - new Date(timestamp).getTime();
    
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    if (hours < 24) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    if (days < 7) return `${days} day${days > 1 ? 's' : ''} ago`;
    
    return new Date(timestamp).toLocaleDateString();
  }

  /**
   * Generate description if not provided
   */
  private generateDescription(auditLog: any): string {
    return `${auditLog.action} operation on ${auditLog.entityType} in ${auditLog.module} module`;
  }

  /**
   * Get start date for period
   */
  private getStartDateForPeriod(period: 'day' | 'week' | 'month', now: Date): Date {
    switch (period) {
      case 'day':
        return new Date(now.getFullYear(), now.getMonth(), now.getDate());
      case 'week':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      case 'month':
        return new Date(now.getFullYear(), now.getMonth(), 1);
      default:
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }
  }

  /**
   * Get module breakdown statistics
   */
  private async getModuleBreakdown(startDate: Date): Promise<Array<{ module: string; count: number; percentage: number }>> {
    const pipeline = [
      {
        $match: {
          isArchived: false,
          timestamp: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$module',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ];

    const results = await AuditLog.aggregate(pipeline);
    const total = results.reduce((sum, item) => sum + item.count, 0);

    return results.map(item => ({
      module: item._id,
      count: item.count,
      percentage: total > 0 ? Math.round((item.count / total) * 100) : 0
    }));
  }

  /**
   * Get action breakdown statistics
   */
  private async getActionBreakdown(startDate: Date): Promise<Array<{ action: string; count: number; percentage: number }>> {
    const pipeline = [
      {
        $match: {
          isArchived: false,
          timestamp: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$action',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ];

    const results = await AuditLog.aggregate(pipeline);
    const total = results.reduce((sum, item) => sum + item.count, 0);

    return results.map(item => ({
      action: item._id,
      count: item.count,
      percentage: total > 0 ? Math.round((item.count / total) * 100) : 0
    }));
  }

  /**
   * Get user breakdown statistics
   */
  private async getUserBreakdown(startDate: Date): Promise<Array<{ userId: string; userName: string; count: number }>> {
    const pipeline = [
      {
        $match: {
          isArchived: false,
          timestamp: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$userId',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      },
      {
        $limit: 10
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      {
        $unwind: '$user'
      }
    ];

    const results = await AuditLog.aggregate(pipeline);

    return results.map(item => ({
      userId: item._id.toString(),
      userName: `${item.user.firstName} ${item.user.lastName}`,
      count: item.count
    }));
  }

  /**
   * Get recent trends data
   */
  private async getRecentTrends(period: 'day' | 'week' | 'month', now: Date): Promise<Array<{ date: string; count: number }>> {
    const days = period === 'day' ? 1 : period === 'week' ? 7 : 30;
    const trends = [];

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

      const count = await AuditLog.countDocuments({
        isArchived: false,
        timestamp: {
          $gte: startOfDay,
          $lt: endOfDay
        }
      });

      trends.push({
        date: startOfDay.toISOString().split('T')[0],
        count
      });
    }

    return trends;
  }
}

// Export singleton instance
export const dashboardActivityService = new DashboardActivityService();
