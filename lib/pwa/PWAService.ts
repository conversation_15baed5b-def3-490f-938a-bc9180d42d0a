"use client"

interface OfflineLeaveRequest {
  id: string;
  data: any;
  timestamp: number;
  synced: boolean;
}

interface PWAInstallPrompt {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

class PWAService {
  private dbName = 'kawandama-hr-offline';
  private dbVersion = 1;
  private db: IDBDatabase | null = null;
  private installPrompt: PWAInstallPrompt | null = null;

  /**
   * Initialize the PWA service
   */
  async initialize(): Promise<void> {
    try {
      // Initialize IndexedDB
      await this.initializeDB();
      
      // Register service worker
      await this.registerServiceWorker();
      
      // Set up install prompt listener
      this.setupInstallPrompt();
      
      // Set up online/offline listeners
      this.setupNetworkListeners();
      
      console.log('PWA Service initialized successfully');
    } catch (error) {
      console.error('Error initializing PWA service:', error);
    }
  }

  /**
   * Initialize IndexedDB for offline storage
   */
  private async initializeDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // Create object stores
        if (!db.objectStoreNames.contains('leaveRequests')) {
          const leaveStore = db.createObjectStore('leaveRequests', { keyPath: 'id' });
          leaveStore.createIndex('timestamp', 'timestamp', { unique: false });
          leaveStore.createIndex('synced', 'synced', { unique: false });
        }
        
        if (!db.objectStoreNames.contains('notifications')) {
          const notificationStore = db.createObjectStore('notifications', { keyPath: 'id' });
          notificationStore.createIndex('timestamp', 'timestamp', { unique: false });
        }
        
        if (!db.objectStoreNames.contains('userPreferences')) {
          db.createObjectStore('userPreferences', { keyPath: 'key' });
        }
      };
    });
  }

  /**
   * Register service worker
   */
  private async registerServiceWorker(): Promise<void> {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js');
        console.log('Service Worker registered:', registration);
        
        // Listen for updates
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                // New version available
                this.notifyUpdate();
              }
            });
          }
        });
      } catch (error) {
        console.error('Service Worker registration failed:', error);
      }
    }
  }

  /**
   * Set up install prompt handling
   */
  private setupInstallPrompt(): void {
    window.addEventListener('beforeinstallprompt', (event) => {
      event.preventDefault();
      this.installPrompt = event as any;
      
      // Dispatch custom event to notify components
      window.dispatchEvent(new CustomEvent('pwa-install-available'));
    });
  }

  /**
   * Set up network status listeners
   */
  private setupNetworkListeners(): void {
    window.addEventListener('online', () => {
      console.log('App is online');
      this.syncOfflineData();
      window.dispatchEvent(new CustomEvent('pwa-online'));
    });
    
    window.addEventListener('offline', () => {
      console.log('App is offline');
      window.dispatchEvent(new CustomEvent('pwa-offline'));
    });
  }

  /**
   * Check if the app is installable
   */
  isInstallable(): boolean {
    return this.installPrompt !== null;
  }

  /**
   * Trigger app installation
   */
  async installApp(): Promise<boolean> {
    if (!this.installPrompt) {
      return false;
    }
    
    try {
      await this.installPrompt.prompt();
      const choiceResult = await this.installPrompt.userChoice;
      
      if (choiceResult.outcome === 'accepted') {
        console.log('User accepted the install prompt');
        this.installPrompt = null;
        return true;
      } else {
        console.log('User dismissed the install prompt');
        return false;
      }
    } catch (error) {
      console.error('Error during app installation:', error);
      return false;
    }
  }

  /**
   * Check if the app is running as PWA
   */
  isPWA(): boolean {
    return window.matchMedia('(display-mode: standalone)').matches ||
           (window.navigator as any).standalone === true;
  }

  /**
   * Check if the device is online
   */
  isOnline(): boolean {
    return navigator.onLine;
  }

  /**
   * Store leave request for offline submission
   */
  async storeOfflineLeaveRequest(requestData: any): Promise<string> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    
    const id = `offline-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const offlineRequest: OfflineLeaveRequest = {
      id,
      data: requestData,
      timestamp: Date.now(),
      synced: false
    };
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['leaveRequests'], 'readwrite');
      const store = transaction.objectStore('leaveRequests');
      const request = store.add(offlineRequest);
      
      request.onsuccess = () => resolve(id);
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Get all offline leave requests
   */
  async getOfflineLeaveRequests(): Promise<OfflineLeaveRequest[]> {
    if (!this.db) {
      return [];
    }
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['leaveRequests'], 'readonly');
      const store = transaction.objectStore('leaveRequests');
      const index = store.index('synced');
      const request = index.getAll(false);
      
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Mark offline leave request as synced
   */
  async markLeaveRequestSynced(id: string): Promise<void> {
    if (!this.db) {
      return;
    }
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['leaveRequests'], 'readwrite');
      const store = transaction.objectStore('leaveRequests');
      const getRequest = store.get(id);
      
      getRequest.onsuccess = () => {
        const request = getRequest.result;
        if (request) {
          request.synced = true;
          const updateRequest = store.put(request);
          updateRequest.onsuccess = () => resolve();
          updateRequest.onerror = () => reject(updateRequest.error);
        } else {
          resolve();
        }
      };
      
      getRequest.onerror = () => reject(getRequest.error);
    });
  }

  /**
   * Sync offline data when back online
   */
  async syncOfflineData(): Promise<void> {
    if (!this.isOnline()) {
      return;
    }
    
    try {
      const offlineRequests = await this.getOfflineLeaveRequests();
      
      for (const request of offlineRequests) {
        try {
          const response = await fetch('/api/leave/requests', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(request.data)
          });
          
          if (response.ok) {
            await this.markLeaveRequestSynced(request.id);
            console.log('Synced offline leave request:', request.id);
          }
        } catch (error) {
          console.error('Failed to sync leave request:', request.id, error);
        }
      }
    } catch (error) {
      console.error('Error syncing offline data:', error);
    }
  }

  /**
   * Store user preferences
   */
  async storeUserPreference(key: string, value: any): Promise<void> {
    if (!this.db) {
      return;
    }
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['userPreferences'], 'readwrite');
      const store = transaction.objectStore('userPreferences');
      const request = store.put({ key, value, timestamp: Date.now() });
      
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Get user preference
   */
  async getUserPreference(key: string): Promise<any> {
    if (!this.db) {
      return null;
    }
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['userPreferences'], 'readonly');
      const store = transaction.objectStore('userPreferences');
      const request = store.get(key);
      
      request.onsuccess = () => {
        const result = request.result;
        resolve(result ? result.value : null);
      };
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Request notification permission
   */
  async requestNotificationPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      return 'denied';
    }
    
    if (Notification.permission === 'default') {
      return await Notification.requestPermission();
    }
    
    return Notification.permission;
  }

  /**
   * Show local notification
   */
  async showNotification(title: string, options: NotificationOptions = {}): Promise<void> {
    const permission = await this.requestNotificationPermission();
    
    if (permission === 'granted') {
      if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
        // Use service worker for better notification handling
        navigator.serviceWorker.controller.postMessage({
          type: 'SHOW_NOTIFICATION',
          title,
          options
        });
      } else {
        // Fallback to regular notification
        new Notification(title, {
          icon: '/icons/icon-192x192.png',
          badge: '/icons/badge-72x72.png',
          ...options
        });
      }
    }
  }

  /**
   * Notify about app update
   */
  private notifyUpdate(): void {
    window.dispatchEvent(new CustomEvent('pwa-update-available'));
  }

  /**
   * Update the service worker
   */
  async updateServiceWorker(): Promise<void> {
    if ('serviceWorker' in navigator) {
      const registration = await navigator.serviceWorker.getRegistration();
      if (registration && registration.waiting) {
        registration.waiting.postMessage({ type: 'SKIP_WAITING' });
        window.location.reload();
      }
    }
  }
}

// Export singleton instance
export const pwaService = new PWAService();
