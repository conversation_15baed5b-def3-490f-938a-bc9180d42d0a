/**
 * Debug Services Package - Debug Store
 * 
 * React state management for debug services using Zustand
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import {
  DebugSession,
  DebugTestResult,
  DebugLogEntry,
  DebugTestSuite,
  DebugTest,
  DebugConfig,
  DebugTestStatus,
  DebugLogLevel
} from '../types';
import { DebugService } from '../core/debug-service';

interface DebugStore {
  // State
  isInitialized: boolean;
  currentSession: DebugSession | null;
  sessions: DebugSession[];
  testSuites: DebugTestSuite[];
  isRunning: boolean;
  runningTests: Set<string>;
  config: DebugConfig | null;
  
  // UI State
  isDebugPanelOpen: boolean;
  selectedTestSuite: string | null;
  logFilter: {
    level?: DebugLogLevel;
    category?: string;
    testId?: string;
  };
  
  // Actions
  initialize: (config: DebugConfig) => void;
  createSession: (name: string, metadata?: Record<string, any>) => Promise<DebugSession>;
  selectSession: (sessionId: string) => void;
  runTest: (test: DebugTest) => Promise<DebugTestResult>;
  runTestSuite: (suiteId: string) => Promise<DebugTestResult[]>;
  addLog: (sessionId: string, level: DebugLogLevel, message: string, details?: any) => void;
  updateConfig: (newConfig: Partial<DebugConfig>) => void;
  
  // UI Actions
  toggleDebugPanel: () => void;
  setSelectedTestSuite: (suiteId: string | null) => void;
  setLogFilter: (filter: Partial<DebugStore['logFilter']>) => void;
  clearLogs: (sessionId?: string) => void;
  clearResults: (sessionId?: string) => void;
  
  // Getters
  getCurrentSessionLogs: () => DebugLogEntry[];
  getCurrentSessionResults: () => DebugTestResult[];
  getFilteredLogs: () => DebugLogEntry[];
  getTestSuiteById: (id: string) => DebugTestSuite | undefined;
  getSessionStats: (sessionId?: string) => {
    total: number;
    passed: number;
    failed: number;
    running: number;
    pending: number;
  };
}

export const useDebugStore = create<DebugStore>()(
  devtools(
    (set, get) => ({
      // Initial State
      isInitialized: false,
      currentSession: null,
      sessions: [],
      testSuites: [],
      isRunning: false,
      runningTests: new Set(),
      config: null,
      
      // UI State
      isDebugPanelOpen: false,
      selectedTestSuite: null,
      logFilter: {},
      
      // Actions
      initialize: (config: DebugConfig) => {
        const debugService = DebugService.getInstance(config);
        
        // Set up event listeners
        debugService.on('session:start', ({ session }) => {
          set((state) => ({
            currentSession: session,
            sessions: [...state.sessions, session]
          }));
        });
        
        debugService.on('session:complete', ({ session }) => {
          set((state) => ({
            sessions: state.sessions.map(s => s.id === session.id ? session : s),
            currentSession: state.currentSession?.id === session.id ? null : state.currentSession
          }));
        });
        
        debugService.on('test:start', ({ test }) => {
          set((state) => ({
            runningTests: new Set([...state.runningTests, test.id]),
            isRunning: true
          }));
        });
        
        debugService.on('test:complete', ({ result }) => {
          set((state) => {
            const newRunningTests = new Set(state.runningTests);
            newRunningTests.delete(result.id);
            
            return {
              runningTests: newRunningTests,
              isRunning: newRunningTests.size > 0,
              sessions: state.sessions.map(session => {
                if (session.id === state.currentSession?.id) {
                  return {
                    ...session,
                    results: [...session.results, result]
                  };
                }
                return session;
              })
            };
          });
        });
        
        debugService.on('log:add', ({ entry }) => {
          set((state) => ({
            sessions: state.sessions.map(session => {
              if (session.id === state.currentSession?.id) {
                return {
                  ...session,
                  logs: [...session.logs, entry]
                };
              }
              return session;
            })
          }));
        });
        
        set({
          isInitialized: true,
          config,
          testSuites: debugService.getAllTestSuites()
        });
      },
      
      createSession: async (name: string, metadata?: Record<string, any>) => {
        const { config } = get();
        if (!config) throw new Error('Debug service not initialized');

        const debugService = DebugService.getInstance(config);
        const session = debugService.createSession(name, metadata);
        return session;
      },

      selectSession: (sessionId: string) => {
        const { config } = get();
        if (!config) return;

        const debugService = DebugService.getInstance(config);
        const session = debugService.getSession(sessionId);
        set({ currentSession: session || null });
      },

      runTest: async (test: DebugTest) => {
        const { config } = get();
        if (!config) throw new Error('Debug service not initialized');

        const debugService = DebugService.getInstance(config);
        return await debugService.runTest(test);
      },

      runTestSuite: async (suiteId: string) => {
        const { config } = get();
        if (!config) throw new Error('Debug service not initialized');

        const debugService = DebugService.getInstance(config);
        return await debugService.runTestSuite(suiteId);
      },

      addLog: (sessionId: string, level: DebugLogLevel, message: string, details?: any) => {
        const { config } = get();
        if (!config) return;

        const debugService = DebugService.getInstance(config);
        debugService.addLog(sessionId, level, message, details);
      },

      updateConfig: (newConfig: Partial<DebugConfig>) => {
        const { config } = get();
        if (!config) return;

        const debugService = DebugService.getInstance(config);
        debugService.updateConfig(newConfig);
        set((state) => ({
          config: state.config ? { ...state.config, ...newConfig } : null
        }));
      },
      
      // UI Actions
      toggleDebugPanel: () => {
        set((state) => ({ isDebugPanelOpen: !state.isDebugPanelOpen }));
      },
      
      setSelectedTestSuite: (suiteId: string | null) => {
        set({ selectedTestSuite: suiteId });
      },
      
      setLogFilter: (filter: Partial<DebugStore['logFilter']>) => {
        set((state) => ({
          logFilter: { ...state.logFilter, ...filter }
        }));
      },
      
      clearLogs: (sessionId?: string) => {
        set((state) => ({
          sessions: state.sessions.map(session => {
            if (!sessionId || session.id === sessionId) {
              return { ...session, logs: [] };
            }
            return session;
          })
        }));
      },
      
      clearResults: (sessionId?: string) => {
        set((state) => ({
          sessions: state.sessions.map(session => {
            if (!sessionId || session.id === sessionId) {
              return { ...session, results: [] };
            }
            return session;
          })
        }));
      },
      
      // Getters
      getCurrentSessionLogs: () => {
        const { currentSession } = get();
        return currentSession?.logs || [];
      },
      
      getCurrentSessionResults: () => {
        const { currentSession } = get();
        return currentSession?.results || [];
      },
      
      getFilteredLogs: () => {
        const { getCurrentSessionLogs, logFilter } = get();
        const logs = getCurrentSessionLogs();
        
        return logs.filter(log => {
          if (logFilter.level && log.level !== logFilter.level) return false;
          if (logFilter.category && log.category !== logFilter.category) return false;
          if (logFilter.testId && log.testId !== logFilter.testId) return false;
          return true;
        });
      },
      
      getTestSuiteById: (id: string) => {
        const { testSuites } = get();
        return testSuites.find(suite => suite.id === id);
      },
      
      getSessionStats: (sessionId?: string) => {
        const { sessions, currentSession } = get();
        const targetSession = sessionId 
          ? sessions.find(s => s.id === sessionId)
          : currentSession;
        
        if (!targetSession) {
          return { total: 0, passed: 0, failed: 0, running: 0, pending: 0 };
        }
        
        const results = targetSession.results;
        return {
          total: results.length,
          passed: results.filter(r => r.status === DebugTestStatus.SUCCESS).length,
          failed: results.filter(r => r.status === DebugTestStatus.FAILED).length,
          running: results.filter(r => r.status === DebugTestStatus.RUNNING).length,
          pending: results.filter(r => r.status === DebugTestStatus.PENDING).length
        };
      }
    }),
    {
      name: 'debug-store',
      partialize: (state) => ({
        // Only persist certain parts of the state
        config: state.config,
        logFilter: state.logFilter,
        selectedTestSuite: state.selectedTestSuite
      })
    }
  )
);

// Convenience hooks
export const useDebugSession = () => {
  const store = useDebugStore();
  return {
    currentSession: store.currentSession,
    sessions: store.sessions,
    createSession: store.createSession,
    selectSession: store.selectSession,
    stats: store.getSessionStats()
  };
};

export const useDebugTests = () => {
  const store = useDebugStore();
  return {
    testSuites: store.testSuites,
    selectedTestSuite: store.selectedTestSuite,
    setSelectedTestSuite: store.setSelectedTestSuite,
    runTest: store.runTest,
    runTestSuite: store.runTestSuite,
    isRunning: store.isRunning,
    runningTests: store.runningTests
  };
};

export const useDebugLogs = () => {
  const store = useDebugStore();
  return {
    logs: store.getCurrentSessionLogs(),
    filteredLogs: store.getFilteredLogs(),
    filter: store.logFilter,
    setFilter: store.setLogFilter,
    clearLogs: store.clearLogs,
    addLog: store.addLog
  };
};
