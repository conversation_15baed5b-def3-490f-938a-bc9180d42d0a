/**
 * Debug Services Package - Main Entry Point
 * 
 * A comprehensive debugging framework for Next.js applications
 * 
 * @example
 * ```typescript
 * import { DebugServices, DebugTestBuilder, CommonTests } from '@/lib/debug-services';
 * 
 * // Initialize debug services
 * const debugServices = DebugServices.initialize({
 *   environment: 'development',
 *   logLevel: DebugLogLevel.DEBUG,
 *   uiEnabled: true
 * });
 * 
 * // Create custom tests
 * const customTest = DebugTestBuilder
 *   .create('My Custom Test')
 *   .description('Test custom functionality')
 *   .type(DebugTestType.CUSTOM)
 *   .execute(async (context) => {
 *     // Your test logic here
 *     return {
 *       id: context.test.id,
 *       name: context.test.name,
 *       type: context.test.type,
 *       status: DebugTestStatus.SUCCESS,
 *       message: 'Test passed!',
 *       timestamp: new Date()
 *     };
 *   });
 * 
 * // Run tests
 * const session = await debugServices.createSession('My Debug Session');
 * const result = await debugServices.runTest(customTest);
 * ```
 */

// Core exports
export { DebugService } from './core/debug-service';

// Type exports
export * from './types';

// Store exports
export { 
  useDebugStore, 
  useDebugSession, 
  useDebugTests, 
  useDebugLogs 
} from './stores/debug-store';

// Component exports
export { DebugPanel } from './components/debug-panel';
export { DebugTestRunner } from './components/debug-test-runner';
export { DebugLogViewer } from './components/debug-log-viewer';
export { DebugResultsView } from './components/debug-results-view';

// Framework exports
export { 
  DebugTestBuilder, 
  DebugTestSuiteBuilder, 
  CommonTests 
} from './framework/test-builder';

// Configuration exports
export { 
  DebugConfigBuilder,
  DebugEnvironments,
  DebugConfigFactory,
  DebugConfigValidator,
  DEFAULT_DEBUG_CONFIG
} from './config/debug-config';

// Utility exports
export { DebugUtils } from './utils/debug-utils';

// Main Debug Services class for easy initialization
export class DebugServices {
  private static instance: DebugService | null = null;

  /**
   * Initialize the debug services with configuration
   */
  public static initialize(config?: Partial<import('./types').DebugConfig>): DebugService {
    const { DebugConfigFactory } = require('./config/debug-config');
    const { DebugService } = require('./core/debug-service');
    const finalConfig = config
      ? { ...DebugConfigFactory.fromEnvironment(), ...config }
      : DebugConfigFactory.fromEnvironment();

    this.instance = DebugService.getInstance(finalConfig);
    return this.instance;
  }

  /**
   * Get the current debug service instance
   */
  public static getInstance(): DebugService | null {
    return this.instance;
  }

  /**
   * Quick setup for common scenarios
   */
  public static quickSetup(environment: 'development' | 'production' | 'testing' | 'staging' = 'development'): DebugService {
    const { DebugEnvironments } = require('./config/debug-config');
    
    let config;
    switch (environment) {
      case 'production':
        config = DebugEnvironments.production();
        break;
      case 'testing':
        config = DebugEnvironments.testing();
        break;
      case 'staging':
        config = DebugEnvironments.staging();
        break;
      default:
        config = DebugEnvironments.development();
    }

    return this.initialize(config);
  }

  /**
   * Create a debug panel component with default configuration
   */
  public static createPanel(props?: Partial<import('./types').DebugPanelProps>) {
    const { DebugPanel } = require('./components/debug-panel');
    return DebugPanel;
  }

  /**
   * Create common test suites for quick setup
   */
  public static createCommonTestSuites() {
    const { CommonTests, DebugTestSuiteBuilder } = require('./framework/test-builder');
    
    return {
      basic: DebugTestSuiteBuilder
        .create('Basic Tests')
        .description('Essential system tests')
        .addTest(CommonTests.authenticationTest())
        .addTest(CommonTests.databaseConnectionTest())
        .build(),
        
      security: DebugTestSuiteBuilder
        .create('Security Tests')
        .description('Security and authentication tests')
        .addTest(CommonTests.authenticationTest())
        .build(),
        
      performance: DebugTestSuiteBuilder
        .create('Performance Tests')
        .description('Performance monitoring tests')
        .parallel(true)
        .build()
    };
  }
}

// Convenience function for React components
export function useDebugServices() {
  const { useDebugStore } = require('./stores/debug-store');
  const store = useDebugStore();

  return {
    ...store,
    service: store.config ? DebugServices.getInstance() : null
  };
}

// Export version information
export const VERSION = '1.0.0';
export const PACKAGE_NAME = 'debug-services';

// Export package metadata
export const PACKAGE_INFO = {
  name: PACKAGE_NAME,
  version: VERSION,
  description: 'Comprehensive debugging framework for Next.js applications',
  author: 'Debug Services Team',
  license: 'MIT',
  features: [
    'Comprehensive test framework',
    'Real-time debugging UI',
    'Configurable test suites',
    'Health monitoring',
    'Performance testing',
    'Security testing',
    'API endpoint testing',
    'Database testing',
    'Authentication testing',
    'Custom test creation',
    'Session management',
    'Log management',
    'Export capabilities',
    'Environment-specific configs',
    'Role-based access control'
  ],
  components: [
    'DebugPanel - Main debugging interface',
    'DebugTestRunner - Test execution component',
    'DebugLogViewer - Log display and filtering',
    'DebugResultsView - Test results visualization'
  ],
  apis: [
    '/api/debug-services/health - Health check endpoint',
    '/api/debug-services/test - Test execution endpoint'
  ]
};

// Default export for easy importing
export default DebugServices;
