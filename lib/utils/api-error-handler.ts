/**
 * Custom error class for API errors
 */
export class ApiError extends Error {
  status: number
  errors?: Record<string, string>
  
  constructor(message: string, status: number = 500, errors?: Record<string, string>) {
    super(message)
    this.name = "ApiError"
    this.status = status
    this.errors = errors
  }
}

/**
 * Handles API responses and throws appropriate errors
 * @param response - Fetch API response
 * @returns The parsed JSON response if successful
 * @throws ApiError if the response is not successful
 */
export async function handleApiResponse<T = any>(response: Response): Promise<T> {
  // If the response is not OK, handle the error
  if (!response.ok) {
    let errorMessage = "An unexpected error occurred"
    let errorDetails: Record<string, string> | undefined

    // Check if response is HTML (likely an error page)
    const contentType = response.headers.get('content-type')
    if (contentType && contentType.includes('text/html')) {
      if (response.status === 401 || response.status === 403) {
        errorMessage = "Authentication required. Please log in and try again."
      } else if (response.status >= 500) {
        errorMessage = "Server error occurred. Please try again later."
      } else {
        errorMessage = `Server error (${response.status}): Please check if you're logged in and the server is running`
      }
      throw new ApiError(errorMessage, response.status, errorDetails)
    }

    try {
      // Try to parse the error response
      const errorData = await response.json()

      // Use the error message from the response if available
      if (errorData.error) {
        errorMessage = errorData.error
      } else if (errorData.message) {
        errorMessage = errorData.message
      }

      // Use the error details if available
      if (errorData.errors) {
        errorDetails = errorData.errors
      }
    } catch (e) {
      // If we can't parse the error response, use the status text
      if (response.status === 401 || response.status === 403) {
        errorMessage = "Authentication required. Please log in and try again."
      } else if (response.status >= 500) {
        errorMessage = "Server error occurred. Please try again later."
      } else {
        errorMessage = response.statusText || errorMessage
      }
    }

    // Throw a custom error with the status code and message
    throw new ApiError(errorMessage, response.status, errorDetails)
  }

  // If the response is OK, parse and return the JSON
  try {
    return await response.json()
  } catch (e) {
    // Handle cases where response is not valid JSON
    throw new ApiError("Server returned invalid response format", response.status)
  }
}

/**
 * Makes an API request with error handling
 * @param url - The URL to fetch
 * @param options - Fetch options
 * @returns The parsed JSON response
 * @throws ApiError if the request fails
 */
export async function apiRequest<T = any>(
  url: string,
  options?: RequestInit
): Promise<T> {
  try {
    const response = await fetch(url, {
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
      ...options,
    })

    return await handleApiResponse<T>(response)
  } catch (error) {
    // If it's already an ApiError, rethrow it
    if (error instanceof ApiError) {
      throw error
    }

    // Handle network errors
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new ApiError("Network error: Please check your internet connection and try again", 0)
    }

    // Handle JSON parsing errors
    if (error instanceof SyntaxError && error.message.includes('Unexpected token')) {
      throw new ApiError("Server returned invalid response. Please check if you're logged in.", 0)
    }

    // Otherwise, wrap it in an ApiError
    throw new ApiError(
      error instanceof Error ? error.message : "Failed to make API request",
      500
    )
  }
}

/**
 * Makes a GET request with error handling
 */
export function apiGet<T = any>(url: string, options?: RequestInit): Promise<T> {
  return apiRequest<T>(url, { method: "GET", ...options })
}

/**
 * Makes a POST request with error handling
 */
export function apiPost<T = any>(
  url: string,
  data?: any,
  options?: RequestInit
): Promise<T> {
  return apiRequest<T>(url, {
    method: "POST",
    body: data ? JSON.stringify(data) : undefined,
    ...options,
  })
}

/**
 * Makes a PUT request with error handling
 */
export function apiPut<T = any>(
  url: string,
  data?: any,
  options?: RequestInit
): Promise<T> {
  return apiRequest<T>(url, {
    method: "PUT",
    body: data ? JSON.stringify(data) : undefined,
    ...options,
  })
}

/**
 * Makes a DELETE request with error handling
 */
export function apiDelete<T = any>(url: string, options?: RequestInit): Promise<T> {
  return apiRequest<T>(url, { method: "DELETE", ...options })
}
