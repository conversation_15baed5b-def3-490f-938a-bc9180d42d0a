import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import LeaveApprovalWorkflow from '@/models/leave/LeaveApprovalWorkflow';
import LeaveApprovalTemplate from '@/models/leave/LeaveApprovalTemplate';
import Leave from '@/models/leave/Leave';
import Employee from '@/models/Employee';
import User from '@/models/User';
import { addDays } from 'date-fns';

export interface WorkflowCreationResult {
  workflowId: string;
  totalSteps: number;
  currentStep: number;
  nextApprovers: string[];
}

export interface WorkflowActionResult {
  success: boolean;
  message: string;
  nextStep?: number;
  nextApprovers?: string[];
  workflowCompleted?: boolean;
  finalStatus?: 'approved' | 'rejected';
}

class LeaveApprovalWorkflowService {
  /**
   * Create approval workflow for a leave request
   * @param leaveRequestId - Leave request ID
   * @param userId - User ID creating the workflow
   * @returns Workflow creation result
   */
  async createWorkflowForLeaveRequest(leaveRequestId: string, userId: string): Promise<WorkflowCreationResult> {
    try {
      await connectToDatabase();
      logger.info('Creating approval workflow for leave request', LogCategory.HR, { leaveRequestId });

      // Get leave request details
      const leaveRequest = await Leave.findById(leaveRequestId)
        .populate('employeeId')
        .populate('leaveTypeId');

      if (!leaveRequest) {
        throw new Error(`Leave request with ID ${leaveRequestId} not found`);
      }

      // Find applicable approval template
      const template = await this.findApplicableTemplate(leaveRequest);
      
      if (!template) {
        throw new Error('No applicable approval template found for this leave request');
      }

      // Create workflow steps based on template
      const workflowSteps = await this.createWorkflowSteps(leaveRequest, template);

      // Create workflow
      const workflow = new LeaveApprovalWorkflow({
        leaveRequestId: new mongoose.Types.ObjectId(leaveRequestId),
        workflowSteps,
        currentStep: 1,
        overallStatus: 'pending',
        workflowType: template.workflowType,
        autoEscalationEnabled: template.escalationSettings.enabled,
        escalationDays: template.escalationSettings.escalationDays,
        createdBy: new mongoose.Types.ObjectId(userId)
      });

      await workflow.save();

      // Get next approvers
      const nextApprovers = await this.getStepApprovers(workflowSteps[0], leaveRequest);

      return {
        workflowId: workflow._id.toString(),
        totalSteps: workflowSteps.length,
        currentStep: 1,
        nextApprovers: nextApprovers.map(a => a.toString())
      };
    } catch (error) {
      logger.error('Error creating approval workflow', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Approve a workflow step
   * @param workflowId - Workflow ID
   * @param stepNumber - Step number to approve
   * @param approverId - Approver user ID
   * @param comments - Optional comments
   * @returns Workflow action result
   */
  async approveStep(
    workflowId: string, 
    stepNumber: number, 
    approverId: string, 
    comments?: string
  ): Promise<WorkflowActionResult> {
    try {
      await connectToDatabase();
      logger.info('Approving workflow step', LogCategory.HR, { workflowId, stepNumber, approverId });

      const workflow = await LeaveApprovalWorkflow.findById(workflowId);
      if (!workflow) {
        throw new Error(`Workflow with ID ${workflowId} not found`);
      }

      // Find the step to approve
      const stepIndex = workflow.workflowSteps.findIndex(step => step.stepNumber === stepNumber);
      if (stepIndex === -1) {
        throw new Error(`Step ${stepNumber} not found in workflow`);
      }

      const step = workflow.workflowSteps[stepIndex];

      // Validate approver
      const isValidApprover = await this.validateApprover(step, approverId);
      if (!isValidApprover) {
        throw new Error('User is not authorized to approve this step');
      }

      // Update step status
      step.status = 'approved';
      step.approvedBy = new mongoose.Types.ObjectId(approverId);
      step.approvedDate = new Date();
      step.comments = comments;

      // Check if this was the last step
      const isLastStep = stepNumber === workflow.workflowSteps.length;
      
      if (isLastStep) {
        // Workflow completed - approve the leave request
        workflow.overallStatus = 'approved';
        workflow.updatedBy = new mongoose.Types.ObjectId(approverId);
        
        // Update leave request status
        await Leave.findByIdAndUpdate(workflow.leaveRequestId, {
          status: 'approved',
          approvedBy: new mongoose.Types.ObjectId(approverId),
          approvalDate: new Date()
        });

        await workflow.save();

        return {
          success: true,
          message: 'Leave request approved successfully',
          workflowCompleted: true,
          finalStatus: 'approved'
        };
      } else {
        // Move to next step
        workflow.currentStep = stepNumber + 1;
        workflow.updatedBy = new mongoose.Types.ObjectId(approverId);
        
        await workflow.save();

        // Get next step approvers
        const nextStep = workflow.workflowSteps.find(s => s.stepNumber === workflow.currentStep);
        const leaveRequest = await Leave.findById(workflow.leaveRequestId).populate('employeeId').populate('leaveTypeId');
        const nextApprovers = nextStep ? await this.getStepApprovers(nextStep, leaveRequest) : [];

        return {
          success: true,
          message: 'Step approved successfully. Moved to next approval level.',
          nextStep: workflow.currentStep,
          nextApprovers: nextApprovers.map(a => a.toString()),
          workflowCompleted: false
        };
      }
    } catch (error) {
      logger.error('Error approving workflow step', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Reject a workflow step
   * @param workflowId - Workflow ID
   * @param stepNumber - Step number to reject
   * @param approverId - Approver user ID
   * @param rejectionReason - Reason for rejection
   * @returns Workflow action result
   */
  async rejectStep(
    workflowId: string, 
    stepNumber: number, 
    approverId: string, 
    rejectionReason: string
  ): Promise<WorkflowActionResult> {
    try {
      await connectToDatabase();
      logger.info('Rejecting workflow step', LogCategory.HR, { workflowId, stepNumber, approverId });

      const workflow = await LeaveApprovalWorkflow.findById(workflowId);
      if (!workflow) {
        throw new Error(`Workflow with ID ${workflowId} not found`);
      }

      // Find the step to reject
      const stepIndex = workflow.workflowSteps.findIndex(step => step.stepNumber === stepNumber);
      if (stepIndex === -1) {
        throw new Error(`Step ${stepNumber} not found in workflow`);
      }

      const step = workflow.workflowSteps[stepIndex];

      // Validate approver
      const isValidApprover = await this.validateApprover(step, approverId);
      if (!isValidApprover) {
        throw new Error('User is not authorized to reject this step');
      }

      // Update step status
      step.status = 'rejected';
      step.approvedBy = new mongoose.Types.ObjectId(approverId);
      step.approvedDate = new Date();
      step.rejectionReason = rejectionReason;

      // Reject the entire workflow
      workflow.overallStatus = 'rejected';
      workflow.updatedBy = new mongoose.Types.ObjectId(approverId);

      // Update leave request status
      await Leave.findByIdAndUpdate(workflow.leaveRequestId, {
        status: 'rejected',
        approvedBy: new mongoose.Types.ObjectId(approverId),
        approvalDate: new Date(),
        rejectionReason
      });

      await workflow.save();

      return {
        success: true,
        message: 'Leave request rejected successfully',
        workflowCompleted: true,
        finalStatus: 'rejected'
      };
    } catch (error) {
      logger.error('Error rejecting workflow step', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Delegate a workflow step to another user
   * @param workflowId - Workflow ID
   * @param stepNumber - Step number to delegate
   * @param delegatorId - User ID delegating the step
   * @param delegateToId - User ID to delegate to
   * @param delegationReason - Reason for delegation
   * @returns Workflow action result
   */
  async delegateStep(
    workflowId: string,
    stepNumber: number,
    delegatorId: string,
    delegateToId: string,
    delegationReason: string
  ): Promise<WorkflowActionResult> {
    try {
      await connectToDatabase();
      logger.info('Delegating workflow step', LogCategory.HR, { workflowId, stepNumber, delegatorId, delegateToId });

      const workflow = await LeaveApprovalWorkflow.findById(workflowId);
      if (!workflow) {
        throw new Error(`Workflow with ID ${workflowId} not found`);
      }

      // Find the step to delegate
      const stepIndex = workflow.workflowSteps.findIndex(step => step.stepNumber === stepNumber);
      if (stepIndex === -1) {
        throw new Error(`Step ${stepNumber} not found in workflow`);
      }

      const step = workflow.workflowSteps[stepIndex];

      // Check if delegation is allowed
      if (!step.canDelegate) {
        throw new Error('Delegation is not allowed for this step');
      }

      // Validate delegator
      const isValidDelegator = await this.validateApprover(step, delegatorId);
      if (!isValidDelegator) {
        throw new Error('User is not authorized to delegate this step');
      }

      // Validate delegate
      const delegateUser = await User.findById(delegateToId);
      if (!delegateUser) {
        throw new Error('Delegate user not found');
      }

      // Update step with delegation info
      step.status = 'delegated';
      step.delegatedTo = new mongoose.Types.ObjectId(delegateToId);
      step.delegatedDate = new Date();
      step.delegationReason = delegationReason;

      // Update approver to the delegate
      step.approverUserId = new mongoose.Types.ObjectId(delegateToId);
      step.approverType = 'specific-user';

      workflow.updatedBy = new mongoose.Types.ObjectId(delegatorId);
      await workflow.save();

      return {
        success: true,
        message: 'Step delegated successfully',
        nextApprovers: [delegateToId],
        workflowCompleted: false
      };
    } catch (error) {
      logger.error('Error delegating workflow step', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Find applicable approval template for a leave request
   * @param leaveRequest - Leave request document
   * @returns Applicable template or null
   */
  private async findApplicableTemplate(leaveRequest: any): Promise<any> {
    const employee = leaveRequest.employeeId;
    const leaveType = leaveRequest.leaveTypeId;

    // Find templates that match the criteria
    const templates = await LeaveApprovalTemplate.find({
      isActive: true,
      effectiveFrom: { $lte: new Date() },
      $or: [
        { effectiveTo: { $exists: false } },
        { effectiveTo: { $gte: new Date() } }
      ]
    }).sort({ priority: -1, isDefault: -1 });

    for (const template of templates) {
      let matches = true;

      // Check leave type
      if (template.applicabilityCriteria.leaveTypes?.length) {
        if (!template.applicabilityCriteria.leaveTypes.some(lt =>
          lt.toString() === leaveType._id.toString()
        )) {
          matches = false;
        }
      }

      // Check department
      if (matches && template.applicabilityCriteria.departments?.length) {
        if (!template.applicabilityCriteria.departments.some(dept =>
          dept.toString() === employee.departmentId?.toString()
        )) {
          matches = false;
        }
      }

      // Check role
      if (matches && template.applicabilityCriteria.roles?.length) {
        if (!template.applicabilityCriteria.roles.includes(employee.position)) {
          matches = false;
        }
      }

      // Check employment type
      if (matches && template.applicabilityCriteria.employmentTypes?.length) {
        if (!template.applicabilityCriteria.employmentTypes.includes(employee.employmentType)) {
          matches = false;
        }
      }

      // Check leave duration
      if (matches && template.applicabilityCriteria.minLeaveDays) {
        if (leaveRequest.duration < template.applicabilityCriteria.minLeaveDays) {
          matches = false;
        }
      }

      if (matches && template.applicabilityCriteria.maxLeaveDays) {
        if (leaveRequest.duration > template.applicabilityCriteria.maxLeaveDays) {
          matches = false;
        }
      }

      if (matches) {
        return template;
      }
    }

    // Return default template if no specific match found
    return await LeaveApprovalTemplate.findOne({ isDefault: true, isActive: true });
  }

  /**
   * Create workflow steps based on template
   * @param leaveRequest - Leave request document
   * @param template - Approval template
   * @returns Array of workflow steps
   */
  private async createWorkflowSteps(leaveRequest: any, template: any): Promise<any[]> {
    const steps = [];

    for (const stepTemplate of template.stepTemplates) {
      const step = {
        stepNumber: stepTemplate.stepNumber,
        approverType: stepTemplate.approverType,
        approverUserId: stepTemplate.approverUserId,
        approverRole: stepTemplate.approverRole,
        approverDepartment: stepTemplate.approverDepartment,
        isRequired: stepTemplate.isRequired,
        canDelegate: stepTemplate.canDelegate,
        status: 'pending'
      };

      // Resolve dynamic approvers
      if (stepTemplate.approverType === 'direct-manager') {
        const manager = await this.findDirectManager(leaveRequest.employeeId);
        if (manager) {
          step.approverUserId = manager._id;
        }
      } else if (stepTemplate.approverType === 'department-head') {
        const deptHead = await this.findDepartmentHead(leaveRequest.employeeId.departmentId);
        if (deptHead) {
          step.approverUserId = deptHead._id;
        }
      } else if (stepTemplate.approverType === 'hr-manager') {
        const hrManager = await this.findHRManager();
        if (hrManager) {
          step.approverUserId = hrManager._id;
        }
      }

      steps.push(step);
    }

    return steps;
  }

  /**
   * Get approvers for a workflow step
   * @param step - Workflow step
   * @param leaveRequest - Leave request document
   * @returns Array of approver user IDs
   */
  private async getStepApprovers(step: any, leaveRequest: any): Promise<mongoose.Types.ObjectId[]> {
    const approvers: mongoose.Types.ObjectId[] = [];

    if (step.approverUserId) {
      approvers.push(step.approverUserId);
    } else if (step.approverType === 'role-based' && step.approverRole) {
      const roleUsers = await User.find({ role: step.approverRole, isActive: true });
      approvers.push(...roleUsers.map(u => u._id));
    }

    return approvers;
  }

  /**
   * Validate if user can approve a step
   * @param step - Workflow step
   * @param userId - User ID to validate
   * @returns True if user can approve
   */
  private async validateApprover(step: any, userId: string): Promise<boolean> {
    if (step.approverUserId && step.approverUserId.toString() === userId) {
      return true;
    }

    if (step.approverType === 'role-based' && step.approverRole) {
      const user = await User.findById(userId);
      return user?.role === step.approverRole;
    }

    return false;
  }

  /**
   * Find direct manager for an employee
   * @param employee - Employee document
   * @returns Manager user or null
   */
  private async findDirectManager(employee: any): Promise<any> {
    if (employee.managerId) {
      return await User.findById(employee.managerId);
    }
    return null;
  }

  /**
   * Find department head for a department
   * @param departmentId - Department ID
   * @returns Department head user or null
   */
  private async findDepartmentHead(departmentId: mongoose.Types.ObjectId): Promise<any> {
    const Department = (await import('@/models/Department')).default;
    const department = await Department.findById(departmentId);
    if (department?.headId) {
      return await User.findById(department.headId);
    }
    return null;
  }

  /**
   * Find HR manager
   * @returns HR manager user or null
   */
  private async findHRManager(): Promise<any> {
    return await User.findOne({
      role: { $in: ['HR_MANAGER', 'HR_DIRECTOR'] },
      isActive: true
    });
  }
}

// Export singleton instance
export const leaveApprovalWorkflowService = new LeaveApprovalWorkflowService();
