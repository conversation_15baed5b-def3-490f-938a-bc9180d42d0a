import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import Leave from '@/models/leave/Leave';
import LeaveBalance from '@/models/leave/LeaveBalance';
import LeaveType from '@/models/leave/LeaveType';
import Employee from '@/models/Employee';
import { startOfYear, endOfYear, startOfMonth, endOfMonth, format, subMonths, subYears } from 'date-fns';

export interface LeaveUtilizationReport {
  totalEmployees: number;
  totalLeaveRequests: number;
  approvedRequests: number;
  rejectedRequests: number;
  pendingRequests: number;
  totalLeaveDaysTaken: number;
  averageLeaveDaysPerEmployee: number;
  utilizationByLeaveType: {
    leaveType: string;
    totalRequests: number;
    totalDays: number;
    averageDays: number;
  }[];
  utilizationByMonth: {
    month: string;
    totalRequests: number;
    totalDays: number;
  }[];
}

export interface DepartmentAnalyticsReport {
  departmentId: string;
  departmentName: string;
  totalEmployees: number;
  totalLeaveRequests: number;
  totalLeaveDays: number;
  averageLeaveDaysPerEmployee: number;
  utilizationRate: number; // Percentage of available leave days used
  leaveTypeBreakdown: {
    leaveType: string;
    requests: number;
    days: number;
  }[];
}

export interface TrendAnalysisReport {
  period: 'monthly' | 'quarterly' | 'yearly';
  trends: {
    period: string;
    totalRequests: number;
    totalDays: number;
    approvalRate: number;
    averageProcessingDays: number;
  }[];
  insights: {
    trendDirection: 'increasing' | 'decreasing' | 'stable';
    percentageChange: number;
    peakPeriod: string;
    lowPeriod: string;
  };
}

export interface EmployeeLeaveReport {
  employeeId: string;
  employeeName: string;
  department: string;
  position: string;
  totalRequests: number;
  totalDaysTaken: number;
  balances: {
    leaveType: string;
    totalDays: number;
    usedDays: number;
    remainingDays: number;
    utilizationRate: number;
  }[];
  recentRequests: {
    id: string;
    leaveType: string;
    startDate: string;
    endDate: string;
    days: number;
    status: string;
  }[];
}

class LeaveReportingService {
  /**
   * Generate leave utilization report
   * @param startDate - Start date for the report
   * @param endDate - End date for the report
   * @returns Leave utilization report
   */
  async generateUtilizationReport(startDate: Date, endDate: Date): Promise<LeaveUtilizationReport> {
    try {
      await connectToDatabase();
      logger.info('Generating leave utilization report', LogCategory.HR, { startDate, endDate });

      // Get total active employees
      const totalEmployees = await Employee.countDocuments({ isActive: true });

      // Get leave requests in the period
      const leaveRequests = await Leave.find({
        createdAt: { $gte: startDate, $lte: endDate }
      }).populate('leaveTypeId', 'name').populate('employeeId', 'firstName lastName');

      const totalLeaveRequests = leaveRequests.length;
      const approvedRequests = leaveRequests.filter(req => req.status === 'approved').length;
      const rejectedRequests = leaveRequests.filter(req => req.status === 'rejected').length;
      const pendingRequests = leaveRequests.filter(req => req.status === 'pending').length;

      // Calculate total leave days taken (only approved requests)
      const totalLeaveDaysTaken = leaveRequests
        .filter(req => req.status === 'approved')
        .reduce((sum, req) => sum + req.duration, 0);

      const averageLeaveDaysPerEmployee = totalEmployees > 0 ? totalLeaveDaysTaken / totalEmployees : 0;

      // Utilization by leave type
      const leaveTypeMap = new Map();
      leaveRequests.forEach(req => {
        if (req.status === 'approved') {
          const typeName = (req.leaveTypeId as any).name;
          if (!leaveTypeMap.has(typeName)) {
            leaveTypeMap.set(typeName, { totalRequests: 0, totalDays: 0 });
          }
          const current = leaveTypeMap.get(typeName);
          current.totalRequests += 1;
          current.totalDays += req.duration;
        }
      });

      const utilizationByLeaveType = Array.from(leaveTypeMap.entries()).map(([leaveType, data]) => ({
        leaveType,
        totalRequests: data.totalRequests,
        totalDays: data.totalDays,
        averageDays: data.totalRequests > 0 ? data.totalDays / data.totalRequests : 0
      }));

      // Utilization by month
      const monthMap = new Map();
      leaveRequests.forEach(req => {
        if (req.status === 'approved') {
          const month = format(new Date(req.startDate), 'yyyy-MM');
          if (!monthMap.has(month)) {
            monthMap.set(month, { totalRequests: 0, totalDays: 0 });
          }
          const current = monthMap.get(month);
          current.totalRequests += 1;
          current.totalDays += req.duration;
        }
      });

      const utilizationByMonth = Array.from(monthMap.entries()).map(([month, data]) => ({
        month,
        totalRequests: data.totalRequests,
        totalDays: data.totalDays
      })).sort((a, b) => a.month.localeCompare(b.month));

      return {
        totalEmployees,
        totalLeaveRequests,
        approvedRequests,
        rejectedRequests,
        pendingRequests,
        totalLeaveDaysTaken,
        averageLeaveDaysPerEmployee,
        utilizationByLeaveType,
        utilizationByMonth
      };
    } catch (error) {
      logger.error('Error generating utilization report', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Generate department analytics report
   * @param year - Year for the report
   * @returns Array of department analytics
   */
  async generateDepartmentAnalytics(year: number): Promise<DepartmentAnalyticsReport[]> {
    try {
      await connectToDatabase();
      logger.info('Generating department analytics report', LogCategory.HR, { year });

      const startDate = startOfYear(new Date(year, 0, 1));
      const endDate = endOfYear(new Date(year, 0, 1));

      // Get all departments with employees
      const departments = await Employee.aggregate([
        { $match: { isActive: true } },
        {
          $group: {
            _id: '$departmentId',
            totalEmployees: { $sum: 1 }
          }
        },
        {
          $lookup: {
            from: 'departments',
            localField: '_id',
            foreignField: '_id',
            as: 'department'
          }
        },
        { $unwind: '$department' }
      ]);

      const departmentReports: DepartmentAnalyticsReport[] = [];

      for (const dept of departments) {
        // Get employees in this department
        const employees = await Employee.find({ 
          departmentId: dept._id, 
          isActive: true 
        });

        const employeeIds = employees.map(emp => emp._id);

        // Get leave requests for this department
        const leaveRequests = await Leave.find({
          employeeId: { $in: employeeIds },
          createdAt: { $gte: startDate, $lte: endDate },
          status: 'approved'
        }).populate('leaveTypeId', 'name');

        const totalLeaveRequests = leaveRequests.length;
        const totalLeaveDays = leaveRequests.reduce((sum, req) => sum + req.duration, 0);
        const averageLeaveDaysPerEmployee = dept.totalEmployees > 0 ? totalLeaveDays / dept.totalEmployees : 0;

        // Get total available leave days for utilization rate
        const leaveBalances = await LeaveBalance.find({
          employeeId: { $in: employeeIds },
          year
        });

        const totalAvailableDays = leaveBalances.reduce((sum, balance) => sum + balance.totalDays, 0);
        const utilizationRate = totalAvailableDays > 0 ? (totalLeaveDays / totalAvailableDays) * 100 : 0;

        // Leave type breakdown
        const leaveTypeMap = new Map();
        leaveRequests.forEach(req => {
          const typeName = (req.leaveTypeId as any).name;
          if (!leaveTypeMap.has(typeName)) {
            leaveTypeMap.set(typeName, { requests: 0, days: 0 });
          }
          const current = leaveTypeMap.get(typeName);
          current.requests += 1;
          current.days += req.duration;
        });

        const leaveTypeBreakdown = Array.from(leaveTypeMap.entries()).map(([leaveType, data]) => ({
          leaveType,
          requests: data.requests,
          days: data.days
        }));

        departmentReports.push({
          departmentId: dept._id.toString(),
          departmentName: dept.department.name,
          totalEmployees: dept.totalEmployees,
          totalLeaveRequests,
          totalLeaveDays,
          averageLeaveDaysPerEmployee,
          utilizationRate,
          leaveTypeBreakdown
        });
      }

      return departmentReports;
    } catch (error) {
      logger.error('Error generating department analytics', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Generate trend analysis report
   * @param period - Period for trend analysis
   * @param periodsBack - Number of periods to go back
   * @returns Trend analysis report
   */
  async generateTrendAnalysis(
    period: 'monthly' | 'quarterly' | 'yearly',
    periodsBack: number = 12
  ): Promise<TrendAnalysisReport> {
    try {
      await connectToDatabase();
      logger.info('Generating trend analysis report', LogCategory.HR, { period, periodsBack });

      const trends = [];
      const currentDate = new Date();

      for (let i = 0; i < periodsBack; i++) {
        let startDate: Date;
        let endDate: Date;
        let periodLabel: string;

        if (period === 'monthly') {
          const targetDate = subMonths(currentDate, i);
          startDate = startOfMonth(targetDate);
          endDate = endOfMonth(targetDate);
          periodLabel = format(targetDate, 'yyyy-MM');
        } else if (period === 'quarterly') {
          const targetDate = subMonths(currentDate, i * 3);
          const quarter = Math.floor(targetDate.getMonth() / 3) + 1;
          startDate = new Date(targetDate.getFullYear(), (quarter - 1) * 3, 1);
          endDate = new Date(targetDate.getFullYear(), quarter * 3, 0);
          periodLabel = `${targetDate.getFullYear()}-Q${quarter}`;
        } else {
          const targetDate = subYears(currentDate, i);
          startDate = startOfYear(targetDate);
          endDate = endOfYear(targetDate);
          periodLabel = targetDate.getFullYear().toString();
        }

        // Get leave requests for this period
        const leaveRequests = await Leave.find({
          createdAt: { $gte: startDate, $lte: endDate }
        });

        const totalRequests = leaveRequests.length;
        const totalDays = leaveRequests
          .filter(req => req.status === 'approved')
          .reduce((sum, req) => sum + req.duration, 0);

        const approvedRequests = leaveRequests.filter(req => req.status === 'approved').length;
        const approvalRate = totalRequests > 0 ? (approvedRequests / totalRequests) * 100 : 0;

        // Calculate average processing days for approved/rejected requests
        const processedRequests = leaveRequests.filter(req => 
          req.status === 'approved' || req.status === 'rejected'
        );
        
        let totalProcessingDays = 0;
        processedRequests.forEach(req => {
          if (req.approvalDate) {
            const processingTime = Math.ceil(
              (new Date(req.approvalDate).getTime() - new Date(req.createdAt).getTime()) / (1000 * 60 * 60 * 24)
            );
            totalProcessingDays += processingTime;
          }
        });

        const averageProcessingDays = processedRequests.length > 0 ? 
          totalProcessingDays / processedRequests.length : 0;

        trends.unshift({
          period: periodLabel,
          totalRequests,
          totalDays,
          approvalRate,
          averageProcessingDays
        });
      }

      // Calculate insights
      const firstPeriod = trends[0];
      const lastPeriod = trends[trends.length - 1];
      
      let trendDirection: 'increasing' | 'decreasing' | 'stable' = 'stable';
      let percentageChange = 0;

      if (firstPeriod && lastPeriod && firstPeriod.totalRequests > 0) {
        percentageChange = ((lastPeriod.totalRequests - firstPeriod.totalRequests) / firstPeriod.totalRequests) * 100;
        
        if (percentageChange > 5) {
          trendDirection = 'increasing';
        } else if (percentageChange < -5) {
          trendDirection = 'decreasing';
        }
      }

      // Find peak and low periods
      const peakPeriod = trends.reduce((max, current) => 
        current.totalRequests > max.totalRequests ? current : max
      );
      
      const lowPeriod = trends.reduce((min, current) => 
        current.totalRequests < min.totalRequests ? current : min
      );

      return {
        period,
        trends,
        insights: {
          trendDirection,
          percentageChange,
          peakPeriod: peakPeriod.period,
          lowPeriod: lowPeriod.period
        }
      };
    } catch (error) {
      logger.error('Error generating trend analysis', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Generate employee leave report
   * @param employeeId - Employee ID
   * @param year - Year for the report
   * @returns Employee leave report
   */
  async generateEmployeeReport(employeeId: string, year: number): Promise<EmployeeLeaveReport> {
    try {
      await connectToDatabase();
      logger.info('Generating employee leave report', LogCategory.HR, { employeeId, year });

      // Get employee details
      const employee = await Employee.findById(employeeId).populate('departmentId', 'name');
      if (!employee) {
        throw new Error(`Employee with ID ${employeeId} not found`);
      }

      const startDate = startOfYear(new Date(year, 0, 1));
      const endDate = endOfYear(new Date(year, 0, 1));

      // Get leave requests for the year
      const leaveRequests = await Leave.find({
        employeeId: new mongoose.Types.ObjectId(employeeId),
        createdAt: { $gte: startDate, $lte: endDate }
      }).populate('leaveTypeId', 'name').sort({ createdAt: -1 });

      const totalRequests = leaveRequests.length;
      const totalDaysTaken = leaveRequests
        .filter(req => req.status === 'approved')
        .reduce((sum, req) => sum + req.duration, 0);

      // Get leave balances
      const leaveBalances = await LeaveBalance.find({
        employeeId: new mongoose.Types.ObjectId(employeeId),
        year
      }).populate('leaveTypeId', 'name');

      const balances = leaveBalances.map(balance => ({
        leaveType: (balance.leaveTypeId as any).name,
        totalDays: balance.totalDays,
        usedDays: balance.usedDays,
        remainingDays: balance.remainingDays,
        utilizationRate: balance.totalDays > 0 ? (balance.usedDays / balance.totalDays) * 100 : 0
      }));

      // Get recent requests (last 10)
      const recentRequests = leaveRequests.slice(0, 10).map(req => ({
        id: req._id.toString(),
        leaveType: (req.leaveTypeId as any).name,
        startDate: format(new Date(req.startDate), 'yyyy-MM-dd'),
        endDate: format(new Date(req.endDate), 'yyyy-MM-dd'),
        days: req.duration,
        status: req.status
      }));

      return {
        employeeId,
        employeeName: `${employee.firstName} ${employee.lastName}`,
        department: (employee.departmentId as any)?.name || 'Unknown',
        position: employee.position,
        totalRequests,
        totalDaysTaken,
        balances,
        recentRequests
      };
    } catch (error) {
      logger.error('Error generating employee report', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Export report data to CSV format
   * @param reportData - Report data to export
   * @param reportType - Type of report
   * @returns CSV string
   */
  async exportToCSV(reportData: any, reportType: string): Promise<string> {
    try {
      logger.info('Exporting report to CSV', LogCategory.HR, { reportType });

      let csvContent = '';

      switch (reportType) {
        case 'utilization':
          csvContent = this.generateUtilizationCSV(reportData);
          break;
        case 'department':
          csvContent = this.generateDepartmentCSV(reportData);
          break;
        case 'trend':
          csvContent = this.generateTrendCSV(reportData);
          break;
        case 'employee':
          csvContent = this.generateEmployeeCSV(reportData);
          break;
        default:
          throw new Error(`Unsupported report type: ${reportType}`);
      }

      return csvContent;
    } catch (error) {
      logger.error('Error exporting report to CSV', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Generate utilization report CSV
   */
  private generateUtilizationCSV(data: LeaveUtilizationReport): string {
    let csv = 'Leave Utilization Report\n\n';
    csv += 'Summary\n';
    csv += 'Metric,Value\n';
    csv += `Total Employees,${data.totalEmployees}\n`;
    csv += `Total Leave Requests,${data.totalLeaveRequests}\n`;
    csv += `Approved Requests,${data.approvedRequests}\n`;
    csv += `Rejected Requests,${data.rejectedRequests}\n`;
    csv += `Pending Requests,${data.pendingRequests}\n`;
    csv += `Total Leave Days Taken,${data.totalLeaveDaysTaken}\n`;
    csv += `Average Leave Days Per Employee,${data.averageLeaveDaysPerEmployee.toFixed(2)}\n\n`;

    csv += 'Utilization by Leave Type\n';
    csv += 'Leave Type,Total Requests,Total Days,Average Days\n';
    data.utilizationByLeaveType.forEach(item => {
      csv += `${item.leaveType},${item.totalRequests},${item.totalDays},${item.averageDays.toFixed(2)}\n`;
    });

    csv += '\nUtilization by Month\n';
    csv += 'Month,Total Requests,Total Days\n';
    data.utilizationByMonth.forEach(item => {
      csv += `${item.month},${item.totalRequests},${item.totalDays}\n`;
    });

    return csv;
  }

  /**
   * Generate department analytics CSV
   */
  private generateDepartmentCSV(data: DepartmentAnalyticsReport[]): string {
    let csv = 'Department Analytics Report\n\n';
    csv += 'Department,Total Employees,Total Requests,Total Days,Avg Days Per Employee,Utilization Rate (%)\n';

    data.forEach(dept => {
      csv += `${dept.departmentName},${dept.totalEmployees},${dept.totalLeaveRequests},${dept.totalLeaveDays},${dept.averageLeaveDaysPerEmployee.toFixed(2)},${dept.utilizationRate.toFixed(2)}\n`;
    });

    return csv;
  }

  /**
   * Generate trend analysis CSV
   */
  private generateTrendCSV(data: TrendAnalysisReport): string {
    let csv = 'Trend Analysis Report\n\n';
    csv += 'Period,Total Requests,Total Days,Approval Rate (%),Avg Processing Days\n';

    data.trends.forEach(trend => {
      csv += `${trend.period},${trend.totalRequests},${trend.totalDays},${trend.approvalRate.toFixed(2)},${trend.averageProcessingDays.toFixed(2)}\n`;
    });

    csv += '\nInsights\n';
    csv += 'Metric,Value\n';
    csv += `Trend Direction,${data.insights.trendDirection}\n`;
    csv += `Percentage Change,${data.insights.percentageChange.toFixed(2)}%\n`;
    csv += `Peak Period,${data.insights.peakPeriod}\n`;
    csv += `Low Period,${data.insights.lowPeriod}\n`;

    return csv;
  }

  /**
   * Generate employee report CSV
   */
  private generateEmployeeCSV(data: EmployeeLeaveReport): string {
    let csv = 'Employee Leave Report\n\n';
    csv += 'Employee Information\n';
    csv += 'Field,Value\n';
    csv += `Name,${data.employeeName}\n`;
    csv += `Department,${data.department}\n`;
    csv += `Position,${data.position}\n`;
    csv += `Total Requests,${data.totalRequests}\n`;
    csv += `Total Days Taken,${data.totalDaysTaken}\n\n`;

    csv += 'Leave Balances\n';
    csv += 'Leave Type,Total Days,Used Days,Remaining Days,Utilization Rate (%)\n';
    data.balances.forEach(balance => {
      csv += `${balance.leaveType},${balance.totalDays},${balance.usedDays},${balance.remainingDays},${balance.utilizationRate.toFixed(2)}\n`;
    });

    csv += '\nRecent Requests\n';
    csv += 'Leave Type,Start Date,End Date,Days,Status\n';
    data.recentRequests.forEach(request => {
      csv += `${request.leaveType},${request.startDate},${request.endDate},${request.days},${request.status}\n`;
    });

    return csv;
  }
}

// Export singleton instance
export const leaveReportingService = new LeaveReportingService();
