import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import LeaveAccrual from '@/models/leave/LeaveAccrual';
import LeaveAccrualRule from '@/models/leave/LeaveAccrualRule';
import LeaveBalance from '@/models/leave/LeaveBalance';
import Employee from '@/models/Employee';
import { differenceInMonths, format, startOfMonth, endOfMonth, addMonths, startOfYear } from 'date-fns';

export interface AccrualCalculationResult {
  employeeId: string;
  leaveTypeId: string;
  accruedDays: number;
  accrualPeriod: string;
  reason: string;
  eligible: boolean;
  errors?: string[];
}

export interface AccrualProcessResult {
  totalProcessed: number;
  totalEligible: number;
  totalErrors: number;
  results: AccrualCalculationResult[];
  errors: string[];
}

class LeaveAccrualService {
  /**
   * Process monthly accruals for all eligible employees
   * @param processingDate - Date to process accruals for
   * @param userId - User ID processing the accruals
   * @returns Processing results
   */
  async processMonthlyAccruals(processingDate: Date, userId: string): Promise<AccrualProcessResult> {
    try {
      await connectToDatabase();
      logger.info('Processing monthly leave accruals', LogCategory.HR, { processingDate });

      const accrualPeriod = format(processingDate, 'yyyy-MM');
      
      // Get all active monthly accrual rules
      const accrualRules = await LeaveAccrualRule.find({
        isActive: true,
        accrualFrequency: 'monthly',
        effectiveFrom: { $lte: processingDate },
        $or: [
          { effectiveTo: { $exists: false } },
          { effectiveTo: { $gte: processingDate } }
        ]
      }).populate('leaveTypeId');

      const results: AccrualCalculationResult[] = [];
      const errors: string[] = [];
      let totalProcessed = 0;
      let totalEligible = 0;
      let totalErrors = 0;

      for (const rule of accrualRules) {
        try {
          const ruleResults = await this.processAccrualRule(rule, processingDate, accrualPeriod, userId);
          results.push(...ruleResults.results);
          totalProcessed += ruleResults.totalProcessed;
          totalEligible += ruleResults.totalEligible;
          totalErrors += ruleResults.totalErrors;
          errors.push(...ruleResults.errors);
        } catch (error) {
          const errorMessage = `Error processing rule ${rule.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMessage);
          totalErrors++;
          logger.error('Error processing accrual rule', LogCategory.HR, { rule: rule.name, error });
        }
      }

      return {
        totalProcessed,
        totalEligible,
        totalErrors,
        results,
        errors
      };
    } catch (error) {
      logger.error('Error processing monthly accruals', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Process quarterly accruals for all eligible employees
   * @param processingDate - Date to process accruals for
   * @param userId - User ID processing the accruals
   * @returns Processing results
   */
  async processQuarterlyAccruals(processingDate: Date, userId: string): Promise<AccrualProcessResult> {
    try {
      await connectToDatabase();
      logger.info('Processing quarterly leave accruals', LogCategory.HR, { processingDate });

      const quarter = Math.ceil((processingDate.getMonth() + 1) / 3);
      const accrualPeriod = `${processingDate.getFullYear()}-Q${quarter}`;
      
      // Get all active quarterly accrual rules
      const accrualRules = await LeaveAccrualRule.find({
        isActive: true,
        accrualFrequency: 'quarterly',
        effectiveFrom: { $lte: processingDate },
        $or: [
          { effectiveTo: { $exists: false } },
          { effectiveTo: { $gte: processingDate } }
        ]
      }).populate('leaveTypeId');

      const results: AccrualCalculationResult[] = [];
      const errors: string[] = [];
      let totalProcessed = 0;
      let totalEligible = 0;
      let totalErrors = 0;

      for (const rule of accrualRules) {
        try {
          const ruleResults = await this.processAccrualRule(rule, processingDate, accrualPeriod, userId);
          results.push(...ruleResults.results);
          totalProcessed += ruleResults.totalProcessed;
          totalEligible += ruleResults.totalEligible;
          totalErrors += ruleResults.totalErrors;
          errors.push(...ruleResults.errors);
        } catch (error) {
          const errorMessage = `Error processing rule ${rule.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMessage);
          totalErrors++;
          logger.error('Error processing accrual rule', LogCategory.HR, { rule: rule.name, error });
        }
      }

      return {
        totalProcessed,
        totalEligible,
        totalErrors,
        results,
        errors
      };
    } catch (error) {
      logger.error('Error processing quarterly accruals', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Process annual accruals for all eligible employees
   * @param processingDate - Date to process accruals for
   * @param userId - User ID processing the accruals
   * @returns Processing results
   */
  async processAnnualAccruals(processingDate: Date, userId: string): Promise<AccrualProcessResult> {
    try {
      await connectToDatabase();
      logger.info('Processing annual leave accruals', LogCategory.HR, { processingDate });

      const accrualPeriod = processingDate.getFullYear().toString();
      
      // Get all active annual accrual rules
      const accrualRules = await LeaveAccrualRule.find({
        isActive: true,
        accrualFrequency: 'annually',
        effectiveFrom: { $lte: processingDate },
        $or: [
          { effectiveTo: { $exists: false } },
          { effectiveTo: { $gte: processingDate } }
        ]
      }).populate('leaveTypeId');

      const results: AccrualCalculationResult[] = [];
      const errors: string[] = [];
      let totalProcessed = 0;
      let totalEligible = 0;
      let totalErrors = 0;

      for (const rule of accrualRules) {
        try {
          const ruleResults = await this.processAccrualRule(rule, processingDate, accrualPeriod, userId);
          results.push(...ruleResults.results);
          totalProcessed += ruleResults.totalProcessed;
          totalEligible += ruleResults.totalEligible;
          totalErrors += ruleResults.totalErrors;
          errors.push(...ruleResults.errors);
        } catch (error) {
          const errorMessage = `Error processing rule ${rule.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMessage);
          totalErrors++;
          logger.error('Error processing accrual rule', LogCategory.HR, { rule: rule.name, error });
        }
      }

      return {
        totalProcessed,
        totalEligible,
        totalErrors,
        results,
        errors
      };
    } catch (error) {
      logger.error('Error processing annual accruals', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Process pro-rata accrual for a new employee
   * @param employeeId - Employee ID
   * @param hireDate - Employee hire date
   * @param userId - User ID processing the accrual
   * @returns Processing results
   */
  async processProRataAccrual(employeeId: string, hireDate: Date, userId: string): Promise<AccrualProcessResult> {
    try {
      await connectToDatabase();
      logger.info('Processing pro-rata leave accrual for new employee', LogCategory.HR, { employeeId, hireDate });

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        throw new Error(`Employee with ID ${employeeId} not found`);
      }

      const currentYear = new Date().getFullYear();
      const accrualPeriod = `${currentYear}-ProRata`;
      
      // Get all active accrual rules that support pro-rata
      const accrualRules = await LeaveAccrualRule.find({
        isActive: true,
        proRataEnabled: true,
        effectiveFrom: { $lte: hireDate },
        $or: [
          { effectiveTo: { $exists: false } },
          { effectiveTo: { $gte: hireDate } }
        ]
      }).populate('leaveTypeId');

      const results: AccrualCalculationResult[] = [];
      const errors: string[] = [];
      let totalProcessed = 0;
      let totalEligible = 0;
      let totalErrors = 0;

      for (const rule of accrualRules) {
        try {
          // Check if employee is eligible for this rule
          const isEligible = await this.checkEmployeeEligibility(employee, rule);
          
          if (isEligible) {
            totalEligible++;
            
            // Check if accrual already exists for this period
            const existingAccrual = await LeaveAccrual.findOne({
              employeeId: new mongoose.Types.ObjectId(employeeId),
              leaveTypeId: rule.leaveTypeId,
              accrualPeriod
            });

            if (existingAccrual) {
              results.push({
                employeeId,
                leaveTypeId: rule.leaveTypeId._id.toString(),
                accruedDays: 0,
                accrualPeriod,
                reason: 'Pro-rata accrual already processed',
                eligible: true
              });
              continue;
            }

            // Calculate pro-rata accrual
            const accruedDays = await this.calculateProRataAccrual(employee, rule, hireDate);
            
            if (accruedDays > 0) {
              // Create accrual record
              const accrual = new LeaveAccrual({
                employeeId: new mongoose.Types.ObjectId(employeeId),
                leaveTypeId: rule.leaveTypeId,
                accrualDate: hireDate,
                accruedDays,
                accrualType: 'pro-rata',
                accrualPeriod,
                reason: `Pro-rata accrual for hire date ${format(hireDate, 'yyyy-MM-dd')}`,
                isProcessed: false,
                createdBy: new mongoose.Types.ObjectId(userId)
              });

              await accrual.save();
              totalProcessed++;

              results.push({
                employeeId,
                leaveTypeId: rule.leaveTypeId._id.toString(),
                accruedDays,
                accrualPeriod,
                reason: accrual.reason,
                eligible: true
              });
            }
          } else {
            results.push({
              employeeId,
              leaveTypeId: rule.leaveTypeId._id.toString(),
              accruedDays: 0,
              accrualPeriod,
              reason: 'Employee not eligible for this leave type',
              eligible: false
            });
          }
        } catch (error) {
          const errorMessage = `Error processing pro-rata accrual for rule ${rule.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMessage);
          totalErrors++;
          logger.error('Error processing pro-rata accrual rule', LogCategory.HR, { rule: rule.name, employeeId, error });
        }
      }

      return {
        totalProcessed,
        totalEligible,
        totalErrors,
        results,
        errors
      };
    } catch (error) {
      logger.error('Error processing pro-rata accrual', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Process accrual rule for all eligible employees
   * @param rule - Accrual rule to process
   * @param processingDate - Date to process accruals for
   * @param accrualPeriod - Accrual period string
   * @param userId - User ID processing the accruals
   * @returns Processing results for this rule
   */
  private async processAccrualRule(
    rule: any,
    processingDate: Date,
    accrualPeriod: string,
    userId: string
  ): Promise<AccrualProcessResult> {
    const results: AccrualCalculationResult[] = [];
    const errors: string[] = [];
    let totalProcessed = 0;
    let totalEligible = 0;
    let totalErrors = 0;

    // Build employee query based on eligibility criteria
    const employeeQuery: any = { isActive: true };

    if (rule.eligibilityCriteria.departments?.length) {
      employeeQuery.departmentId = { $in: rule.eligibilityCriteria.departments };
    }

    if (rule.eligibilityCriteria.employmentTypes?.length) {
      employeeQuery.employmentType = { $in: rule.eligibilityCriteria.employmentTypes };
    }

    if (rule.eligibilityCriteria.roles?.length) {
      employeeQuery.position = { $in: rule.eligibilityCriteria.roles };
    }

    // Get eligible employees
    const employees = await Employee.find(employeeQuery);

    for (const employee of employees) {
      try {
        // Check detailed eligibility
        const isEligible = await this.checkEmployeeEligibility(employee, rule);

        if (isEligible) {
          totalEligible++;

          // Check if accrual already exists for this period
          const existingAccrual = await LeaveAccrual.findOne({
            employeeId: employee._id,
            leaveTypeId: rule.leaveTypeId,
            accrualPeriod
          });

          if (existingAccrual) {
            results.push({
              employeeId: employee._id.toString(),
              leaveTypeId: rule.leaveTypeId._id.toString(),
              accruedDays: 0,
              accrualPeriod,
              reason: 'Accrual already processed for this period',
              eligible: true
            });
            continue;
          }

          // Calculate accrual amount
          const accruedDays = await this.calculateAccrualAmount(employee, rule, processingDate);

          if (accruedDays > 0) {
            // Create accrual record
            const accrual = new LeaveAccrual({
              employeeId: employee._id,
              leaveTypeId: rule.leaveTypeId,
              accrualDate: processingDate,
              accruedDays,
              accrualType: rule.accrualFrequency,
              accrualPeriod,
              reason: `${rule.accrualFrequency} accrual for ${rule.leaveTypeId.name}`,
              isProcessed: false,
              createdBy: new mongoose.Types.ObjectId(userId)
            });

            await accrual.save();
            totalProcessed++;

            results.push({
              employeeId: employee._id.toString(),
              leaveTypeId: rule.leaveTypeId._id.toString(),
              accruedDays,
              accrualPeriod,
              reason: accrual.reason,
              eligible: true
            });
          }
        } else {
          results.push({
            employeeId: employee._id.toString(),
            leaveTypeId: rule.leaveTypeId._id.toString(),
            accruedDays: 0,
            accrualPeriod,
            reason: 'Employee not eligible for this leave type',
            eligible: false
          });
        }
      } catch (error) {
        const errorMessage = `Error processing accrual for employee ${employee._id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMessage);
        totalErrors++;
        logger.error('Error processing employee accrual', LogCategory.HR, { employeeId: employee._id, error });
      }
    }

    return {
      totalProcessed,
      totalEligible,
      totalErrors,
      results,
      errors
    };
  }

  /**
   * Check if employee is eligible for accrual rule
   * @param employee - Employee document
   * @param rule - Accrual rule
   * @returns True if eligible
   */
  private async checkEmployeeEligibility(employee: any, rule: any): Promise<boolean> {
    // Check minimum tenure
    if (rule.eligibilityCriteria.minTenureMonths) {
      const tenureMonths = differenceInMonths(new Date(), new Date(employee.hireDate));
      if (tenureMonths < rule.eligibilityCriteria.minTenureMonths) {
        return false;
      }
    }

    // Check employment type
    if (rule.eligibilityCriteria.employmentTypes?.length) {
      if (!rule.eligibilityCriteria.employmentTypes.includes(employee.employmentType)) {
        return false;
      }
    }

    // Check department
    if (rule.eligibilityCriteria.departments?.length) {
      if (!rule.eligibilityCriteria.departments.some((dept: any) =>
        dept.toString() === employee.departmentId?.toString()
      )) {
        return false;
      }
    }

    // Check role
    if (rule.eligibilityCriteria.roles?.length) {
      if (!rule.eligibilityCriteria.roles.includes(employee.position)) {
        return false;
      }
    }

    // Check grade
    if (rule.eligibilityCriteria.grades?.length) {
      if (!rule.eligibilityCriteria.grades.includes(employee.grade)) {
        return false;
      }
    }

    return true;
  }

  /**
   * Calculate accrual amount for employee based on rule
   * @param employee - Employee document
   * @param rule - Accrual rule
   * @param processingDate - Processing date
   * @returns Accrued days
   */
  private async calculateAccrualAmount(employee: any, rule: any, processingDate: Date): Promise<number> {
    switch (rule.accrualMethod) {
      case 'fixed':
        return rule.fixedDaysPerPeriod || 0;

      case 'tenure-based':
        return this.calculateTenureBasedAccrual(employee, rule, processingDate);

      case 'pro-rata':
        return this.calculateProRataAccrual(employee, rule, processingDate);

      default:
        return 0;
    }
  }

  /**
   * Calculate tenure-based accrual
   * @param employee - Employee document
   * @param rule - Accrual rule
   * @param processingDate - Processing date
   * @returns Accrued days
   */
  private calculateTenureBasedAccrual(employee: any, rule: any, processingDate: Date): number {
    const tenureMonths = differenceInMonths(processingDate, new Date(employee.hireDate));

    // Find applicable tenure rule
    const applicableRule = rule.tenureRules?.find((tr: any) => {
      return tenureMonths >= tr.minTenureMonths &&
             (!tr.maxTenureMonths || tenureMonths <= tr.maxTenureMonths);
    });

    return applicableRule?.daysPerPeriod || 0;
  }

  /**
   * Calculate pro-rata accrual
   * @param employee - Employee document
   * @param rule - Accrual rule
   * @param referenceDate - Reference date for calculation
   * @returns Accrued days
   */
  private async calculateProRataAccrual(employee: any, rule: any, referenceDate: Date): Promise<number> {
    const hireDate = new Date(employee.hireDate);
    const currentYear = referenceDate.getFullYear();

    // Determine start date for pro-rata calculation
    let startDate: Date;
    switch (rule.proRataStartDate) {
      case 'hire-date':
        startDate = hireDate;
        break;
      case 'year-start':
        startDate = startOfYear(referenceDate);
        break;
      default:
        startDate = hireDate;
    }

    // If hire date is in a different year, use year start
    if (hireDate.getFullYear() < currentYear) {
      startDate = startOfYear(referenceDate);
    }

    // Calculate months worked in the year
    const monthsWorked = differenceInMonths(referenceDate, startDate) + 1;
    const totalMonthsInYear = 12;

    // Get annual entitlement (use fixed days or tenure-based)
    let annualEntitlement: number;
    if (rule.accrualMethod === 'tenure-based') {
      annualEntitlement = this.calculateTenureBasedAccrual(employee, rule, referenceDate) * 12; // Assuming monthly
    } else {
      annualEntitlement = rule.fixedDaysPerPeriod * 12; // Assuming monthly
    }

    // Calculate pro-rata amount
    const proRataAmount = (annualEntitlement * monthsWorked) / totalMonthsInYear;

    return Math.round(proRataAmount * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Apply accruals to leave balances
   * @param accrualIds - Array of accrual IDs to apply
   * @param userId - User ID applying the accruals
   * @returns Number of accruals applied
   */
  async applyAccrualsToBalances(accrualIds: string[], userId: string): Promise<number> {
    try {
      await connectToDatabase();
      logger.info('Applying accruals to leave balances', LogCategory.HR, { accrualIds });

      let appliedCount = 0;

      for (const accrualId of accrualIds) {
        try {
          const accrual = await LeaveAccrual.findById(accrualId);
          if (!accrual || accrual.isProcessed) {
            continue;
          }

          // Get or create leave balance
          const currentYear = new Date().getFullYear();
          let leaveBalance = await LeaveBalance.findOne({
            employeeId: accrual.employeeId,
            leaveTypeId: accrual.leaveTypeId,
            year: currentYear
          });

          if (!leaveBalance) {
            // Create new balance
            const LeaveType = (await import('@/models/leave/LeaveType')).default;
            const leaveType = await LeaveType.findById(accrual.leaveTypeId);

            leaveBalance = new LeaveBalance({
              employeeId: accrual.employeeId,
              leaveTypeId: accrual.leaveTypeId,
              year: currentYear,
              totalDays: accrual.accruedDays,
              usedDays: 0,
              pendingDays: 0,
              remainingDays: accrual.accruedDays,
              carryOverDays: 0,
              createdBy: new mongoose.Types.ObjectId(userId)
            });
          } else {
            // Update existing balance
            leaveBalance.totalDays += accrual.accruedDays;
            leaveBalance.remainingDays = leaveBalance.totalDays - leaveBalance.usedDays - leaveBalance.pendingDays;
            leaveBalance.updatedBy = new mongoose.Types.ObjectId(userId);
          }

          await leaveBalance.save();

          // Mark accrual as processed
          accrual.isProcessed = true;
          accrual.processedDate = new Date();
          accrual.processedBy = new mongoose.Types.ObjectId(userId);
          await accrual.save();

          appliedCount++;
        } catch (error) {
          logger.error('Error applying individual accrual', LogCategory.HR, { accrualId, error });
        }
      }

      return appliedCount;
    } catch (error) {
      logger.error('Error applying accruals to balances', LogCategory.HR, error);
      throw error;
    }
  }
}

// Export singleton instance
export const leaveAccrualService = new LeaveAccrualService();
