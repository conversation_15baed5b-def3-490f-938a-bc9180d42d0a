import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import LeaveEncashment from '@/models/leave/LeaveEncashment';
import LeaveEncashmentRule from '@/models/leave/LeaveEncashmentRule';
import LeaveBalance from '@/models/leave/LeaveBalance';
import Employee from '@/models/Employee';
import { differenceInMonths, format } from 'date-fns';

export interface EncashmentEligibilityResult {
  eligible: boolean;
  maxEncashableDays: number;
  minRetentionDays: number;
  availableForEncashment: number;
  ratePerDay: number;
  estimatedAmount: number;
  rule?: any;
  errors?: string[];
}

export interface EncashmentCalculationResult {
  daysToEncash: number;
  ratePerDay: number;
  totalAmount: number;
  balanceBeforeEncashment: number;
  balanceAfterEncashment: number;
  rule: any;
}

export interface EncashmentRequestPayload {
  employeeId: string;
  leaveTypeId: string;
  year: number;
  daysToEncash: number;
  reason?: string;
  notes?: string;
}

class LeaveEncashmentService {
  /**
   * Check encashment eligibility for an employee
   * @param employeeId - Employee ID
   * @param leaveTypeId - Leave type ID
   * @param year - Year for encashment
   * @returns Eligibility result
   */
  async checkEncashmentEligibility(
    employeeId: string,
    leaveTypeId: string,
    year: number
  ): Promise<EncashmentEligibilityResult> {
    try {
      await connectToDatabase();
      logger.info('Checking encashment eligibility', LogCategory.HR, { employeeId, leaveTypeId, year });

      // Get employee details
      const employee = await Employee.findById(employeeId).populate('departmentId');
      if (!employee) {
        return {
          eligible: false,
          maxEncashableDays: 0,
          minRetentionDays: 0,
          availableForEncashment: 0,
          ratePerDay: 0,
          estimatedAmount: 0,
          errors: ['Employee not found']
        };
      }

      // Find applicable encashment rule
      const rule = await this.findApplicableEncashmentRule(employee, leaveTypeId);
      if (!rule) {
        return {
          eligible: false,
          maxEncashableDays: 0,
          minRetentionDays: 0,
          availableForEncashment: 0,
          ratePerDay: 0,
          estimatedAmount: 0,
          errors: ['No applicable encashment rule found']
        };
      }

      // Get leave balance
      const leaveBalance = await LeaveBalance.findOne({
        employeeId: new mongoose.Types.ObjectId(employeeId),
        leaveTypeId: new mongoose.Types.ObjectId(leaveTypeId),
        year
      });

      if (!leaveBalance) {
        return {
          eligible: false,
          maxEncashableDays: 0,
          minRetentionDays: 0,
          availableForEncashment: 0,
          ratePerDay: 0,
          estimatedAmount: 0,
          errors: ['No leave balance found for the specified year']
        };
      }

      // Check if employee has already encashed this year
      const existingEncashments = await LeaveEncashment.countDocuments({
        employeeId: new mongoose.Types.ObjectId(employeeId),
        leaveTypeId: new mongoose.Types.ObjectId(leaveTypeId),
        year,
        status: { $in: ['pending', 'approved', 'processed', 'paid'] }
      });

      if (existingEncashments >= rule.maxEncashmentPerYear) {
        return {
          eligible: false,
          maxEncashableDays: 0,
          minRetentionDays: 0,
          availableForEncashment: 0,
          ratePerDay: 0,
          estimatedAmount: 0,
          errors: [`Maximum encashments per year (${rule.maxEncashmentPerYear}) already reached`]
        };
      }

      // Check timing restrictions
      const currentMonth = new Date().getMonth() + 1;
      if (rule.allowedMonths && rule.allowedMonths.length > 0) {
        if (!rule.allowedMonths.includes(currentMonth)) {
          return {
            eligible: false,
            maxEncashableDays: 0,
            minRetentionDays: 0,
            availableForEncashment: 0,
            ratePerDay: 0,
            estimatedAmount: 0,
            errors: [`Encashment not allowed in month ${currentMonth}`]
          };
        }
      }

      // Calculate available days for encashment
      const availableDays = leaveBalance.remainingDays;
      const maxEncashableDays = Math.min(rule.maxEncashableDays, availableDays - rule.minRetentionDays);
      const availableForEncashment = Math.max(0, maxEncashableDays);

      // Calculate rate per day
      const ratePerDay = await this.calculateDailyRate(employee, rule);

      // Calculate estimated amount
      const estimatedAmount = availableForEncashment * ratePerDay;

      return {
        eligible: availableForEncashment > 0,
        maxEncashableDays: rule.maxEncashableDays,
        minRetentionDays: rule.minRetentionDays,
        availableForEncashment,
        ratePerDay,
        estimatedAmount,
        rule,
        errors: availableForEncashment <= 0 ? ['No days available for encashment'] : undefined
      };
    } catch (error) {
      logger.error('Error checking encashment eligibility', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Create encashment request
   * @param payload - Encashment request payload
   * @param userId - User ID creating the request
   * @returns Created encashment record
   */
  async createEncashmentRequest(payload: EncashmentRequestPayload, userId: string): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Creating encashment request', LogCategory.HR, { payload, userId });

      // Check eligibility first
      const eligibility = await this.checkEncashmentEligibility(
        payload.employeeId,
        payload.leaveTypeId,
        payload.year
      );

      if (!eligibility.eligible) {
        throw new Error(`Encashment not eligible: ${eligibility.errors?.join(', ')}`);
      }

      if (payload.daysToEncash > eligibility.availableForEncashment) {
        throw new Error(`Cannot encash ${payload.daysToEncash} days. Maximum available: ${eligibility.availableForEncashment}`);
      }

      // Get leave balance
      const leaveBalance = await LeaveBalance.findOne({
        employeeId: new mongoose.Types.ObjectId(payload.employeeId),
        leaveTypeId: new mongoose.Types.ObjectId(payload.leaveTypeId),
        year: payload.year
      });

      if (!leaveBalance) {
        throw new Error('Leave balance not found');
      }

      // Calculate encashment details
      const calculation = await this.calculateEncashmentAmount(
        payload.employeeId,
        payload.leaveTypeId,
        payload.daysToEncash,
        eligibility.rule
      );

      // Generate encashment ID
      const encashmentId = await this.generateEncashmentId();

      // Create encashment record
      const encashment = new LeaveEncashment({
        encashmentId,
        employeeId: new mongoose.Types.ObjectId(payload.employeeId),
        leaveTypeId: new mongoose.Types.ObjectId(payload.leaveTypeId),
        year: payload.year,
        daysEncashed: payload.daysToEncash,
        ratePerDay: calculation.ratePerDay,
        totalAmount: calculation.totalAmount,
        encashmentDate: new Date(),
        status: eligibility.rule.requiresApproval ? 'pending' : 'approved',
        balanceBeforeEncashment: calculation.balanceBeforeEncashment,
        balanceAfterEncashment: calculation.balanceAfterEncashment,
        encashmentRuleId: eligibility.rule._id,
        maxEncashableDays: eligibility.rule.maxEncashableDays,
        minRetentionDays: eligibility.rule.minRetentionDays,
        encashmentRate: eligibility.rule.encashmentRate,
        reason: payload.reason,
        notes: payload.notes,
        createdBy: new mongoose.Types.ObjectId(userId)
      });

      await encashment.save();

      // Update leave balance if auto-approved
      if (!eligibility.rule.requiresApproval) {
        leaveBalance.remainingDays -= payload.daysToEncash;
        leaveBalance.updatedBy = new mongoose.Types.ObjectId(userId);
        await leaveBalance.save();
      }

      return encashment;
    } catch (error) {
      logger.error('Error creating encashment request', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Approve encashment request
   * @param encashmentId - Encashment ID
   * @param approverId - Approver user ID
   * @returns Updated encashment record
   */
  async approveEncashmentRequest(encashmentId: string, approverId: string): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Approving encashment request', LogCategory.HR, { encashmentId, approverId });

      const encashment = await LeaveEncashment.findById(encashmentId);
      if (!encashment) {
        throw new Error(`Encashment with ID ${encashmentId} not found`);
      }

      if (encashment.status !== 'pending') {
        throw new Error(`Cannot approve encashment with status: ${encashment.status}`);
      }

      // Update encashment status
      encashment.status = 'approved';
      encashment.approvedBy = new mongoose.Types.ObjectId(approverId);
      encashment.approvalDate = new Date();
      encashment.updatedBy = new mongoose.Types.ObjectId(approverId);

      await encashment.save();

      // Update leave balance
      const leaveBalance = await LeaveBalance.findOne({
        employeeId: encashment.employeeId,
        leaveTypeId: encashment.leaveTypeId,
        year: encashment.year
      });

      if (leaveBalance) {
        leaveBalance.remainingDays -= encashment.daysEncashed;
        leaveBalance.updatedBy = new mongoose.Types.ObjectId(approverId);
        await leaveBalance.save();
      }

      return encashment;
    } catch (error) {
      logger.error('Error approving encashment request', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Reject encashment request
   * @param encashmentId - Encashment ID
   * @param approverId - Approver user ID
   * @param rejectionReason - Reason for rejection
   * @returns Updated encashment record
   */
  async rejectEncashmentRequest(encashmentId: string, approverId: string, rejectionReason: string): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Rejecting encashment request', LogCategory.HR, { encashmentId, approverId });

      const encashment = await LeaveEncashment.findById(encashmentId);
      if (!encashment) {
        throw new Error(`Encashment with ID ${encashmentId} not found`);
      }

      if (encashment.status !== 'pending') {
        throw new Error(`Cannot reject encashment with status: ${encashment.status}`);
      }

      // Update encashment status
      encashment.status = 'rejected';
      encashment.approvedBy = new mongoose.Types.ObjectId(approverId);
      encashment.approvalDate = new Date();
      encashment.rejectionReason = rejectionReason;
      encashment.updatedBy = new mongoose.Types.ObjectId(approverId);

      await encashment.save();

      return encashment;
    } catch (error) {
      logger.error('Error rejecting encashment request', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Find applicable encashment rule for employee and leave type
   * @param employee - Employee document
   * @param leaveTypeId - Leave type ID
   * @returns Applicable rule or null
   */
  private async findApplicableEncashmentRule(employee: any, leaveTypeId: string): Promise<any> {
    // Find rules that match the criteria
    const rules = await LeaveEncashmentRule.find({
      isActive: true,
      effectiveFrom: { $lte: new Date() },
      $or: [
        { effectiveTo: { $exists: false } },
        { effectiveTo: { $gte: new Date() } }
      ]
    }).sort({ isDefault: 1 }); // Default rules last

    for (const rule of rules) {
      let matches = true;

      // Check leave type
      if (rule.applicabilityCriteria.leaveTypes?.length) {
        if (!rule.applicabilityCriteria.leaveTypes.some((lt: any) =>
          lt.toString() === leaveTypeId
        )) {
          matches = false;
        }
      }

      // Check department
      if (matches && rule.applicabilityCriteria.departments?.length) {
        if (!rule.applicabilityCriteria.departments.some((dept: any) =>
          dept.toString() === employee.departmentId?.toString()
        )) {
          matches = false;
        }
      }

      // Check role
      if (matches && rule.applicabilityCriteria.roles?.length) {
        if (!rule.applicabilityCriteria.roles.includes(employee.position)) {
          matches = false;
        }
      }

      // Check employment type
      if (matches && rule.applicabilityCriteria.employmentTypes?.length) {
        if (!rule.applicabilityCriteria.employmentTypes.includes(employee.employmentType)) {
          matches = false;
        }
      }

      // Check tenure
      if (matches && rule.applicabilityCriteria.minTenureMonths) {
        const tenureMonths = differenceInMonths(new Date(), new Date(employee.hireDate));
        if (tenureMonths < rule.applicabilityCriteria.minTenureMonths) {
          matches = false;
        }
      }

      if (matches) {
        return rule;
      }
    }

    // Return default rule if no specific match found
    return await LeaveEncashmentRule.findOne({ isDefault: true, isActive: true });
  }

  /**
   * Calculate daily rate for encashment
   * @param employee - Employee document
   * @param rule - Encashment rule
   * @returns Daily rate amount
   */
  private async calculateDailyRate(employee: any, rule: any): Promise<number> {
    let dailyRate = 0;

    switch (rule.rateCalculationMethod) {
      case 'basic-salary':
        dailyRate = (employee.basicSalary || 0) / 30; // Assuming 30 days per month
        break;
      case 'gross-salary':
        dailyRate = (employee.grossSalary || employee.basicSalary || 0) / 30;
        break;
      case 'fixed-rate':
        dailyRate = rule.fixedRateAmount || 0;
        break;
      case 'custom':
        // Implement custom calculation logic here
        dailyRate = (employee.basicSalary || 0) / 30;
        break;
      default:
        dailyRate = (employee.basicSalary || 0) / 30;
    }

    // Apply encashment rate percentage
    return (dailyRate * rule.encashmentRate) / 100;
  }

  /**
   * Calculate encashment amount and balance impact
   * @param employeeId - Employee ID
   * @param leaveTypeId - Leave type ID
   * @param daysToEncash - Days to encash
   * @param rule - Encashment rule
   * @returns Calculation result
   */
  private async calculateEncashmentAmount(
    employeeId: string,
    leaveTypeId: string,
    daysToEncash: number,
    rule: any
  ): Promise<EncashmentCalculationResult> {
    const employee = await Employee.findById(employeeId);
    if (!employee) {
      throw new Error('Employee not found');
    }

    const leaveBalance = await LeaveBalance.findOne({
      employeeId: new mongoose.Types.ObjectId(employeeId),
      leaveTypeId: new mongoose.Types.ObjectId(leaveTypeId),
      year: new Date().getFullYear()
    });

    if (!leaveBalance) {
      throw new Error('Leave balance not found');
    }

    const ratePerDay = await this.calculateDailyRate(employee, rule);
    const totalAmount = daysToEncash * ratePerDay;
    const balanceBeforeEncashment = leaveBalance.remainingDays;
    const balanceAfterEncashment = balanceBeforeEncashment - daysToEncash;

    return {
      daysToEncash,
      ratePerDay,
      totalAmount,
      balanceBeforeEncashment,
      balanceAfterEncashment,
      rule
    };
  }

  /**
   * Generate unique encashment ID
   * @returns Encashment ID
   */
  private async generateEncashmentId(): Promise<string> {
    const year = new Date().getFullYear();
    const prefix = `ENC${year}`;

    // Find the last encashment ID for this year
    const lastEncashment = await LeaveEncashment.findOne({
      encashmentId: { $regex: `^${prefix}` }
    }).sort({ encashmentId: -1 });

    let nextNumber = 1;
    if (lastEncashment) {
      const lastNumber = parseInt(lastEncashment.encashmentId.replace(prefix, ''));
      nextNumber = lastNumber + 1;
    }

    return `${prefix}${nextNumber.toString().padStart(4, '0')}`;
  }

  /**
   * Process encashment for payroll
   * @param encashmentId - Encashment ID
   * @param payrollRunId - Payroll run ID
   * @param userId - User ID processing the encashment
   * @returns Updated encashment record
   */
  async processEncashmentForPayroll(encashmentId: string, payrollRunId: string, userId: string): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Processing encashment for payroll', LogCategory.HR, { encashmentId, payrollRunId });

      const encashment = await LeaveEncashment.findById(encashmentId);
      if (!encashment) {
        throw new Error(`Encashment with ID ${encashmentId} not found`);
      }

      if (encashment.status !== 'approved') {
        throw new Error(`Cannot process encashment with status: ${encashment.status}`);
      }

      // Update encashment status
      encashment.status = 'processed';
      encashment.payrollRunId = new mongoose.Types.ObjectId(payrollRunId);
      encashment.payrollProcessedDate = new Date();
      encashment.updatedBy = new mongoose.Types.ObjectId(userId);

      await encashment.save();

      return encashment;
    } catch (error) {
      logger.error('Error processing encashment for payroll', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Mark encashment as paid
   * @param encashmentId - Encashment ID
   * @param paymentReference - Payment reference
   * @param userId - User ID marking as paid
   * @returns Updated encashment record
   */
  async markEncashmentAsPaid(encashmentId: string, paymentReference: string, userId: string): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Marking encashment as paid', LogCategory.HR, { encashmentId, paymentReference });

      const encashment = await LeaveEncashment.findById(encashmentId);
      if (!encashment) {
        throw new Error(`Encashment with ID ${encashmentId} not found`);
      }

      if (encashment.status !== 'processed') {
        throw new Error(`Cannot mark encashment as paid with status: ${encashment.status}`);
      }

      // Update encashment status
      encashment.status = 'paid';
      encashment.paymentReference = paymentReference;
      encashment.updatedBy = new mongoose.Types.ObjectId(userId);

      await encashment.save();

      return encashment;
    } catch (error) {
      logger.error('Error marking encashment as paid', LogCategory.HR, error);
      throw error;
    }
  }
}

// Export singleton instance
export const leaveEncashmentService = new LeaveEncashmentService();
