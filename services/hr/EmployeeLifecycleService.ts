import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import Employee from '@/models/Employee';
import LeaveType from '@/models/leave/LeaveType';
import LeaveBalance from '@/models/leave/LeaveBalance';
import { leaveAccrualService } from '@/services/leave/LeaveAccrualService';
import { differenceInMonths } from 'date-fns';

export interface EmployeeOnboardingResult {
  employeeId: string;
  leaveBalancesCreated: number;
  accrualRulesApplied: number;
  errors: string[];
}

export interface DepartmentTransferResult {
  employeeId: string;
  oldDepartment: string;
  newDepartment: string;
  leaveEntitlementsUpdated: boolean;
  workflowsUpdated: boolean;
  errors: string[];
}

export interface RoleChangeResult {
  employeeId: string;
  oldRole: string;
  newRole: string;
  leaveEntitlementsUpdated: boolean;
  accessUpdated: boolean;
  errors: string[];
}

class EmployeeLifecycleService {
  /**
   * Handle employee onboarding - set up leave balances and entitlements
   * @param employeeId - Employee ID
   * @param userId - User ID performing the action
   * @returns Onboarding result
   */
  async handleEmployeeOnboarding(employeeId: string, userId: string): Promise<EmployeeOnboardingResult> {
    try {
      await connectToDatabase();
      logger.info('Handling employee onboarding', LogCategory.HR, { employeeId });

      const employee = await Employee.findById(employeeId).populate('departmentId');
      if (!employee) {
        throw new Error(`Employee with ID ${employeeId} not found`);
      }

      const errors: string[] = [];
      let leaveBalancesCreated = 0;
      let accrualRulesApplied = 0;

      // Get all active leave types
      const leaveTypes = await LeaveType.find({ isActive: true });
      const currentYear = new Date().getFullYear();

      for (const leaveType of leaveTypes) {
        try {
          // Check if leave type is applicable to this employee
          const isApplicable = await this.isLeaveTypeApplicableToEmployee(employee, leaveType);
          
          if (isApplicable) {
            // Check if balance already exists
            const existingBalance = await LeaveBalance.findOne({
              employeeId: employee._id,
              leaveTypeId: leaveType._id,
              year: currentYear
            });

            if (!existingBalance) {
              // Calculate initial entitlement
              const initialEntitlement = await this.calculateInitialLeaveEntitlement(
                employee, 
                leaveType
              );

              // Create leave balance
              const leaveBalance = new LeaveBalance({
                employeeId: employee._id,
                leaveTypeId: leaveType._id,
                year: currentYear,
                totalDays: initialEntitlement,
                usedDays: 0,
                pendingDays: 0,
                remainingDays: initialEntitlement,
                carryOverDays: 0,
                createdBy: new mongoose.Types.ObjectId(userId)
              });

              await leaveBalance.save();
              leaveBalancesCreated++;

              // Process pro-rata accrual if employee was hired mid-year
              const hireDate = new Date(employee.hireDate);
              const yearStart = new Date(currentYear, 0, 1);
              
              if (hireDate > yearStart) {
                try {
                  await leaveAccrualService.processProRataAccrual(
                    employeeId,
                    hireDate,
                    userId
                  );
                  accrualRulesApplied++;
                } catch (accrualError) {
                  logger.error('Error processing pro-rata accrual', LogCategory.HR, accrualError);
                  errors.push(`Failed to process pro-rata accrual for ${leaveType.name}: ${accrualError instanceof Error ? accrualError.message : 'Unknown error'}`);
                }
              }
            }
          }
        } catch (error) {
          logger.error('Error setting up leave balance', LogCategory.HR, { leaveType: leaveType.name, error });
          errors.push(`Failed to set up ${leaveType.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      return {
        employeeId,
        leaveBalancesCreated,
        accrualRulesApplied,
        errors
      };
    } catch (error) {
      logger.error('Error handling employee onboarding', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Handle department transfer - update leave entitlements and workflows
   * @param employeeId - Employee ID
   * @param newDepartmentId - New department ID
   * @param userId - User ID performing the action
   * @returns Transfer result
   */
  async handleDepartmentTransfer(
    employeeId: string, 
    newDepartmentId: string, 
    userId: string
  ): Promise<DepartmentTransferResult> {
    try {
      await connectToDatabase();
      logger.info('Handling department transfer', LogCategory.HR, { employeeId, newDepartmentId });

      const employee = await Employee.findById(employeeId)
        .populate('departmentId', 'name');
      
      if (!employee) {
        throw new Error(`Employee with ID ${employeeId} not found`);
      }

      const oldDepartment = (employee.departmentId as any)?.name || 'Unknown';
      
      // Get new department
      const Department = (await import('@/models/Department')).default;
      const newDepartment = await Department.findById(newDepartmentId);
      
      if (!newDepartment) {
        throw new Error(`Department with ID ${newDepartmentId} not found`);
      }

      const errors: string[] = [];
      let leaveEntitlementsUpdated = false;
      let workflowsUpdated = false;

      // Update employee department
      const oldDepartmentId = employee.departmentId;
      employee.departmentId = new mongoose.Types.ObjectId(newDepartmentId);
      employee.updatedBy = new mongoose.Types.ObjectId(userId);
      await employee.save();

      // Check if leave entitlements need to be updated based on new department
      try {
        const entitlementUpdates = await this.updateLeaveEntitlementsForDepartmentChange(
          employeeId,
          oldDepartmentId?.toString(),
          newDepartmentId,
          userId
        );
        leaveEntitlementsUpdated = entitlementUpdates > 0;
      } catch (error) {
        logger.error('Error updating leave entitlements', LogCategory.HR, error);
        errors.push(`Failed to update leave entitlements: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      // Update approval workflows
      try {
        const workflowUpdates = await this.updateApprovalWorkflowsForDepartmentChange(
          employeeId,
          newDepartmentId,
          userId
        );
        workflowsUpdated = workflowUpdates > 0;
      } catch (error) {
        logger.error('Error updating approval workflows', LogCategory.HR, error);
        errors.push(`Failed to update approval workflows: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      return {
        employeeId,
        oldDepartment,
        newDepartment: newDepartment.name,
        leaveEntitlementsUpdated,
        workflowsUpdated,
        errors
      };
    } catch (error) {
      logger.error('Error handling department transfer', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Handle role change - update leave entitlements and access
   * @param employeeId - Employee ID
   * @param newRole - New role/position
   * @param userId - User ID performing the action
   * @returns Role change result
   */
  async handleRoleChange(
    employeeId: string, 
    newRole: string, 
    userId: string
  ): Promise<RoleChangeResult> {
    try {
      await connectToDatabase();
      logger.info('Handling role change', LogCategory.HR, { employeeId, newRole });

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        throw new Error(`Employee with ID ${employeeId} not found`);
      }

      const oldRole = employee.position;
      const errors: string[] = [];
      let leaveEntitlementsUpdated = false;
      let accessUpdated = false;

      // Update employee role
      employee.position = newRole;
      employee.updatedBy = new mongoose.Types.ObjectId(userId);
      await employee.save();

      // Check if leave entitlements need to be updated based on new role
      try {
        const entitlementUpdates = await this.updateLeaveEntitlementsForRoleChange(
          employeeId,
          oldRole,
          newRole,
          userId
        );
        leaveEntitlementsUpdated = entitlementUpdates > 0;
      } catch (error) {
        logger.error('Error updating leave entitlements for role change', LogCategory.HR, error);
        errors.push(`Failed to update leave entitlements: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      // Update system access based on new role
      try {
        const accessUpdates = await this.updateSystemAccessForRoleChange(
          employeeId,
          oldRole,
          newRole,
          userId
        );
        accessUpdated = accessUpdates > 0;
      } catch (error) {
        logger.error('Error updating system access', LogCategory.HR, error);
        errors.push(`Failed to update system access: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      return {
        employeeId,
        oldRole,
        newRole,
        leaveEntitlementsUpdated,
        accessUpdated,
        errors
      };
    } catch (error) {
      logger.error('Error handling role change', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Handle employee termination - process final leave calculations
   * @param employeeId - Employee ID
   * @param terminationDate - Termination date
   * @param userId - User ID performing the action
   * @returns Termination result
   */
  async handleEmployeeTermination(
    employeeId: string, 
    terminationDate: Date, 
    userId: string
  ): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Handling employee termination', LogCategory.HR, { employeeId, terminationDate });

      const employee = await Employee.findById(employeeId);
      if (!employee) {
        throw new Error(`Employee with ID ${employeeId} not found`);
      }

      const errors: string[] = [];
      const currentYear = terminationDate.getFullYear();

      // Get all leave balances for the current year
      const leaveBalances = await LeaveBalance.find({
        employeeId: employee._id,
        year: currentYear
      }).populate('leaveTypeId', 'name allowCarryOver');

      const finalCalculations = [];

      for (const balance of leaveBalances) {
        try {
          // Calculate final leave entitlement (pro-rata for partial year)
          const finalEntitlement = await this.calculateFinalLeaveEntitlement(
            employee,
            balance,
            terminationDate
          );

          finalCalculations.push({
            leaveType: (balance.leaveTypeId as any).name,
            originalBalance: balance.remainingDays,
            finalEntitlement: finalEntitlement,
            encashableAmount: Math.max(0, finalEntitlement)
          });

          // Update balance to reflect final calculation
          balance.remainingDays = finalEntitlement;
          balance.updatedBy = new mongoose.Types.ObjectId(userId);
          await balance.save();

        } catch (error) {
          logger.error('Error calculating final leave entitlement', LogCategory.HR, error);
          errors.push(`Failed to calculate final entitlement for ${(balance.leaveTypeId as any).name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Update employee status
      employee.employmentStatus = 'terminated';
      employee.terminationDate = terminationDate;
      employee.updatedBy = new mongoose.Types.ObjectId(userId);
      await employee.save();

      return {
        employeeId,
        terminationDate,
        finalCalculations,
        errors
      };
    } catch (error) {
      logger.error('Error handling employee termination', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Check if leave type is applicable to employee
   * @param employee - Employee document
   * @param leaveType - Leave type document
   * @returns True if applicable
   */
  private async isLeaveTypeApplicableToEmployee(employee: any, leaveType: any): Promise<boolean> {
    // Check department restrictions
    if (leaveType.applicableDepartments?.length > 0) {
      if (!leaveType.applicableDepartments.some((dept: any) =>
        dept.toString() === employee.departmentId?.toString()
      )) {
        return false;
      }
    }

    // Check role restrictions
    if (leaveType.applicableRoles?.length > 0) {
      if (!leaveType.applicableRoles.includes(employee.position)) {
        return false;
      }
    }

    // Check employment type restrictions
    if (leaveType.applicableEmploymentTypes?.length > 0) {
      if (!leaveType.applicableEmploymentTypes.includes(employee.employmentType)) {
        return false;
      }
    }

    // Check minimum tenure requirements
    if (leaveType.minTenureMonths > 0) {
      const tenureMonths = differenceInMonths(new Date(), new Date(employee.hireDate));
      if (tenureMonths < leaveType.minTenureMonths) {
        return false;
      }
    }

    return true;
  }

  /**
   * Calculate initial leave entitlement for employee
   * @param employee - Employee document
   * @param leaveType - Leave type document
   * @returns Initial entitlement in days
   */
  private async calculateInitialLeaveEntitlement(employee: any, leaveType: any): Promise<number> {
    // Use default days from leave type
    let entitlement = leaveType.defaultDays || 0;

    // Apply tenure-based adjustments if configured
    if (leaveType.tenureBasedEntitlement?.enabled) {
      const tenureMonths = differenceInMonths(new Date(), new Date(employee.hireDate));
      const tenureRule = leaveType.tenureBasedEntitlement.rules?.find((rule: any) =>
        tenureMonths >= rule.minMonths && (!rule.maxMonths || tenureMonths <= rule.maxMonths)
      );

      if (tenureRule) {
        entitlement = tenureRule.days;
      }
    }

    // Apply role-based adjustments if configured
    if (leaveType.roleBasedEntitlement?.enabled) {
      const roleRule = leaveType.roleBasedEntitlement.rules?.find((rule: any) =>
        rule.roles.includes(employee.position)
      );

      if (roleRule) {
        entitlement = roleRule.days;
      }
    }

    return entitlement;
  }

  /**
   * Update leave entitlements for department change
   * @param employeeId - Employee ID
   * @param oldDepartmentId - Old department ID
   * @param newDepartmentId - New department ID
   * @param userId - User ID performing the action
   * @returns Number of entitlements updated
   */
  private async updateLeaveEntitlementsForDepartmentChange(
    employeeId: string,
    oldDepartmentId: string | undefined,
    newDepartmentId: string,
    userId: string
  ): Promise<number> {
    let updatesCount = 0;

    // Get all leave types that have department-specific rules
    const leaveTypes = await LeaveType.find({
      isActive: true,
      $or: [
        { 'applicableDepartments.0': { $exists: true } },
        { 'departmentBasedEntitlement.enabled': true }
      ]
    });

    const employee = await Employee.findById(employeeId);
    if (!employee) return 0;

    const currentYear = new Date().getFullYear();

    for (const leaveType of leaveTypes) {
      // Check if employee is now eligible/ineligible for this leave type
      const isNowApplicable = await this.isLeaveTypeApplicableToEmployee(employee, leaveType);

      const existingBalance = await LeaveBalance.findOne({
        employeeId: employee._id,
        leaveTypeId: leaveType._id,
        year: currentYear
      });

      if (isNowApplicable && !existingBalance) {
        // Create new balance for newly applicable leave type
        const entitlement = await this.calculateInitialLeaveEntitlement(employee, leaveType);

        const newBalance = new LeaveBalance({
          employeeId: employee._id,
          leaveTypeId: leaveType._id,
          year: currentYear,
          totalDays: entitlement,
          usedDays: 0,
          pendingDays: 0,
          remainingDays: entitlement,
          carryOverDays: 0,
          createdBy: new mongoose.Types.ObjectId(userId)
        });

        await newBalance.save();
        updatesCount++;
      } else if (!isNowApplicable && existingBalance && existingBalance.usedDays === 0) {
        // Remove balance for no longer applicable leave type (only if unused)
        await LeaveBalance.findByIdAndDelete(existingBalance._id);
        updatesCount++;
      } else if (isNowApplicable && existingBalance) {
        // Update existing balance if department-specific entitlements changed
        const newEntitlement = await this.calculateInitialLeaveEntitlement(employee, leaveType);

        if (newEntitlement !== existingBalance.totalDays) {
          const difference = newEntitlement - existingBalance.totalDays;
          existingBalance.totalDays = newEntitlement;
          existingBalance.remainingDays += difference;
          existingBalance.updatedBy = new mongoose.Types.ObjectId(userId);
          await existingBalance.save();
          updatesCount++;
        }
      }
    }

    return updatesCount;
  }

  /**
   * Update leave entitlements for role change
   * @param employeeId - Employee ID
   * @param oldRole - Old role
   * @param newRole - New role
   * @param userId - User ID performing the action
   * @returns Number of entitlements updated
   */
  private async updateLeaveEntitlementsForRoleChange(
    employeeId: string,
    oldRole: string,
    newRole: string,
    userId: string
  ): Promise<number> {
    let updatesCount = 0;

    // Get all leave types that have role-specific rules
    const leaveTypes = await LeaveType.find({
      isActive: true,
      $or: [
        { 'applicableRoles.0': { $exists: true } },
        { 'roleBasedEntitlement.enabled': true }
      ]
    });

    const employee = await Employee.findById(employeeId);
    if (!employee) return 0;

    const currentYear = new Date().getFullYear();

    for (const leaveType of leaveTypes) {
      const existingBalance = await LeaveBalance.findOne({
        employeeId: employee._id,
        leaveTypeId: leaveType._id,
        year: currentYear
      });

      if (existingBalance) {
        // Recalculate entitlement based on new role
        const newEntitlement = await this.calculateInitialLeaveEntitlement(employee, leaveType);

        if (newEntitlement !== existingBalance.totalDays) {
          const difference = newEntitlement - existingBalance.totalDays;
          existingBalance.totalDays = newEntitlement;
          existingBalance.remainingDays += difference;
          existingBalance.updatedBy = new mongoose.Types.ObjectId(userId);
          await existingBalance.save();
          updatesCount++;
        }
      }
    }

    return updatesCount;
  }

  /**
   * Update approval workflows for department change
   * @param employeeId - Employee ID
   * @param newDepartmentId - New department ID
   * @param userId - User ID performing the action
   * @returns Number of workflows updated
   */
  private async updateApprovalWorkflowsForDepartmentChange(
    employeeId: string,
    newDepartmentId: string,
    userId: string
  ): Promise<number> {
    // This would update any pending approval workflows
    // For now, return 0 as this is a placeholder for future implementation
    return 0;
  }

  /**
   * Update system access for role change
   * @param employeeId - Employee ID
   * @param oldRole - Old role
   * @param newRole - New role
   * @param userId - User ID performing the action
   * @returns Number of access updates
   */
  private async updateSystemAccessForRoleChange(
    employeeId: string,
    oldRole: string,
    newRole: string,
    userId: string
  ): Promise<number> {
    // This would update user roles and permissions
    // For now, return 0 as this is a placeholder for future implementation
    return 0;
  }

  /**
   * Calculate final leave entitlement for termination
   * @param employee - Employee document
   * @param balance - Leave balance document
   * @param terminationDate - Termination date
   * @returns Final entitlement in days
   */
  private async calculateFinalLeaveEntitlement(
    employee: any,
    balance: any,
    terminationDate: Date
  ): Promise<number> {
    const yearStart = new Date(terminationDate.getFullYear(), 0, 1);
    const monthsWorked = differenceInMonths(terminationDate, yearStart) + 1;

    // Calculate pro-rata entitlement for the year
    const proRataEntitlement = (balance.totalDays * monthsWorked) / 12;

    // Return remaining days or pro-rata entitlement, whichever is lower
    return Math.min(balance.remainingDays, Math.floor(proRataEntitlement));
  }
}

// Export singleton instance
export const employeeLifecycleService = new EmployeeLifecycleService();
