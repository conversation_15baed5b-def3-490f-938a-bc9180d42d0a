import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import Notification from '@/models/notifications/Notification';
import NotificationTemplate from '@/models/notifications/NotificationTemplate';
import User from '@/models/User';
import Employee from '@/models/Employee';
import { addMinutes, addHours } from 'date-fns';

export interface NotificationPayload {
  recipientId: string;
  senderId?: string;
  type: 'leave-request' | 'leave-approval' | 'leave-rejection' | 'leave-reminder' | 'encashment' | 'system' | 'escalation';
  relatedEntityType?: 'leave' | 'encashment' | 'employee' | 'workflow';
  relatedEntityId?: string;
  variables?: Record<string, any>;
  channels?: {
    inApp?: boolean;
    email?: boolean;
    sms?: boolean;
  };
  scheduledFor?: Date;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
}

export interface BulkNotificationPayload {
  recipientIds: string[];
  senderId?: string;
  type: 'leave-request' | 'leave-approval' | 'leave-rejection' | 'leave-reminder' | 'encashment' | 'system' | 'escalation';
  variables?: Record<string, any>;
  channels?: {
    inApp?: boolean;
    email?: boolean;
    sms?: boolean;
  };
  scheduledFor?: Date;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
}

class NotificationService {
  /**
   * Send a notification to a single recipient
   * @param payload - Notification payload
   * @returns Created notification
   */
  async sendNotification(payload: NotificationPayload): Promise<any> {
    try {
      await connectToDatabase();
      logger.info('Sending notification', LogCategory.NOTIFICATION, { payload });

      // Get recipient details
      const recipient = await User.findById(payload.recipientId);
      if (!recipient) {
        throw new Error(`Recipient with ID ${payload.recipientId} not found`);
      }

      // Find appropriate template
      const template = await this.findTemplate(payload.type);
      if (!template) {
        throw new Error(`No template found for notification type: ${payload.type}`);
      }

      // Process template with variables
      const processedContent = await this.processTemplate(template, payload.variables || {});

      // Generate notification ID
      const notificationId = await this.generateNotificationId();

      // Determine delivery channels
      const channels = this.determineDeliveryChannels(template, payload.channels);

      // Create notification
      const notification = new Notification({
        notificationId,
        recipientId: new mongoose.Types.ObjectId(payload.recipientId),
        senderId: payload.senderId ? new mongoose.Types.ObjectId(payload.senderId) : undefined,
        type: payload.type,
        category: template.category,
        title: processedContent.title,
        message: processedContent.message,
        relatedEntityType: payload.relatedEntityType,
        relatedEntityId: payload.relatedEntityId ? new mongoose.Types.ObjectId(payload.relatedEntityId) : undefined,
        channels: {
          inApp: {
            enabled: channels.inApp,
            delivered: channels.inApp,
            deliveredAt: channels.inApp ? new Date() : undefined,
            read: false
          },
          email: {
            enabled: channels.email,
            delivered: false,
            emailAddress: recipient.email,
            emailSubject: processedContent.emailSubject,
            emailTemplate: processedContent.emailTemplate,
            deliveryAttempts: 0
          },
          sms: {
            enabled: channels.sms,
            delivered: false,
            phoneNumber: recipient.phone,
            deliveryAttempts: 0
          }
        },
        scheduledFor: payload.scheduledFor,
        isScheduled: !!payload.scheduledFor,
        priority: payload.priority || template.deliverySettings.priority,
        expiresAt: template.deliverySettings.expiryHours ? 
          addHours(new Date(), template.deliverySettings.expiryHours) : undefined,
        actions: processedContent.actions,
        metadata: payload.variables
      });

      await notification.save();

      // Send immediate notifications
      if (!payload.scheduledFor) {
        await this.deliverNotification(notification);
      }

      return notification;
    } catch (error) {
      logger.error('Error sending notification', LogCategory.NOTIFICATION, error);
      throw error;
    }
  }

  /**
   * Send bulk notifications to multiple recipients
   * @param payload - Bulk notification payload
   * @returns Array of created notifications
   */
  async sendBulkNotifications(payload: BulkNotificationPayload): Promise<any[]> {
    try {
      await connectToDatabase();
      logger.info('Sending bulk notifications', LogCategory.NOTIFICATION, { 
        recipientCount: payload.recipientIds.length,
        type: payload.type 
      });

      const notifications = [];

      for (const recipientId of payload.recipientIds) {
        try {
          const notification = await this.sendNotification({
            recipientId,
            senderId: payload.senderId,
            type: payload.type,
            variables: payload.variables,
            channels: payload.channels,
            scheduledFor: payload.scheduledFor,
            priority: payload.priority
          });
          notifications.push(notification);
        } catch (error) {
          logger.error('Error sending notification to recipient', LogCategory.NOTIFICATION, { 
            recipientId, 
            error 
          });
        }
      }

      return notifications;
    } catch (error) {
      logger.error('Error sending bulk notifications', LogCategory.NOTIFICATION, error);
      throw error;
    }
  }

  /**
   * Send leave request notification
   * @param leaveRequest - Leave request document
   * @param approvers - Array of approver user IDs
   * @returns Array of notifications sent
   */
  async sendLeaveRequestNotification(leaveRequest: any, approvers: string[]): Promise<any[]> {
    const variables = {
      employeeName: `${leaveRequest.employeeId.firstName} ${leaveRequest.employeeId.lastName}`,
      leaveType: leaveRequest.leaveTypeId.name,
      startDate: leaveRequest.startDate.toLocaleDateString(),
      endDate: leaveRequest.endDate.toLocaleDateString(),
      duration: leaveRequest.duration,
      reason: leaveRequest.reason || 'No reason provided'
    };

    return await this.sendBulkNotifications({
      recipientIds: approvers,
      senderId: leaveRequest.employeeId._id.toString(),
      type: 'leave-request',
      relatedEntityType: 'leave',
      relatedEntityId: leaveRequest._id.toString(),
      variables,
      channels: { inApp: true, email: true },
      priority: 'normal'
    });
  }

  /**
   * Send leave approval notification
   * @param leaveRequest - Leave request document
   * @param approverId - Approver user ID
   * @returns Notification sent
   */
  async sendLeaveApprovalNotification(leaveRequest: any, approverId: string): Promise<any> {
    const variables = {
      leaveType: leaveRequest.leaveTypeId.name,
      startDate: leaveRequest.startDate.toLocaleDateString(),
      endDate: leaveRequest.endDate.toLocaleDateString(),
      duration: leaveRequest.duration,
      approverName: 'HR Manager' // This would be fetched from the approver's details
    };

    return await this.sendNotification({
      recipientId: leaveRequest.employeeId._id.toString(),
      senderId: approverId,
      type: 'leave-approval',
      relatedEntityType: 'leave',
      relatedEntityId: leaveRequest._id.toString(),
      variables,
      channels: { inApp: true, email: true },
      priority: 'normal'
    });
  }

  /**
   * Send leave rejection notification
   * @param leaveRequest - Leave request document
   * @param approverId - Approver user ID
   * @param rejectionReason - Reason for rejection
   * @returns Notification sent
   */
  async sendLeaveRejectionNotification(
    leaveRequest: any, 
    approverId: string, 
    rejectionReason: string
  ): Promise<any> {
    const variables = {
      leaveType: leaveRequest.leaveTypeId.name,
      startDate: leaveRequest.startDate.toLocaleDateString(),
      endDate: leaveRequest.endDate.toLocaleDateString(),
      duration: leaveRequest.duration,
      rejectionReason,
      approverName: 'HR Manager' // This would be fetched from the approver's details
    };

    return await this.sendNotification({
      recipientId: leaveRequest.employeeId._id.toString(),
      senderId: approverId,
      type: 'leave-rejection',
      relatedEntityType: 'leave',
      relatedEntityId: leaveRequest._id.toString(),
      variables,
      channels: { inApp: true, email: true },
      priority: 'high'
    });
  }

  /**
   * Send leave reminder notifications
   * @param reminderType - Type of reminder
   * @returns Number of reminders sent
   */
  async sendLeaveReminders(reminderType: 'pending-approval' | 'upcoming-leave' | 'overdue-return'): Promise<number> {
    try {
      await connectToDatabase();
      logger.info('Sending leave reminders', LogCategory.NOTIFICATION, { reminderType });

      let remindersSent = 0;

      switch (reminderType) {
        case 'pending-approval':
          remindersSent = await this.sendPendingApprovalReminders();
          break;
        case 'upcoming-leave':
          remindersSent = await this.sendUpcomingLeaveReminders();
          break;
        case 'overdue-return':
          remindersSent = await this.sendOverdueReturnReminders();
          break;
      }

      return remindersSent;
    } catch (error) {
      logger.error('Error sending leave reminders', LogCategory.NOTIFICATION, error);
      throw error;
    }
  }

  /**
   * Mark notification as read
   * @param notificationId - Notification ID
   * @param userId - User ID marking as read
   * @returns Updated notification
   */
  async markAsRead(notificationId: string, userId: string): Promise<any> {
    try {
      await connectToDatabase();
      
      const notification = await Notification.findOneAndUpdate(
        {
          _id: notificationId,
          recipientId: new mongoose.Types.ObjectId(userId)
        },
        {
          'channels.inApp.read': true,
          'channels.inApp.readAt': new Date()
        },
        { new: true }
      );

      return notification;
    } catch (error) {
      logger.error('Error marking notification as read', LogCategory.NOTIFICATION, error);
      throw error;
    }
  }

  /**
   * Get notifications for a user
   * @param userId - User ID
   * @param options - Query options
   * @returns User notifications
   */
  async getUserNotifications(
    userId: string, 
    options: {
      page?: number;
      limit?: number;
      unreadOnly?: boolean;
      type?: string;
    } = {}
  ): Promise<{ notifications: any[]; total: number; unreadCount: number }> {
    try {
      await connectToDatabase();

      const { page = 1, limit = 20, unreadOnly = false, type } = options;
      const skip = (page - 1) * limit;

      // Build query
      const query: any = {
        recipientId: new mongoose.Types.ObjectId(userId),
        $or: [
          { expiresAt: { $exists: false } },
          { expiresAt: { $gte: new Date() } }
        ]
      };

      if (unreadOnly) {
        query['channels.inApp.read'] = false;
      }

      if (type) {
        query.type = type;
      }

      // Get notifications
      const notifications = await Notification.find(query)
        .populate('senderId', 'firstName lastName')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      // Get total count
      const total = await Notification.countDocuments(query);

      // Get unread count
      const unreadCount = await Notification.countDocuments({
        recipientId: new mongoose.Types.ObjectId(userId),
        'channels.inApp.read': false,
        $or: [
          { expiresAt: { $exists: false } },
          { expiresAt: { $gte: new Date() } }
        ]
      });

      return { notifications, total, unreadCount };
    } catch (error) {
      logger.error('Error getting user notifications', LogCategory.NOTIFICATION, error);
      throw error;
    }
  }

  /**
   * Find notification template by type
   * @param type - Notification type
   * @returns Template or null
   */
  private async findTemplate(type: string): Promise<any> {
    // Try to find specific template first
    let template = await NotificationTemplate.findOne({ type, isActive: true });

    // Fall back to default template
    if (!template) {
      template = await NotificationTemplate.findOne({ type, isDefault: true, isActive: true });
    }

    return template;
  }

  /**
   * Process template with variables
   * @param template - Notification template
   * @param variables - Variables to substitute
   * @returns Processed content
   */
  private async processTemplate(template: any, variables: Record<string, any>): Promise<any> {
    const processText = (text: string): string => {
      let processed = text;

      // Replace variables in format {{variableName}}
      Object.entries(variables).forEach(([key, value]) => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        processed = processed.replace(regex, String(value));
      });

      return processed;
    };

    return {
      title: processText(template.templates.inApp.title),
      message: processText(template.templates.inApp.message),
      emailSubject: template.templates.email.enabled ?
        processText(template.templates.email.subject) : undefined,
      emailTemplate: template.templates.email.enabled ?
        processText(template.templates.email.htmlTemplate) : undefined,
      actions: template.templates.inApp.actions?.map((action: any) => ({
        ...action,
        label: processText(action.label),
        url: action.url ? processText(action.url) : undefined
      }))
    };
  }

  /**
   * Determine delivery channels based on template and preferences
   * @param template - Notification template
   * @param preferences - User channel preferences
   * @returns Enabled channels
   */
  private determineDeliveryChannels(
    template: any,
    preferences?: { inApp?: boolean; email?: boolean; sms?: boolean }
  ): { inApp: boolean; email: boolean; sms: boolean } {
    return {
      inApp: preferences?.inApp ?? template.templates.inApp.enabled,
      email: preferences?.email ?? template.templates.email.enabled,
      sms: preferences?.sms ?? template.templates.sms.enabled
    };
  }

  /**
   * Deliver notification through enabled channels
   * @param notification - Notification document
   */
  private async deliverNotification(notification: any): Promise<void> {
    try {
      // Email delivery
      if (notification.channels.email.enabled && !notification.channels.email.delivered) {
        await this.deliverEmail(notification);
      }

      // SMS delivery
      if (notification.channels.sms.enabled && !notification.channels.sms.delivered) {
        await this.deliverSMS(notification);
      }
    } catch (error) {
      logger.error('Error delivering notification', LogCategory.NOTIFICATION, error);
    }
  }

  /**
   * Deliver email notification
   * @param notification - Notification document
   */
  private async deliverEmail(notification: any): Promise<void> {
    try {
      // This would integrate with your email service (SendGrid, AWS SES, etc.)
      // For now, we'll just mark as delivered
      notification.channels.email.delivered = true;
      notification.channels.email.deliveredAt = new Date();
      notification.channels.email.deliveryAttempts += 1;

      await notification.save();

      logger.info('Email notification delivered', LogCategory.NOTIFICATION, {
        notificationId: notification.notificationId,
        recipient: notification.channels.email.emailAddress
      });
    } catch (error) {
      notification.channels.email.deliveryAttempts += 1;
      notification.channels.email.lastAttemptAt = new Date();
      notification.channels.email.errorMessage = error instanceof Error ? error.message : 'Unknown error';

      await notification.save();

      logger.error('Error delivering email notification', LogCategory.NOTIFICATION, error);
    }
  }

  /**
   * Deliver SMS notification
   * @param notification - Notification document
   */
  private async deliverSMS(notification: any): Promise<void> {
    try {
      // This would integrate with your SMS service (Twilio, AWS SNS, etc.)
      // For now, we'll just mark as delivered
      notification.channels.sms.delivered = true;
      notification.channels.sms.deliveredAt = new Date();
      notification.channels.sms.deliveryAttempts += 1;

      await notification.save();

      logger.info('SMS notification delivered', LogCategory.NOTIFICATION, {
        notificationId: notification.notificationId,
        recipient: notification.channels.sms.phoneNumber
      });
    } catch (error) {
      notification.channels.sms.deliveryAttempts += 1;
      notification.channels.sms.lastAttemptAt = new Date();
      notification.channels.sms.errorMessage = error instanceof Error ? error.message : 'Unknown error';

      await notification.save();

      logger.error('Error delivering SMS notification', LogCategory.NOTIFICATION, error);
    }
  }

  /**
   * Send pending approval reminders
   * @returns Number of reminders sent
   */
  private async sendPendingApprovalReminders(): Promise<number> {
    // This would find pending leave requests and send reminders to approvers
    // Implementation would depend on your specific business rules
    return 0;
  }

  /**
   * Send upcoming leave reminders
   * @returns Number of reminders sent
   */
  private async sendUpcomingLeaveReminders(): Promise<number> {
    // This would find upcoming leave and send reminders to employees
    // Implementation would depend on your specific business rules
    return 0;
  }

  /**
   * Send overdue return reminders
   * @returns Number of reminders sent
   */
  private async sendOverdueReturnReminders(): Promise<number> {
    // This would find overdue returns and send reminders
    // Implementation would depend on your specific business rules
    return 0;
  }

  /**
   * Generate unique notification ID
   * @returns Notification ID
   */
  private async generateNotificationId(): Promise<string> {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
    return `NOTIF-${timestamp}-${random}`;
  }

  /**
   * Process scheduled notifications
   * @returns Number of notifications processed
   */
  async processScheduledNotifications(): Promise<number> {
    try {
      await connectToDatabase();

      const scheduledNotifications = await Notification.find({
        isScheduled: true,
        scheduledFor: { $lte: new Date() },
        'channels.inApp.delivered': false
      });

      let processedCount = 0;

      for (const notification of scheduledNotifications) {
        try {
          await this.deliverNotification(notification);

          notification.isScheduled = false;
          notification.channels.inApp.delivered = true;
          notification.channels.inApp.deliveredAt = new Date();

          await notification.save();
          processedCount++;
        } catch (error) {
          logger.error('Error processing scheduled notification', LogCategory.NOTIFICATION, {
            notificationId: notification.notificationId,
            error
          });
        }
      }

      return processedCount;
    } catch (error) {
      logger.error('Error processing scheduled notifications', LogCategory.NOTIFICATION, error);
      throw error;
    }
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
