import { ObjectId } from 'mongoose';

// Base leave request interface for frontend
export interface LeaveRequest {
  id: string
  employee: {
    id: string
    name: string
    position: string
    avatar: string
  }
  type: string
  startDate: string
  endDate: string
  days: number
  reason: string
  status: "pending" | "approved" | "rejected" | "cancelled"
  requestDate: string
  reviewDate?: string
  reviewedBy?: {
    id: string
    name: string
    avatar: string
  }
  comments?: string
}

// Database leave request interface
export interface ILeaveRequestDB {
  _id: ObjectId
  leaveId: string
  employeeId: ObjectId
  leaveTypeId: ObjectId
  startDate: Date
  endDate: Date
  duration: number
  reason: string
  status: 'pending' | 'approved' | 'rejected' | 'cancelled'
  approvedBy?: ObjectId
  approvalDate?: Date
  rejectionReason?: string
  attachments?: string[]
  notes?: string
  createdBy: ObjectId
  updatedBy?: ObjectId
  createdAt: Date
  updatedAt: Date
}

// Leave request creation payload
export interface CreateLeaveRequestPayload {
  leaveTypeId: string
  startDate: string
  endDate: string
  reason: string
  notes?: string
  attachments?: string[]
}

// Leave request update payload
export interface UpdateLeaveRequestPayload {
  leaveTypeId?: string
  startDate?: string
  endDate?: string
  reason?: string
  notes?: string
  attachments?: string[]
}

// Leave request approval/rejection payload
export interface LeaveRequestActionPayload {
  reason?: string // Required for rejection
}

// Leave type interface
export interface ILeaveType {
  _id: ObjectId
  name: string
  code: string
  description?: string
  defaultDays: number
  isActive: boolean
  isPaid: boolean
  requiresApproval: boolean
  maxConsecutiveDays: number
  minNoticeInDays: number
  allowCarryOver: boolean
  maxCarryOverDays: number
  color: string
  applicableRoles?: string[]
  applicableDepartments?: ObjectId[]
  createdBy: ObjectId
  updatedBy?: ObjectId
  createdAt: Date
  updatedAt: Date
}

// Leave balance interface
export interface ILeaveBalance {
  _id: ObjectId
  employeeId: ObjectId
  leaveTypeId: ObjectId
  year: number
  totalDays: number
  usedDays: number
  pendingDays: number
  remainingDays: number
  carryOverDays: number
  expiryDate?: Date
  notes?: string
  createdBy: ObjectId
  updatedBy?: ObjectId
  createdAt: Date
  updatedAt: Date
}

// API response interfaces
export interface LeaveRequestResponse {
  success: boolean
  message: string
  data: ILeaveRequestDB
}

export interface LeaveRequestListResponse {
  data: ILeaveRequestDB[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface LeaveBalanceResponse {
  leaveType: {
    _id: string
    name: string
    code: string
    color: string
  }
  totalDays: number
  usedDays: number
  pendingDays: number
  remainingDays: number
  carryOverDays: number
}

// Query parameters for leave requests
export interface LeaveRequestQueryParams {
  page?: number
  limit?: number
  status?: string
  year?: number
  employeeId?: string
  leaveTypeId?: string
  startDate?: string
  endDate?: string
}
