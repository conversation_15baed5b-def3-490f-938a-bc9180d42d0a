import mongoose, { Schema, Document } from 'mongoose';

/**
 * Interface for notification document
 */
export interface INotification extends Document {
  notificationId: string;
  recipientId: mongoose.Types.ObjectId;
  senderId?: mongoose.Types.ObjectId;
  type: 'leave-request' | 'leave-approval' | 'leave-rejection' | 'leave-reminder' | 'encashment' | 'system' | 'escalation';
  category: 'info' | 'warning' | 'success' | 'error' | 'urgent';
  title: string;
  message: string;
  
  // Related entities
  relatedEntityType?: 'leave' | 'encashment' | 'employee' | 'workflow';
  relatedEntityId?: mongoose.Types.ObjectId;
  
  // Delivery channels
  channels: {
    inApp: {
      enabled: boolean;
      delivered: boolean;
      deliveredAt?: Date;
      read: boolean;
      readAt?: Date;
    };
    email: {
      enabled: boolean;
      delivered: boolean;
      deliveredAt?: Date;
      emailAddress?: string;
      emailSubject?: string;
      emailTemplate?: string;
      deliveryAttempts: number;
      lastAttemptAt?: Date;
      errorMessage?: string;
    };
    sms: {
      enabled: boolean;
      delivered: boolean;
      deliveredAt?: Date;
      phoneNumber?: string;
      deliveryAttempts: number;
      lastAttemptAt?: Date;
      errorMessage?: string;
    };
  };
  
  // Scheduling
  scheduledFor?: Date;
  isScheduled: boolean;
  isRecurring: boolean;
  recurringPattern?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    interval: number;
    endDate?: Date;
  };
  
  // Priority and urgency
  priority: 'low' | 'normal' | 'high' | 'urgent';
  expiresAt?: Date;
  
  // Action buttons for in-app notifications
  actions?: {
    label: string;
    action: string;
    url?: string;
    style: 'primary' | 'secondary' | 'danger';
  }[];
  
  // Metadata
  metadata?: Record<string, any>;
  
  createdBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Schema for notification
 */
const NotificationSchema: Schema = new Schema(
  {
    notificationId: {
      type: String,
      required: true,
      unique: true,
    },
    recipientId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    senderId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    type: {
      type: String,
      enum: ['leave-request', 'leave-approval', 'leave-rejection', 'leave-reminder', 'encashment', 'system', 'escalation'],
      required: true,
    },
    category: {
      type: String,
      enum: ['info', 'warning', 'success', 'error', 'urgent'],
      default: 'info',
    },
    title: {
      type: String,
      required: true,
      trim: true,
    },
    message: {
      type: String,
      required: true,
      trim: true,
    },
    relatedEntityType: {
      type: String,
      enum: ['leave', 'encashment', 'employee', 'workflow'],
    },
    relatedEntityId: {
      type: mongoose.Schema.Types.ObjectId,
    },
    channels: {
      inApp: {
        enabled: {
          type: Boolean,
          default: true,
        },
        delivered: {
          type: Boolean,
          default: false,
        },
        deliveredAt: {
          type: Date,
        },
        read: {
          type: Boolean,
          default: false,
        },
        readAt: {
          type: Date,
        },
      },
      email: {
        enabled: {
          type: Boolean,
          default: false,
        },
        delivered: {
          type: Boolean,
          default: false,
        },
        deliveredAt: {
          type: Date,
        },
        emailAddress: {
          type: String,
          trim: true,
        },
        emailSubject: {
          type: String,
          trim: true,
        },
        emailTemplate: {
          type: String,
          trim: true,
        },
        deliveryAttempts: {
          type: Number,
          default: 0,
        },
        lastAttemptAt: {
          type: Date,
        },
        errorMessage: {
          type: String,
          trim: true,
        },
      },
      sms: {
        enabled: {
          type: Boolean,
          default: false,
        },
        delivered: {
          type: Boolean,
          default: false,
        },
        deliveredAt: {
          type: Date,
        },
        phoneNumber: {
          type: String,
          trim: true,
        },
        deliveryAttempts: {
          type: Number,
          default: 0,
        },
        lastAttemptAt: {
          type: Date,
        },
        errorMessage: {
          type: String,
          trim: true,
        },
      },
    },
    scheduledFor: {
      type: Date,
    },
    isScheduled: {
      type: Boolean,
      default: false,
    },
    isRecurring: {
      type: Boolean,
      default: false,
    },
    recurringPattern: {
      frequency: {
        type: String,
        enum: ['daily', 'weekly', 'monthly'],
      },
      interval: {
        type: Number,
        min: 1,
      },
      endDate: {
        type: Date,
      },
    },
    priority: {
      type: String,
      enum: ['low', 'normal', 'high', 'urgent'],
      default: 'normal',
    },
    expiresAt: {
      type: Date,
    },
    actions: [{
      label: {
        type: String,
        required: true,
        trim: true,
      },
      action: {
        type: String,
        required: true,
        trim: true,
      },
      url: {
        type: String,
        trim: true,
      },
      style: {
        type: String,
        enum: ['primary', 'secondary', 'danger'],
        default: 'primary',
      },
    }],
    metadata: {
      type: mongoose.Schema.Types.Mixed,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes for faster queries
NotificationSchema.index({ recipientId: 1, createdAt: -1 });
NotificationSchema.index({ type: 1 });
NotificationSchema.index({ category: 1 });
NotificationSchema.index({ priority: 1 });
NotificationSchema.index({ 'channels.inApp.read': 1 });
NotificationSchema.index({ scheduledFor: 1 });
NotificationSchema.index({ expiresAt: 1 });
NotificationSchema.index({ relatedEntityType: 1, relatedEntityId: 1 });

// Create and export the model
const Notification = mongoose.models.Notification || mongoose.model<INotification>('Notification', NotificationSchema);

export default Notification;
