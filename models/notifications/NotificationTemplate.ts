import mongoose, { Schema, Document } from 'mongoose';

/**
 * Interface for notification template document
 */
export interface INotificationTemplate extends Document {
  templateId: string;
  name: string;
  description?: string;
  type: 'leave-request' | 'leave-approval' | 'leave-rejection' | 'leave-reminder' | 'encashment' | 'system' | 'escalation';
  category: 'info' | 'warning' | 'success' | 'error' | 'urgent';
  isActive: boolean;
  isDefault: boolean;
  
  // Template content
  templates: {
    inApp: {
      enabled: boolean;
      title: string;
      message: string;
      actions?: {
        label: string;
        action: string;
        url?: string;
        style: 'primary' | 'secondary' | 'danger';
      }[];
    };
    email: {
      enabled: boolean;
      subject: string;
      htmlTemplate: string;
      textTemplate?: string;
      fromName?: string;
      fromEmail?: string;
    };
    sms: {
      enabled: boolean;
      message: string;
    };
  };
  
  // Variables that can be used in templates
  variables: {
    name: string;
    description: string;
    type: 'string' | 'number' | 'date' | 'boolean';
    required: boolean;
    defaultValue?: string;
  }[];
  
  // Trigger conditions
  triggerConditions?: {
    entityType: 'leave' | 'encashment' | 'employee' | 'workflow';
    events: string[];
    filters?: Record<string, any>;
  };
  
  // Delivery settings
  deliverySettings: {
    priority: 'low' | 'normal' | 'high' | 'urgent';
    retryAttempts: number;
    retryInterval: number; // minutes
    expiryHours?: number;
  };
  
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Schema for notification template
 */
const NotificationTemplateSchema: Schema = new Schema(
  {
    templateId: {
      type: String,
      required: true,
      unique: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    type: {
      type: String,
      enum: ['leave-request', 'leave-approval', 'leave-rejection', 'leave-reminder', 'encashment', 'system', 'escalation'],
      required: true,
    },
    category: {
      type: String,
      enum: ['info', 'warning', 'success', 'error', 'urgent'],
      default: 'info',
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
    templates: {
      inApp: {
        enabled: {
          type: Boolean,
          default: true,
        },
        title: {
          type: String,
          required: true,
          trim: true,
        },
        message: {
          type: String,
          required: true,
          trim: true,
        },
        actions: [{
          label: {
            type: String,
            required: true,
            trim: true,
          },
          action: {
            type: String,
            required: true,
            trim: true,
          },
          url: {
            type: String,
            trim: true,
          },
          style: {
            type: String,
            enum: ['primary', 'secondary', 'danger'],
            default: 'primary',
          },
        }],
      },
      email: {
        enabled: {
          type: Boolean,
          default: false,
        },
        subject: {
          type: String,
          trim: true,
        },
        htmlTemplate: {
          type: String,
        },
        textTemplate: {
          type: String,
        },
        fromName: {
          type: String,
          trim: true,
        },
        fromEmail: {
          type: String,
          trim: true,
        },
      },
      sms: {
        enabled: {
          type: Boolean,
          default: false,
        },
        message: {
          type: String,
          trim: true,
        },
      },
    },
    variables: [{
      name: {
        type: String,
        required: true,
        trim: true,
      },
      description: {
        type: String,
        required: true,
        trim: true,
      },
      type: {
        type: String,
        enum: ['string', 'number', 'date', 'boolean'],
        required: true,
      },
      required: {
        type: Boolean,
        default: false,
      },
      defaultValue: {
        type: String,
        trim: true,
      },
    }],
    triggerConditions: {
      entityType: {
        type: String,
        enum: ['leave', 'encashment', 'employee', 'workflow'],
      },
      events: [{
        type: String,
        trim: true,
      }],
      filters: {
        type: mongoose.Schema.Types.Mixed,
      },
    },
    deliverySettings: {
      priority: {
        type: String,
        enum: ['low', 'normal', 'high', 'urgent'],
        default: 'normal',
      },
      retryAttempts: {
        type: Number,
        default: 3,
        min: 0,
        max: 10,
      },
      retryInterval: {
        type: Number,
        default: 5,
        min: 1,
      },
      expiryHours: {
        type: Number,
        min: 1,
      },
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes for faster queries
NotificationTemplateSchema.index({ type: 1 });
NotificationTemplateSchema.index({ isActive: 1 });
NotificationTemplateSchema.index({ isDefault: 1 });
NotificationTemplateSchema.index({ 'triggerConditions.entityType': 1 });

// Create and export the model
const NotificationTemplate = mongoose.models.NotificationTemplate || mongoose.model<INotificationTemplate>('NotificationTemplate', NotificationTemplateSchema);

export default NotificationTemplate;
