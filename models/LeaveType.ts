import mongoose, { Schema, Document, Model } from 'mongoose';

export interface ILeaveType extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  code: string;
  description?: string;
  color: string;
  isPaid: boolean;
  isActive: boolean;
  
  // Entitlement Rules
  defaultDaysPerYear: number;
  maxDaysPerYear?: number;
  minDaysPerRequest?: number;
  maxDaysPerRequest?: number;
  maxConsecutiveDays?: number;
  
  // Accrual Rules
  accrualMethod: 'monthly' | 'quarterly' | 'annually' | 'none';
  accrualRate?: number; // Days per period
  proRataForNewEmployees: boolean;
  
  // Carry Over Rules
  allowCarryOver: boolean;
  maxCarryOverDays?: number;
  carryOverExpiryMonths?: number;
  
  // Notice and Approval Rules
  minNoticeInDays: number;
  requiresApproval: boolean;
  approvalLevels: number;
  
  // Restrictions
  restrictToGender?: 'male' | 'female';
  minTenureInMonths?: number;
  maxTenureInMonths?: number;
  applicableToRoles?: string[];
  applicableToDepartments?: string[];
  
  // Encashment Rules
  allowEncashment: boolean;
  encashmentRate?: number; // Percentage of salary
  minEncashmentDays?: number;
  maxEncashmentDays?: number;
  
  // Calendar Rules
  excludeWeekends: boolean;
  excludeHolidays: boolean;
  blackoutPeriods?: Array<{
    startDate: Date;
    endDate: Date;
    description: string;
  }>;
  
  // System Fields
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  isDeleted: boolean;
}

const LeaveTypeSchema = new Schema<ILeaveType>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  code: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
    trim: true,
    maxlength: 10
  },
  description: {
    type: String,
    trim: true,
    maxlength: 500
  },
  color: {
    type: String,
    required: true,
    match: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
    default: '#3B82F6'
  },
  isPaid: {
    type: Boolean,
    required: true,
    default: true
  },
  isActive: {
    type: Boolean,
    required: true,
    default: true
  },
  
  // Entitlement Rules
  defaultDaysPerYear: {
    type: Number,
    required: true,
    min: 0,
    max: 365
  },
  maxDaysPerYear: {
    type: Number,
    min: 0,
    max: 365
  },
  minDaysPerRequest: {
    type: Number,
    min: 0.5,
    default: 0.5
  },
  maxDaysPerRequest: {
    type: Number,
    min: 1
  },
  maxConsecutiveDays: {
    type: Number,
    min: 1
  },
  
  // Accrual Rules
  accrualMethod: {
    type: String,
    enum: ['monthly', 'quarterly', 'annually', 'none'],
    required: true,
    default: 'annually'
  },
  accrualRate: {
    type: Number,
    min: 0
  },
  proRataForNewEmployees: {
    type: Boolean,
    default: true
  },
  
  // Carry Over Rules
  allowCarryOver: {
    type: Boolean,
    default: false
  },
  maxCarryOverDays: {
    type: Number,
    min: 0
  },
  carryOverExpiryMonths: {
    type: Number,
    min: 1,
    max: 12
  },
  
  // Notice and Approval Rules
  minNoticeInDays: {
    type: Number,
    required: true,
    min: 0,
    default: 1
  },
  requiresApproval: {
    type: Boolean,
    required: true,
    default: true
  },
  approvalLevels: {
    type: Number,
    required: true,
    min: 1,
    max: 5,
    default: 1
  },
  
  // Restrictions
  restrictToGender: {
    type: String,
    enum: ['male', 'female']
  },
  minTenureInMonths: {
    type: Number,
    min: 0
  },
  maxTenureInMonths: {
    type: Number,
    min: 0
  },
  applicableToRoles: [{
    type: String,
    trim: true
  }],
  applicableToDepartments: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Department'
  }],
  
  // Encashment Rules
  allowEncashment: {
    type: Boolean,
    default: false
  },
  encashmentRate: {
    type: Number,
    min: 0,
    max: 100
  },
  minEncashmentDays: {
    type: Number,
    min: 1
  },
  maxEncashmentDays: {
    type: Number,
    min: 1
  },
  
  // Calendar Rules
  excludeWeekends: {
    type: Boolean,
    default: true
  },
  excludeHolidays: {
    type: Boolean,
    default: true
  },
  blackoutPeriods: [{
    startDate: {
      type: Date,
      required: true
    },
    endDate: {
      type: Date,
      required: true
    },
    description: {
      type: String,
      required: true,
      trim: true
    }
  }],
  
  // System Fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  deletedAt: {
    type: Date
  },
  isDeleted: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
LeaveTypeSchema.index({ code: 1 }, { unique: true });
LeaveTypeSchema.index({ name: 1 });
LeaveTypeSchema.index({ isActive: 1 });
LeaveTypeSchema.index({ isDeleted: 1 });
LeaveTypeSchema.index({ createdAt: -1 });

// Virtual for display name
LeaveTypeSchema.virtual('displayName').get(function() {
  return `${this.name} (${this.code})`;
});

// Pre-save middleware
LeaveTypeSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Static methods
LeaveTypeSchema.statics.findActive = function() {
  return this.find({ isActive: true, isDeleted: false });
};

LeaveTypeSchema.statics.findByCode = function(code: string) {
  return this.findOne({ code: code.toUpperCase(), isDeleted: false });
};

// Instance methods
LeaveTypeSchema.methods.softDelete = function(userId: mongoose.Types.ObjectId) {
  this.isDeleted = true;
  this.deletedAt = new Date();
  this.updatedBy = userId;
  return this.save();
};

LeaveTypeSchema.methods.activate = function(userId: mongoose.Types.ObjectId) {
  this.isActive = true;
  this.updatedBy = userId;
  return this.save();
};

LeaveTypeSchema.methods.deactivate = function(userId: mongoose.Types.ObjectId) {
  this.isActive = false;
  this.updatedBy = userId;
  return this.save();
};

const LeaveType: Model<ILeaveType> = mongoose.models.LeaveType || mongoose.model<ILeaveType>('LeaveType', LeaveTypeSchema);

export default LeaveType;
