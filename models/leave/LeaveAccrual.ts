import mongoose, { Schema, Document } from 'mongoose';

/**
 * Interface for leave accrual document
 */
export interface ILeaveAccrual extends Document {
  employeeId: mongoose.Types.ObjectId;
  leaveTypeId: mongoose.Types.ObjectId;
  accrualDate: Date;
  accruedDays: number;
  accrualType: 'monthly' | 'quarterly' | 'annually' | 'pro-rata' | 'manual';
  accrualPeriod: string; // e.g., "2024-01", "2024-Q1", "2024"
  reason: string;
  isProcessed: boolean;
  processedDate?: Date;
  processedBy?: mongoose.Types.ObjectId;
  notes?: string;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Schema for leave accrual
 */
const LeaveAccrualSchema: Schema = new Schema(
  {
    employeeId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Employee',
      required: true,
    },
    leaveTypeId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'LeaveType',
      required: true,
    },
    accrualDate: {
      type: Date,
      required: true,
    },
    accruedDays: {
      type: Number,
      required: true,
      min: 0,
    },
    accrualType: {
      type: String,
      enum: ['monthly', 'quarterly', 'annually', 'pro-rata', 'manual'],
      required: true,
    },
    accrualPeriod: {
      type: String,
      required: true,
      trim: true,
    },
    reason: {
      type: String,
      required: true,
      trim: true,
    },
    isProcessed: {
      type: Boolean,
      default: false,
    },
    processedDate: {
      type: Date,
    },
    processedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    notes: {
      type: String,
      trim: true,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes for faster queries
LeaveAccrualSchema.index({ employeeId: 1, leaveTypeId: 1 });
LeaveAccrualSchema.index({ accrualDate: 1 });
LeaveAccrualSchema.index({ accrualPeriod: 1 });
LeaveAccrualSchema.index({ isProcessed: 1 });
LeaveAccrualSchema.index({ accrualType: 1 });

// Compound index for unique accrual per employee, leave type, and period
LeaveAccrualSchema.index(
  { employeeId: 1, leaveTypeId: 1, accrualPeriod: 1 },
  { unique: true }
);

// Create and export the model
const LeaveAccrual = mongoose.models.LeaveAccrual || mongoose.model<ILeaveAccrual>('LeaveAccrual', LeaveAccrualSchema);

export default LeaveAccrual;
