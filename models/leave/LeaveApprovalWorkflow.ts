import mongoose, { Schema, Document } from 'mongoose';

/**
 * Interface for leave approval workflow document
 */
export interface ILeaveApprovalWorkflow extends Document {
  leaveRequestId: mongoose.Types.ObjectId;
  workflowSteps: {
    stepNumber: number;
    approverType: 'direct-manager' | 'department-head' | 'hr-manager' | 'specific-user' | 'role-based';
    approverUserId?: mongoose.Types.ObjectId;
    approverRole?: string;
    approverDepartment?: mongoose.Types.ObjectId;
    isRequired: boolean;
    canDelegate: boolean;
    status: 'pending' | 'approved' | 'rejected' | 'skipped' | 'delegated';
    approvedBy?: mongoose.Types.ObjectId;
    approvedDate?: Date;
    rejectionReason?: string;
    comments?: string;
    delegatedTo?: mongoose.Types.ObjectId;
    delegatedDate?: Date;
    delegationReason?: string;
    escalationDate?: Date;
    escalatedTo?: mongoose.Types.ObjectId;
    escalationReason?: string;
  }[];
  currentStep: number;
  overallStatus: 'pending' | 'approved' | 'rejected' | 'cancelled';
  workflowType: 'single-level' | 'multi-level' | 'department-based' | 'custom';
  autoEscalationEnabled: boolean;
  escalationDays?: number;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Schema for leave approval workflow
 */
const LeaveApprovalWorkflowSchema: Schema = new Schema(
  {
    leaveRequestId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Leave',
      required: true,
      unique: true,
    },
    workflowSteps: [{
      stepNumber: {
        type: Number,
        required: true,
        min: 1,
      },
      approverType: {
        type: String,
        enum: ['direct-manager', 'department-head', 'hr-manager', 'specific-user', 'role-based'],
        required: true,
      },
      approverUserId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      approverRole: {
        type: String,
        trim: true,
      },
      approverDepartment: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Department',
      },
      isRequired: {
        type: Boolean,
        default: true,
      },
      canDelegate: {
        type: Boolean,
        default: false,
      },
      status: {
        type: String,
        enum: ['pending', 'approved', 'rejected', 'skipped', 'delegated'],
        default: 'pending',
      },
      approvedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      approvedDate: {
        type: Date,
      },
      rejectionReason: {
        type: String,
        trim: true,
      },
      comments: {
        type: String,
        trim: true,
      },
      delegatedTo: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      delegatedDate: {
        type: Date,
      },
      delegationReason: {
        type: String,
        trim: true,
      },
      escalationDate: {
        type: Date,
      },
      escalatedTo: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      escalationReason: {
        type: String,
        trim: true,
      },
    }],
    currentStep: {
      type: Number,
      default: 1,
      min: 1,
    },
    overallStatus: {
      type: String,
      enum: ['pending', 'approved', 'rejected', 'cancelled'],
      default: 'pending',
    },
    workflowType: {
      type: String,
      enum: ['single-level', 'multi-level', 'department-based', 'custom'],
      required: true,
    },
    autoEscalationEnabled: {
      type: Boolean,
      default: false,
    },
    escalationDays: {
      type: Number,
      min: 1,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes for faster queries
LeaveApprovalWorkflowSchema.index({ leaveRequestId: 1 });
LeaveApprovalWorkflowSchema.index({ overallStatus: 1 });
LeaveApprovalWorkflowSchema.index({ currentStep: 1 });
LeaveApprovalWorkflowSchema.index({ 'workflowSteps.approverUserId': 1 });
LeaveApprovalWorkflowSchema.index({ 'workflowSteps.status': 1 });

// Create and export the model
const LeaveApprovalWorkflow = mongoose.models.LeaveApprovalWorkflow || mongoose.model<ILeaveApprovalWorkflow>('LeaveApprovalWorkflow', LeaveApprovalWorkflowSchema);

export default LeaveApprovalWorkflow;
