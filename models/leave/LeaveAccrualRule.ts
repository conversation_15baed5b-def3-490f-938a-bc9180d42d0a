import mongoose, { Schema, Document } from 'mongoose';

/**
 * Interface for leave accrual rule document
 */
export interface ILeaveAccrualRule extends Document {
  name: string;
  description?: string;
  leaveTypeId: mongoose.Types.ObjectId;
  isActive: boolean;
  accrualFrequency: 'monthly' | 'quarterly' | 'annually';
  accrualMethod: 'fixed' | 'tenure-based' | 'pro-rata';
  
  // Fixed accrual settings
  fixedDaysPerPeriod?: number;
  
  // Tenure-based accrual settings
  tenureRules?: {
    minTenureMonths: number;
    maxTenureMonths?: number;
    daysPerPeriod: number;
  }[];
  
  // Pro-rata settings
  proRataEnabled: boolean;
  proRataStartDate?: 'hire-date' | 'year-start' | 'custom';
  
  // Eligibility criteria
  eligibilityCriteria: {
    minTenureMonths?: number;
    employmentTypes?: string[]; // 'permanent', 'contract', 'temporary'
    departments?: mongoose.Types.ObjectId[];
    roles?: string[];
    grades?: string[];
  };
  
  // Accrual limits
  maxAccrualPerYear?: number;
  maxAccrualBalance?: number;
  
  // Processing settings
  accrualStartDay: number; // Day of month/quarter for processing
  autoProcess: boolean;
  
  // Effective dates
  effectiveFrom: Date;
  effectiveTo?: Date;
  
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Schema for leave accrual rule
 */
const LeaveAccrualRuleSchema: Schema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    leaveTypeId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'LeaveType',
      required: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    accrualFrequency: {
      type: String,
      enum: ['monthly', 'quarterly', 'annually'],
      required: true,
    },
    accrualMethod: {
      type: String,
      enum: ['fixed', 'tenure-based', 'pro-rata'],
      required: true,
    },
    fixedDaysPerPeriod: {
      type: Number,
      min: 0,
    },
    tenureRules: [{
      minTenureMonths: {
        type: Number,
        required: true,
        min: 0,
      },
      maxTenureMonths: {
        type: Number,
        min: 0,
      },
      daysPerPeriod: {
        type: Number,
        required: true,
        min: 0,
      },
    }],
    proRataEnabled: {
      type: Boolean,
      default: true,
    },
    proRataStartDate: {
      type: String,
      enum: ['hire-date', 'year-start', 'custom'],
      default: 'hire-date',
    },
    eligibilityCriteria: {
      minTenureMonths: {
        type: Number,
        min: 0,
      },
      employmentTypes: [{
        type: String,
        trim: true,
      }],
      departments: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Department',
      }],
      roles: [{
        type: String,
        trim: true,
      }],
      grades: [{
        type: String,
        trim: true,
      }],
    },
    maxAccrualPerYear: {
      type: Number,
      min: 0,
    },
    maxAccrualBalance: {
      type: Number,
      min: 0,
    },
    accrualStartDay: {
      type: Number,
      required: true,
      min: 1,
      max: 31,
      default: 1,
    },
    autoProcess: {
      type: Boolean,
      default: true,
    },
    effectiveFrom: {
      type: Date,
      required: true,
    },
    effectiveTo: {
      type: Date,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes for faster queries
LeaveAccrualRuleSchema.index({ leaveTypeId: 1 });
LeaveAccrualRuleSchema.index({ isActive: 1 });
LeaveAccrualRuleSchema.index({ accrualFrequency: 1 });
LeaveAccrualRuleSchema.index({ effectiveFrom: 1, effectiveTo: 1 });
LeaveAccrualRuleSchema.index({ autoProcess: 1 });

// Create and export the model
const LeaveAccrualRule = mongoose.models.LeaveAccrualRule || mongoose.model<ILeaveAccrualRule>('LeaveAccrualRule', LeaveAccrualRuleSchema);

export default LeaveAccrualRule;
