import mongoose, { Schema, Document } from 'mongoose';

/**
 * Interface for leave encashment rule document
 */
export interface ILeaveEncashmentRule extends Document {
  name: string;
  description?: string;
  isActive: boolean;
  isDefault: boolean;
  
  // Applicability criteria
  applicabilityCriteria: {
    leaveTypes?: mongoose.Types.ObjectId[];
    departments?: mongoose.Types.ObjectId[];
    roles?: string[];
    grades?: string[];
    employmentTypes?: string[];
    minTenureMonths?: number;
  };
  
  // Encashment limits
  maxEncashableDays: number; // Maximum days that can be encashed
  minRetentionDays: number; // Minimum days that must be retained
  maxEncashmentPerYear: number; // Maximum number of encashments per year
  
  // Encashment rates
  encashmentRate: number; // Percentage of daily rate (e.g., 100% = full daily rate)
  rateCalculationMethod: 'basic-salary' | 'gross-salary' | 'fixed-rate' | 'custom';
  fixedRateAmount?: number; // Used when method is 'fixed-rate'
  
  // Timing restrictions
  allowedMonths?: number[]; // Months when encashment is allowed (1-12)
  minDaysBetweenEncashments?: number; // Minimum days between encashments
  
  // Approval requirements
  requiresApproval: boolean;
  approvalLevels: {
    level: number;
    approverType: 'direct-manager' | 'department-head' | 'hr-manager' | 'specific-user';
    approverUserId?: mongoose.Types.ObjectId;
    isRequired: boolean;
  }[];
  
  // Payroll integration
  payrollIntegration: {
    enabled: boolean;
    payrollComponent: string; // Component code in payroll system
    taxable: boolean;
    includeInGrossPay: boolean;
  };
  
  // Effective dates
  effectiveFrom: Date;
  effectiveTo?: Date;
  
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Schema for leave encashment rule
 */
const LeaveEncashmentRuleSchema: Schema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
    applicabilityCriteria: {
      leaveTypes: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'LeaveType',
      }],
      departments: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Department',
      }],
      roles: [{
        type: String,
        trim: true,
      }],
      grades: [{
        type: String,
        trim: true,
      }],
      employmentTypes: [{
        type: String,
        trim: true,
      }],
      minTenureMonths: {
        type: Number,
        min: 0,
      },
    },
    maxEncashableDays: {
      type: Number,
      required: true,
      min: 0,
    },
    minRetentionDays: {
      type: Number,
      required: true,
      min: 0,
    },
    maxEncashmentPerYear: {
      type: Number,
      required: true,
      min: 1,
      default: 1,
    },
    encashmentRate: {
      type: Number,
      required: true,
      min: 0,
      max: 200, // Allow up to 200% of daily rate
      default: 100,
    },
    rateCalculationMethod: {
      type: String,
      enum: ['basic-salary', 'gross-salary', 'fixed-rate', 'custom'],
      required: true,
      default: 'basic-salary',
    },
    fixedRateAmount: {
      type: Number,
      min: 0,
    },
    allowedMonths: [{
      type: Number,
      min: 1,
      max: 12,
    }],
    minDaysBetweenEncashments: {
      type: Number,
      min: 0,
      default: 0,
    },
    requiresApproval: {
      type: Boolean,
      default: true,
    },
    approvalLevels: [{
      level: {
        type: Number,
        required: true,
        min: 1,
      },
      approverType: {
        type: String,
        enum: ['direct-manager', 'department-head', 'hr-manager', 'specific-user'],
        required: true,
      },
      approverUserId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      isRequired: {
        type: Boolean,
        default: true,
      },
    }],
    payrollIntegration: {
      enabled: {
        type: Boolean,
        default: true,
      },
      payrollComponent: {
        type: String,
        trim: true,
        default: 'LEAVE_ENCASHMENT',
      },
      taxable: {
        type: Boolean,
        default: true,
      },
      includeInGrossPay: {
        type: Boolean,
        default: true,
      },
    },
    effectiveFrom: {
      type: Date,
      required: true,
    },
    effectiveTo: {
      type: Date,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes for faster queries
LeaveEncashmentRuleSchema.index({ isActive: 1 });
LeaveEncashmentRuleSchema.index({ isDefault: 1 });
LeaveEncashmentRuleSchema.index({ effectiveFrom: 1, effectiveTo: 1 });
LeaveEncashmentRuleSchema.index({ 'applicabilityCriteria.leaveTypes': 1 });
LeaveEncashmentRuleSchema.index({ 'applicabilityCriteria.departments': 1 });

// Create and export the model
const LeaveEncashmentRule = mongoose.models.LeaveEncashmentRule || mongoose.model<ILeaveEncashmentRule>('LeaveEncashmentRule', LeaveEncashmentRuleSchema);

export default LeaveEncashmentRule;
