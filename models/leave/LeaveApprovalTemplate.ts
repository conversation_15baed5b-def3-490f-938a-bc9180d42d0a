import mongoose, { Schema, Document } from 'mongoose';

/**
 * Interface for leave approval template document
 */
export interface ILeaveApprovalTemplate extends Document {
  name: string;
  description?: string;
  isActive: boolean;
  isDefault: boolean;
  workflowType: 'single-level' | 'multi-level' | 'department-based' | 'custom';
  
  // Applicability criteria
  applicabilityCriteria: {
    leaveTypes?: mongoose.Types.ObjectId[];
    departments?: mongoose.Types.ObjectId[];
    roles?: string[];
    grades?: string[];
    employmentTypes?: string[];
    minLeaveDays?: number;
    maxLeaveDays?: number;
  };
  
  // Workflow steps template
  stepTemplates: {
    stepNumber: number;
    stepName: string;
    approverType: 'direct-manager' | 'department-head' | 'hr-manager' | 'specific-user' | 'role-based';
    approverUserId?: mongoose.Types.ObjectId;
    approverRole?: string;
    approverDepartment?: mongoose.Types.ObjectId;
    isRequired: boolean;
    canDelegate: boolean;
    canSkip: boolean;
    skipConditions?: string[]; // Conditions under which this step can be skipped
    autoApprove?: boolean;
    autoApproveConditions?: string[]; // Conditions for auto-approval
  }[];
  
  // Escalation settings
  escalationSettings: {
    enabled: boolean;
    escalationDays: number;
    escalationType: 'next-level' | 'specific-user' | 'department-head' | 'hr-manager';
    escalationUserId?: mongoose.Types.ObjectId;
    escalationRole?: string;
    notifyOriginalApprover: boolean;
  };
  
  // Notification settings
  notificationSettings: {
    notifyOnSubmission: boolean;
    notifyOnApproval: boolean;
    notifyOnRejection: boolean;
    notifyOnEscalation: boolean;
    reminderDays: number[];
    customRecipients?: mongoose.Types.ObjectId[];
  };
  
  priority: number; // Higher number = higher priority when multiple templates match
  effectiveFrom: Date;
  effectiveTo?: Date;
  
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Schema for leave approval template
 */
const LeaveApprovalTemplateSchema: Schema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
    workflowType: {
      type: String,
      enum: ['single-level', 'multi-level', 'department-based', 'custom'],
      required: true,
    },
    applicabilityCriteria: {
      leaveTypes: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'LeaveType',
      }],
      departments: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Department',
      }],
      roles: [{
        type: String,
        trim: true,
      }],
      grades: [{
        type: String,
        trim: true,
      }],
      employmentTypes: [{
        type: String,
        trim: true,
      }],
      minLeaveDays: {
        type: Number,
        min: 0,
      },
      maxLeaveDays: {
        type: Number,
        min: 0,
      },
    },
    stepTemplates: [{
      stepNumber: {
        type: Number,
        required: true,
        min: 1,
      },
      stepName: {
        type: String,
        required: true,
        trim: true,
      },
      approverType: {
        type: String,
        enum: ['direct-manager', 'department-head', 'hr-manager', 'specific-user', 'role-based'],
        required: true,
      },
      approverUserId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      approverRole: {
        type: String,
        trim: true,
      },
      approverDepartment: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Department',
      },
      isRequired: {
        type: Boolean,
        default: true,
      },
      canDelegate: {
        type: Boolean,
        default: false,
      },
      canSkip: {
        type: Boolean,
        default: false,
      },
      skipConditions: [{
        type: String,
        trim: true,
      }],
      autoApprove: {
        type: Boolean,
        default: false,
      },
      autoApproveConditions: [{
        type: String,
        trim: true,
      }],
    }],
    escalationSettings: {
      enabled: {
        type: Boolean,
        default: false,
      },
      escalationDays: {
        type: Number,
        min: 1,
        default: 3,
      },
      escalationType: {
        type: String,
        enum: ['next-level', 'specific-user', 'department-head', 'hr-manager'],
        default: 'next-level',
      },
      escalationUserId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      escalationRole: {
        type: String,
        trim: true,
      },
      notifyOriginalApprover: {
        type: Boolean,
        default: true,
      },
    },
    notificationSettings: {
      notifyOnSubmission: {
        type: Boolean,
        default: true,
      },
      notifyOnApproval: {
        type: Boolean,
        default: true,
      },
      notifyOnRejection: {
        type: Boolean,
        default: true,
      },
      notifyOnEscalation: {
        type: Boolean,
        default: true,
      },
      reminderDays: [{
        type: Number,
        min: 1,
      }],
      customRecipients: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      }],
    },
    priority: {
      type: Number,
      default: 0,
    },
    effectiveFrom: {
      type: Date,
      required: true,
    },
    effectiveTo: {
      type: Date,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes for faster queries
LeaveApprovalTemplateSchema.index({ isActive: 1 });
LeaveApprovalTemplateSchema.index({ isDefault: 1 });
LeaveApprovalTemplateSchema.index({ workflowType: 1 });
LeaveApprovalTemplateSchema.index({ priority: -1 });
LeaveApprovalTemplateSchema.index({ effectiveFrom: 1, effectiveTo: 1 });
LeaveApprovalTemplateSchema.index({ 'applicabilityCriteria.leaveTypes': 1 });
LeaveApprovalTemplateSchema.index({ 'applicabilityCriteria.departments': 1 });

// Create and export the model
const LeaveApprovalTemplate = mongoose.models.LeaveApprovalTemplate || mongoose.model<ILeaveApprovalTemplate>('LeaveApprovalTemplate', LeaveApprovalTemplateSchema);

export default LeaveApprovalTemplate;
