import mongoose, { Schema, Document } from 'mongoose';

/**
 * Interface for leave encashment document
 */
export interface ILeaveEncashment extends Document {
  encashmentId: string;
  employeeId: mongoose.Types.ObjectId;
  leaveTypeId: mongoose.Types.ObjectId;
  year: number;
  daysEncashed: number;
  ratePerDay: number;
  totalAmount: number;
  encashmentDate: Date;
  status: 'pending' | 'approved' | 'rejected' | 'processed' | 'paid';
  
  // Approval workflow
  approvedBy?: mongoose.Types.ObjectId;
  approvalDate?: Date;
  rejectionReason?: string;
  
  // Payroll integration
  payrollRunId?: mongoose.Types.ObjectId;
  payrollProcessedDate?: Date;
  paymentReference?: string;
  
  // Leave balance impact
  balanceBeforeEncashment: number;
  balanceAfterEncashment: number;
  
  // Encashment rules applied
  encashmentRuleId?: mongoose.Types.ObjectId;
  maxEncashableDays: number;
  minRetentionDays: number;
  encashmentRate: number; // Percentage of daily rate
  
  // Additional details
  reason?: string;
  notes?: string;
  attachments?: string[];
  
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Schema for leave encashment
 */
const LeaveEncashmentSchema: Schema = new Schema(
  {
    encashmentId: {
      type: String,
      required: true,
      unique: true,
    },
    employeeId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Employee',
      required: true,
    },
    leaveTypeId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'LeaveType',
      required: true,
    },
    year: {
      type: Number,
      required: true,
    },
    daysEncashed: {
      type: Number,
      required: true,
      min: 0,
    },
    ratePerDay: {
      type: Number,
      required: true,
      min: 0,
    },
    totalAmount: {
      type: Number,
      required: true,
      min: 0,
    },
    encashmentDate: {
      type: Date,
      required: true,
    },
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected', 'processed', 'paid'],
      default: 'pending',
    },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    approvalDate: {
      type: Date,
    },
    rejectionReason: {
      type: String,
      trim: true,
    },
    payrollRunId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'PayrollRun',
    },
    payrollProcessedDate: {
      type: Date,
    },
    paymentReference: {
      type: String,
      trim: true,
    },
    balanceBeforeEncashment: {
      type: Number,
      required: true,
      min: 0,
    },
    balanceAfterEncashment: {
      type: Number,
      required: true,
      min: 0,
    },
    encashmentRuleId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'LeaveEncashmentRule',
    },
    maxEncashableDays: {
      type: Number,
      required: true,
      min: 0,
    },
    minRetentionDays: {
      type: Number,
      required: true,
      min: 0,
    },
    encashmentRate: {
      type: Number,
      required: true,
      min: 0,
      max: 100,
    },
    reason: {
      type: String,
      trim: true,
    },
    notes: {
      type: String,
      trim: true,
    },
    attachments: [{
      type: String,
      trim: true,
    }],
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes for faster queries
LeaveEncashmentSchema.index({ employeeId: 1, year: 1 });
LeaveEncashmentSchema.index({ leaveTypeId: 1 });
LeaveEncashmentSchema.index({ status: 1 });
LeaveEncashmentSchema.index({ encashmentDate: 1 });
LeaveEncashmentSchema.index({ payrollRunId: 1 });
LeaveEncashmentSchema.index({ encashmentId: 1 });

// Compound index for unique encashment per employee, leave type, and year
LeaveEncashmentSchema.index(
  { employeeId: 1, leaveTypeId: 1, year: 1 },
  { 
    unique: true,
    partialFilterExpression: { 
      status: { $in: ['pending', 'approved', 'processed', 'paid'] } 
    }
  }
);

// Create and export the model
const LeaveEncashment = mongoose.models.LeaveEncashment || mongoose.model<ILeaveEncashment>('LeaveEncashment', LeaveEncashmentSchema);

export default LeaveEncashment;
