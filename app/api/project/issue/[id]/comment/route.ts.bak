// app/api/project/issue/[id]/comment/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import { IssueService } from '@/lib/backend/services/project/IssueService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

// Initialize services
const issueService = new IssueService();

/**
 * GET /api/project/issue/[id]/comment
 * Get comments for an issue
 */
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '20', 10);
    const sortField = searchParams.get('sortField') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    // Resolve the params promise
    const { id } = await context.params;

    // Get comments
    const comments = await issueService.getComments(
      id,
      {
        sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 },
        page,
        limit
      }
    );

    return NextResponse.json(comments);
  } catch (error: unknown) {
    logger.error('Error in GET /api/project/issue/[id]/comment', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}

/**
 * POST /api/project/issue/[id]/comment
 * Add a comment to an issue
 */
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await request.json();
    const { content, attachments } = body;

    // Validate content
    if (!content || content.trim() === '') {
      return NextResponse.json(
        { error: 'Comment content is required' },
        { status: 400 }
      );
    }

    // Resolve the params promise
    const { id } = await context.params;

    // Add comment
    const comment = await issueService.addComment(
      id,
      content,
      user.id,
      attachments
    );

    return NextResponse.json(comment, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in POST /api/project/issue/[id]/comment', LogCategory.PROJECT, error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: (error as any)?.status || 500 }
    );
  }
}
