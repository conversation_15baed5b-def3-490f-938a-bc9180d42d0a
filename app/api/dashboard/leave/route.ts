// app/api/dashboard/leave/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { leaveDashboardService } from '@/lib/services/dashboard/leave-dashboard-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

export const runtime = 'nodejs';

/**
 * GET /api/dashboard/leave
 * Get leave requests for dashboard display
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check permissions - allow most authenticated users to view leave data
    const canViewLeaveData = hasRequiredPermissions(user.role, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_OFFICER,
      UserRole.EMPLOYEE,
      UserRole.MANAGER
    ]);

    if (!canViewLeaveData) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view leave data' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50); // Max 50 items
    
    // Parse filters
    const filters: any = {};
    
    const status = searchParams.get('status')?.split(',').filter(Boolean);
    if (status && status.length > 0) {
      filters.status = status;
    }
    
    const leaveTypes = searchParams.get('leaveTypes')?.split(',').filter(Boolean);
    if (leaveTypes && leaveTypes.length > 0) {
      filters.leaveTypes = leaveTypes;
    }
    
    const departments = searchParams.get('departments')?.split(',').filter(Boolean);
    if (departments && departments.length > 0) {
      filters.departments = departments;
    }
    
    const employeeId = searchParams.get('employeeId');
    if (employeeId) {
      filters.employeeId = employeeId;
    }
    
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    if (startDate || endDate) {
      if (startDate) filters.startDate = new Date(startDate);
      if (endDate) filters.endDate = new Date(endDate);
    }

    logger.info('Fetching dashboard leave requests', LogCategory.DASHBOARD, {
      userId: user._id.toString(),
      userRole: user.role,
      limit,
      filters
    });

    // Fetch leave requests
    const leaveRequests = await leaveDashboardService.getLeaveRequests(
      user._id.toString(),
      user.role,
      limit,
      filters
    );

    // Calculate summary statistics
    const summary = {
      total: leaveRequests.length,
      pending: leaveRequests.filter(r => r.status === 'pending').length,
      approved: leaveRequests.filter(r => r.status === 'approved').length,
      rejected: leaveRequests.filter(r => r.status === 'rejected').length,
      overdue: leaveRequests.filter(r => r.isOverdue).length,
      urgent: leaveRequests.filter(r => r.urgency === 'urgent' || r.urgency === 'high').length,
      totalDays: leaveRequests.reduce((sum, r) => sum + r.duration, 0),
      byStatus: leaveRequests.reduce((acc, request) => {
        acc[request.status] = (acc[request.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };

    logger.info('Successfully fetched dashboard leave requests', LogCategory.DASHBOARD, {
      userId: user._id.toString(),
      count: leaveRequests.length,
      pending: summary.pending,
      overdue: summary.overdue
    });

    return NextResponse.json({
      success: true,
      data: {
        leaveRequests,
        summary,
        count: leaveRequests.length,
        limit,
        hasMore: leaveRequests.length === limit
      }
    });

  } catch (error: any) {
    logger.error('Error fetching dashboard leave requests', LogCategory.DASHBOARD, error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch leave requests',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/dashboard/leave/stats
 * Get leave statistics for dashboard metrics
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check permissions - allow management and HR to view detailed stats
    const canViewStats = hasRequiredPermissions(user.role, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD,
      UserRole.FINANCE_MANAGER,
      UserRole.MANAGER
    ]);

    if (!canViewStats) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view leave statistics' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();
    const period = body.period || 'month';

    // Validate period
    if (!['day', 'week', 'month', 'year'].includes(period)) {
      return NextResponse.json(
        { error: 'Invalid period. Must be day, week, month, or year' },
        { status: 400 }
      );
    }

    logger.info('Fetching leave statistics', LogCategory.DASHBOARD, {
      userId: user._id.toString(),
      userRole: user.role,
      period
    });

    // Fetch statistics
    const stats = await leaveDashboardService.getLeaveStats(
      user._id.toString(),
      user.role,
      period
    );

    logger.info('Successfully fetched leave statistics', LogCategory.DASHBOARD, {
      userId: user._id.toString(),
      period,
      totalRequests: stats.totalRequests,
      pendingRequests: stats.pendingRequests
    });

    return NextResponse.json({
      success: true,
      data: stats
    });

  } catch (error: any) {
    logger.error('Error fetching leave statistics', LogCategory.DASHBOARD, error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch leave statistics',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/dashboard/leave/upcoming
 * Get upcoming leaves
 */
export async function PUT(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check permissions - allow most authenticated users to view upcoming leaves
    const canViewUpcoming = hasRequiredPermissions(user.role, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD,
      UserRole.FINANCE_MANAGER,
      UserRole.FINANCE_OFFICER,
      UserRole.EMPLOYEE,
      UserRole.MANAGER
    ]);

    if (!canViewUpcoming) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view upcoming leaves' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();
    const limit = Math.min(body.limit || 10, 50);
    const daysAhead = Math.min(body.daysAhead || 30, 90);

    logger.info('Fetching upcoming leaves', LogCategory.DASHBOARD, {
      userId: user._id.toString(),
      limit,
      daysAhead
    });

    // Fetch upcoming leaves
    const upcomingLeaves = await leaveDashboardService.getUpcomingLeaves(
      user._id.toString(),
      user.role,
      limit,
      daysAhead
    );

    logger.info('Successfully fetched upcoming leaves', LogCategory.DASHBOARD, {
      userId: user._id.toString(),
      count: upcomingLeaves.length
    });

    return NextResponse.json({
      success: true,
      data: {
        upcomingLeaves,
        count: upcomingLeaves.length,
        daysAhead
      }
    });

  } catch (error: any) {
    logger.error('Error fetching upcoming leaves', LogCategory.DASHBOARD, error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch upcoming leaves',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}
