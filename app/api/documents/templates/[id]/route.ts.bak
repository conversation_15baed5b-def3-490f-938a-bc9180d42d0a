import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { documentTemplateService } from '@/services/document/DocumentTemplateService';
import { documentUploadService } from '@/services/document/DocumentUploadService';
import { parseFormData } from '@/lib/backend/utils/form-parser';
import mongoose from 'mongoose';

/**
 * GET /api/documents/templates/[id]
 * Get a document template by ID
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();


    // Resolve the params promise
    const { id } = await params;

    // Get template
    const template = await documentTemplateService.getTemplateById(id);

    if (!template) {
      return NextResponse.json(
        { error: 'Document template not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(template);
  } catch (error: unknown) {
    logger.error('Error getting document template', LogCategory.API, error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/documents/templates/[id]
 * Update a document template
 */
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Check if request is multipart form data
    const contentType = req.headers.get('content-type') || '';

    if (contentType.includes('multipart/form-data')) {
      // Parse form data
      const formData = await req.formData();
      const { fields, files } = await parseFormData(formData);

      // Get existing template
      const existingTemplate = await documentTemplateService.getTemplateById(id);
      if (!existingTemplate) {
        return NextResponse.json(
          { error: 'Document template not found' },
          { status: 404 }
        );
      }

      // Prepare update data
      const updateData: any = {
        updatedBy: new mongoose.Types.ObjectId(user.id)
      };

      // Update fields if provided
      if (fields.name) updateData.name = fields.name;
      if (fields.code) updateData.code = fields.code;
      if (fields.description !== undefined) updateData.description = fields.description;
      if (fields.categoryId) updateData.categoryId = new mongoose.Types.ObjectId(fields.categoryId);
      if (fields.variables) updateData.variables = fields.variables.split(',').map((v: string) => v.trim());
      if (fields.isActive !== undefined) updateData.isActive = fields.isActive === 'true';

      // Upload new file if provided
      const file = files.file;
      if (file) {
        const uploadResult = await documentUploadService.uploadDocument(
          file.data,
          file.name,
          fields.categoryId || existingTemplate.categoryId.toString()
        );

        updateData.filePath = uploadResult.filePath;
        updateData.fileName = uploadResult.fileName;
        updateData.fileType = uploadResult.fileType;
        updateData.fileSize = uploadResult.fileSize;
      }

      // Update template
      const template = await documentTemplateService.updateTemplate(
        id,
        updateData,
        fields.createNewVersion === 'true'
      );

      return NextResponse.json({
        success: true,
        message: 'Document template updated successfully',
        data: template
      });
    } else {
      // Handle JSON request
      const body = await req.json();

      // Set updated by and convert ObjectIds
      body.updatedBy = new mongoose.Types.ObjectId(user.id);
      if (body.categoryId) {
        body.categoryId = new mongoose.Types.ObjectId(body.categoryId);
      }

      // Update template
      const template = await documentTemplateService.updateTemplate(
        id,
        body,
        body.createNewVersion === true
      );

      if (!template) {
        return NextResponse.json(
          { error: 'Document template not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        message: 'Document template updated successfully',
        data: template
      });
    }
  } catch (error: unknown) {
    logger.error('Error updating document template', LogCategory.API, error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/documents/templates/[id]
 * Delete a document template
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string  }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Resolve the params promise
    const { id } = await params;

    // Delete template
    const template = await documentTemplateService.deleteTemplate(id);

    if (!template) {
      return NextResponse.json(
        { error: 'Document template not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Document template deleted successfully'
    });
  } catch (error: unknown) {
    logger.error('Error deleting document template', LogCategory.API, error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
