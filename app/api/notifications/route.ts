// app/api/notifications/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { notificationService } from '@/services/notifications/NotificationService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/notifications
 * Get notifications for the current user
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const unreadOnly = searchParams.get('unreadOnly') === 'true';
    const type = searchParams.get('type') || undefined;
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Get notifications
    const result = await notificationService.getUserNotifications(
      user.id,
      {
        page,
        limit,
        unreadOnly,
        type,
        sortBy,
        sortOrder: sortOrder as 'asc' | 'desc'
      }
    );

    return NextResponse.json(result);
  } catch (error: unknown) {
    logger.error('Error in notifications GET handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}

/**
 * POST /api/notifications
 * Create a new notification
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await request.json();

    // Validate required fields
    if (!body.title || !body.message || !body.recipients || !body.recipients.length) {
      return NextResponse.json({
        error: 'Title, message, and recipients are required'
      }, { status: 400 });
    }

    // Send notification
    const notification = await notificationService.sendNotification(
      body.title,
      body.message,
      body.recipients,
      {
        type: body.type,
        priority: body.priority,
        actions: body.actions,
        expiresAt: body.expiresAt,
        sourceType: body.sourceType,
        sourceId: body.sourceId,
        createdBy: user.id
      }
    );

    return NextResponse.json(notification, { status: 201 });
  } catch (error: unknown) {
    logger.error('Error in notifications POST handler', LogCategory.API, error);
    return NextResponse.json({ error: error instanceof Error ? error instanceof Error ? error instanceof Error ? error.message : 'An error occurred' : 'An error occurred' : 'An error occurred' }, { status: 500 });
  }
}
