import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import mongoose from 'mongoose';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

export async function GET(req: NextRequest): Promise<Response> {
  try {
    console.log('Database health check started...');
    
    // Check current connection state
    const initialState = {
      readyState: mongoose.connection.readyState,
      host: mongoose.connection.host,
      database: mongoose.connection.name
    };
    
    console.log('Initial connection state:', initialState);
    
    // Attempt to connect
    const startTime = Date.now();
    await connectToDatabase();
    const connectionTime = Date.now() - startTime;
    
    // Verify connection is working by running a simple query
    const testResult = await mongoose.connection.db.admin().ping();
    
    const finalState = {
      readyState: mongoose.connection.readyState,
      host: mongoose.connection.host,
      database: mongoose.connection.name,
      connectionTime: `${connectionTime}ms`
    };
    
    console.log('Final connection state:', finalState);
    console.log('Database ping result:', testResult);
    
    return NextResponse.json({
      status: 'healthy',
      message: 'Database connection is working',
      connection: finalState,
      ping: testResult,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Database health check failed:', error);
    
    const errorInfo = {
      message: error instanceof Error ? error.message : 'Unknown error',
      readyState: mongoose.connection.readyState,
      host: mongoose.connection.host,
      database: mongoose.connection.name
    };
    
    logger.error('Database health check failed', LogCategory.DATABASE, error);
    
    return NextResponse.json({
      status: 'unhealthy',
      message: 'Database connection failed',
      error: errorInfo,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
