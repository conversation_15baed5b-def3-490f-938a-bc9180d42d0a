import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { leaveEncashmentService } from '@/services/leave/LeaveEncashmentService';
import mongoose from 'mongoose';

/**
 * POST /api/leave/encashments/[id]/approve
 * Approve a leave encashment request
 */
export async function POST(
  req: NextRequest, 
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only HR can approve encashments
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions to approve encashments' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get the encashment ID from params
    const { id } = await params;

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid encashment ID format' },
        { status: 400 }
      );
    }

    // Approve the encashment request
    const encashment = await leaveEncashmentService.approveEncashmentRequest(id, user.id);

    return NextResponse.json({
      success: true,
      message: 'Leave encashment request approved successfully',
      data: encashment
    });

  } catch (error: unknown) {
    logger.error('Error approving encashment request', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while approving encashment request' 
      },
      { status: 500 }
    );
  }
}
