import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { leaveService } from '@/services/leave/LeaveService';
import Employee from '@/models/Employee';

/**
 * GET /api/leave/balances
 * Get leave balances for the current user or specified employee (for HR managers)
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const requestedEmployeeId = searchParams.get('employeeId');
    const year = parseInt(searchParams.get('year') || new Date().getFullYear().toString());

    // Determine which employee's balances to fetch
    let employeeId = user.id; // Default to current user

    if (requestedEmployeeId) {
      // Check if user has permission to view other employees' balances
      const hasHRPermission = hasRequiredPermissions(user, [
        UserRole.SUPER_ADMIN,
        UserRole.SYSTEM_ADMIN,
        UserRole.HR_DIRECTOR,
        UserRole.HR_MANAGER,
        UserRole.HR_SPECIALIST,
        UserRole.DEPARTMENT_HEAD,
        UserRole.TEAM_LEADER
      ]);

      if (!hasHRPermission) {
        return NextResponse.json(
          { error: 'Forbidden: You do not have permission to view other employees\' leave balances' },
          { status: 403 }
        );
      }

      employeeId = requestedEmployeeId;
    }

    // Check if employee exists
    const employee = await Employee.findById(employeeId);
    if (!employee) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      );
    }

    // Get leave balances
    const leaveBalances = await leaveService.getLeaveBalances(employeeId, year);

    return NextResponse.json({
      data: leaveBalances,
      employee: {
        _id: employee._id,
        firstName: employee.firstName,
        lastName: employee.lastName,
        employeeId: employee.employeeId
      },
      year
    });
  } catch (error: unknown) {
    logger.error('Error getting leave balances', LogCategory.HR, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
