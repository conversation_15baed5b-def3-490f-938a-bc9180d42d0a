import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { leaveAccrualService } from '@/services/leave/LeaveAccrualService';

interface ProcessAccrualsPayload {
  type: 'monthly' | 'quarterly' | 'annually' | 'pro-rata';
  processingDate: string;
  employeeId?: string; // For pro-rata only
  hireDate?: string; // For pro-rata only
}

/**
 * POST /api/leave/accruals/process
 * Process leave accruals
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only HR can process accruals
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions to process accruals' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body: ProcessAccrualsPayload = await req.json();

    // Validate required fields
    if (!body.type || !body.processingDate) {
      return NextResponse.json(
        { error: 'Missing required fields: type, processingDate' },
        { status: 400 }
      );
    }

    // Validate type
    if (!['monthly', 'quarterly', 'annually', 'pro-rata'].includes(body.type)) {
      return NextResponse.json(
        { error: 'Invalid accrual type. Must be monthly, quarterly, annually, or pro-rata' },
        { status: 400 }
      );
    }

    const processingDate = new Date(body.processingDate);
    if (isNaN(processingDate.getTime())) {
      return NextResponse.json(
        { error: 'Invalid processing date format' },
        { status: 400 }
      );
    }

    // Validate pro-rata specific fields
    if (body.type === 'pro-rata') {
      if (!body.employeeId || !body.hireDate) {
        return NextResponse.json(
          { error: 'Missing required fields for pro-rata: employeeId, hireDate' },
          { status: 400 }
        );
      }

      const hireDate = new Date(body.hireDate);
      if (isNaN(hireDate.getTime())) {
        return NextResponse.json(
          { error: 'Invalid hire date format' },
          { status: 400 }
        );
      }
    }

    let result;

    // Process accruals based on type
    switch (body.type) {
      case 'monthly':
        result = await leaveAccrualService.processMonthlyAccruals(processingDate, user.id);
        break;
      case 'quarterly':
        result = await leaveAccrualService.processQuarterlyAccruals(processingDate, user.id);
        break;
      case 'annually':
        result = await leaveAccrualService.processAnnualAccruals(processingDate, user.id);
        break;
      case 'pro-rata':
        result = await leaveAccrualService.processProRataAccrual(
          body.employeeId!,
          new Date(body.hireDate!),
          user.id
        );
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid accrual type' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      message: `${body.type} accruals processed successfully`,
      data: result
    });

  } catch (error: unknown) {
    logger.error('Error processing leave accruals', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while processing accruals' 
      },
      { status: 500 }
    );
  }
}
