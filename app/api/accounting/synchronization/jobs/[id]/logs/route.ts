// app/api/accounting/synchronization/jobs/[id]/logs/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { synchronizationScheduler } from '@/lib/services/accounting/synchronization/synchronization-scheduler';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { connectToDatabase } from '@/lib/backend/database';

/**
 * GET /api/accounting/synchronization/jobs/[id]/logs
 * Get logs for a synchronization job
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {
  // Declare id at function scope
  let id = 'unknown';

  try {
    // Resolve the params promise
    const { id: jobId } = await params;
    id = jobId;
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '10');

    // Get job logs
    const logs = await synchronizationScheduler.getJobLogs(id, limit);

    return NextResponse.json(logs);
  } catch (error) {
    logger.error(`Error getting synchronization job logs: ${id}`, LogCategory.SYNC, error);
    return NextResponse.json(
      { error: 'Failed to get synchronization job logs' },
      { status: 500 }
    );
  }
}
