import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import ExternalSystem from '@/models/accounting/ExternalSystem';
import { IntegrationFactory } from '@/lib/services/accounting/integration/integration-factory';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET /api/accounting/integrations/[id]/logs
 * Get integration logs for an external system
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }): Promise<NextResponse> {

  try {
    // Resolve the params promise
    const { id } = await params;
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Get external system
    const externalSystem = await ExternalSystem.findById(id);

    if (!externalSystem) {
      return NextResponse.json(
        { error: 'External system not found' },
        { status: 404 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const operation = searchParams.get('operation') || undefined;
    const entityType = searchParams.get('entityType') || undefined;
    const status = searchParams.get('status') || undefined;
    const fromDate = searchParams.get('fromDate') ? new Date(searchParams.get('fromDate')!) : undefined;
    const toDate = searchParams.get('toDate') ? new Date(searchParams.get('toDate')!) : undefined;

    // Create integration service
    const integrationService = IntegrationFactory.createIntegrationService(externalSystem);

    // Get integration logs
    const result = await integrationService.getIntegrationLogs({
      page,
      limit,
      operation,
      entityType,
      status,
      fromDate,
      toDate
    });

    return NextResponse.json(result);
  } catch (error) {
    logger.error('Error getting integration logs', LogCategory.INTEGRATION, error);
    return NextResponse.json(
      { error: 'Failed to get integration logs' },
      { status: 500 }
    );
  }
}
