// app/api/accounting/income/monthly/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { connectToDatabase } from '@/lib/database';
import Income from '@/models/accounting/Income';
import Budget from '@/models/accounting/Budget';
import mongoose from 'mongoose';

export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.FINANCE_OFFICER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const fiscalYear = searchParams.get('fiscalYear') || '2024-2025';
    const budgetId = searchParams.get('budgetId');

    // Connect to database
    await connectToDatabase();

    // Build filter for income aggregation
    const filter: Record<string, any> = {
      fiscalYear,
      status: { $in: ['received', 'approved'] }, // Only count received/approved income
      appliedToBudget: true
    };

    // Add budget filter if provided
    if (budgetId) {
      filter.budget = new mongoose.Types.ObjectId(budgetId);
    }

    // Get budget data for monthly budgeted amounts
    let totalBudgeted = 0;
    if (budgetId) {
      const budgetData = await Budget.findById(budgetId)
        .populate({
          path: 'categories',
          match: { type: 'income' },
          select: 'name budgetedAmount'
        });

      if (budgetData && budgetData.categories) {
        totalBudgeted = budgetData.categories.reduce((sum: number, cat: any) => sum + (cat.budgetedAmount || 0), 0);
      }
    } else {
      // Get active budget for fiscal year
      const activeBudget = await Budget.findOne({
        fiscalYear,
        status: { $in: ['active', 'approved'] }
      }).populate({
        path: 'categories',
        match: { type: 'income' },
        select: 'name budgetedAmount'
      });

      if (activeBudget && activeBudget.categories) {
        totalBudgeted = activeBudget.categories.reduce((sum: number, cat: any) => sum + (cat.budgetedAmount || 0), 0);
      }
    }

    // Get monthly income data by source
    const monthlyIncomeData = await Income.aggregate([
      { $match: filter },
      {
        $group: {
          _id: {
            year: { $year: '$date' },
            month: { $month: '$date' },
            source: '$source'
          },
          amount: { $sum: '$amount' }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } }
    ]);

    // Group by month and calculate totals
    const monthlyData: Record<string, any> = {};
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    // Initialize all months with zero values
    monthNames.forEach(month => {
      monthlyData[month] = {
        month,
        government: 0,
        registration: 0,
        licensing: 0,
        donations: 0,
        other: 0,
        total: 0,
        budgeted: totalBudgeted > 0 ? Math.round(totalBudgeted / 12) : 0
      };
    });

    // Populate with actual data
    monthlyIncomeData.forEach(item => {
      const monthName = monthNames[item._id.month - 1];
      const source = item._id.source;

      if (monthlyData[monthName]) {
        switch (source) {
          case 'government_subvention':
            monthlyData[monthName].government = item.amount;
            break;
          case 'registration_fees':
            monthlyData[monthName].registration = item.amount;
            break;
          case 'licensing_fees':
            monthlyData[monthName].licensing = item.amount;
            break;
          case 'donations':
            monthlyData[monthName].donations = item.amount;
            break;
          default:
            monthlyData[monthName].other += item.amount;
            break;
        }

        // Update total
        monthlyData[monthName].total =
          monthlyData[monthName].government +
          monthlyData[monthName].registration +
          monthlyData[monthName].licensing +
          monthlyData[monthName].donations +
          monthlyData[monthName].other;
      }
    });

    // Convert to array and maintain fiscal year order (Jul-Jun)
    const fiscalYearOrder = ['Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    const monthlyIncome = fiscalYearOrder.map(month => monthlyData[month]);

    const responseData = {
      fiscalYear,
      budgetId,
      monthlyIncome
    };

    return NextResponse.json(responseData);
  } catch (error) {
    console.error('Error fetching monthly income data:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching monthly income data' },
      { status: 500 }
    );
  }
}
