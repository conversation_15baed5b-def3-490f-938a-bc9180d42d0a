// app/api/accounting/income/by-source/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
// Custom auth system doesn't require authOptions;
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { connectToDatabase } from '@/lib/database';
import Income from '@/models/accounting/Income';
import Budget from '@/models/accounting/Budget';
import mongoose from 'mongoose';

export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.FINANCE_OFFICER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const fiscalYear = searchParams.get('fiscalYear') || '2025-2026';
    const budgetId = searchParams.get('budgetId');

    // Connect to database
    await connectToDatabase();

    // Build filter for income aggregation
    const filter: Record<string, any> = {
      fiscalYear,
      status: { $in: ['received', 'approved'] }, // Only count received/approved income
      appliedToBudget: true
    };

    // Add budget filter if provided
    if (budgetId) {
      filter.budget = new mongoose.Types.ObjectId(budgetId);
    }

    // Aggregate income by source
    const incomeBySource = await Income.aggregate([
      { $match: filter },
      {
        $group: {
          _id: '$source',
          total: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      }
    ]);

    // Get budget data for comparison if budgetId is provided
    let budgetData: any = null;
    if (budgetId) {
      budgetData = await Budget.findById(budgetId)
        .populate({
          path: 'categories',
          match: { type: 'income' },
          select: 'name budgetedAmount actualAmount'
        });
    }

    // Calculate total actual income
    const totalIncome = incomeBySource.reduce((sum, item) => sum + item.total, 0);

    // Get total budgeted amount for income categories
    let totalBudgeted = 0;
    if (budgetData && budgetData.categories) {
      totalBudgeted = budgetData.categories.reduce((sum: number, cat: any) => sum + (cat.budgetedAmount || 0), 0);
    }

    // Format source data
    const sources = incomeBySource.map(item => {
      const sourceName = item._id === 'government_subvention' ? 'Government Subvention' :
        item._id === 'registration_fees' ? 'Registration Fees' :
        item._id === 'licensing_fees' ? 'Licensing Fees' :
        item._id === 'donations' ? 'Donations' : 'Other';

      // Find corresponding budget category for this source
      let budgeted = 0;
      if (budgetData && budgetData.categories) {
        const matchingCategory = budgetData.categories.find((cat: any) =>
          cat.name.toLowerCase().includes(sourceName.toLowerCase().split(' ')[0])
        );
        if (matchingCategory) {
          budgeted = matchingCategory.budgetedAmount || 0;
        }
      }

      const percentage = totalIncome > 0 ? (item.total / totalIncome) * 100 : 0;

      return {
        name: sourceName,
        total: item.total,
        budgeted,
        percentage: Math.round(percentage * 100) / 100,
        count: item.count
      };
    });

    // Add sources with zero income but have budget allocations
    if (budgetData && budgetData.categories) {
      const existingSources = sources.map(s => s.name.toLowerCase());

      budgetData.categories.forEach((cat: any) => {
        const categoryName = cat.name.toLowerCase();
        let sourceName = '';

        if (categoryName.includes('government') || categoryName.includes('subvention')) {
          sourceName = 'Government Subvention';
        } else if (categoryName.includes('registration')) {
          sourceName = 'Registration Fees';
        } else if (categoryName.includes('licensing')) {
          sourceName = 'Licensing Fees';
        } else if (categoryName.includes('donation')) {
          sourceName = 'Donations';
        }

        if (sourceName && !existingSources.includes(sourceName.toLowerCase())) {
          sources.push({
            name: sourceName,
            total: 0,
            budgeted: cat.budgetedAmount || 0,
            percentage: 0,
            count: 0
          });
        }
      });
    }

    const responseData = {
      fiscalYear,
      totalIncome,
      totalBudgeted,
      sources: sources.sort((a, b) => b.total - a.total) // Sort by total income descending
    };

    return NextResponse.json(responseData);
  } catch (error) {
    console.error('Error fetching income by source:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching income by source' },
      { status: 500 }
    );
  }
}
