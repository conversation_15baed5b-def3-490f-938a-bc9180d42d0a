// app/api/accounting/income/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/database';
import { logger } from '@/lib/utils/logger';
import { z } from 'zod';
import Income from '@/models/accounting/Income';
import Budget from '@/models/accounting/Budget';
import BudgetCategory from '@/models/accounting/BudgetCategory';
import BudgetSubcategory from '@/models/accounting/BudgetSubcategory';
import mongoose from 'mongoose';
import { budgetTransactionService } from '@/lib/services/accounting/budget-transaction-service';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

// Income validation schema
const incomeSchema = z.object({
  date: z.string().refine((val) => !isNaN(Date.parse(val)), {
    message: "Invalid date format",
  }),
  source: z.enum(['government_subvention', 'registration_fees', 'licensing_fees', 'donations', 'other']),
  subSource: z.string().optional(),
  amount: z.number().positive("Amount must be positive"),
  reference: z.string().min(2, "Reference must be at least 2 characters"),
  description: z.string().optional(),
  fiscalYear: z.string().min(4, "Fiscal year is required"),
  status: z.enum(['pending', 'received', 'cancelled']).default('pending'),
  paymentMethod: z.string().optional(),
  bankAccount: z.string().optional(),
  budget: z.string().optional(),
  budgetCategory: z.string().optional(),
  budgetSubcategory: z.string().optional(),
  appliedToBudget: z.boolean().optional().default(true),
  notes: z.string().optional(),
});

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const fiscalYear = searchParams.get('fiscalYear');
    const source = searchParams.get('source');
    const id = searchParams.get('id');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');

    // If ID is provided, get a specific income transaction
    if (id) {
      const income = await Income.findById(id)
        .populate('createdBy', 'name')
        .populate('budgetCategory', 'name type')
        .populate('budgetSubcategory', 'name')
        .populate('bankAccount', 'name accountNumber');

      if (!income) {
        return NextResponse.json(
          { error: 'Income transaction not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({ income });
    }

    // Build filter for listing income transactions
    const filter: Record<string, any> = {};

    // Add fiscal year filter if provided
    if (fiscalYear) {
      filter.fiscalYear = fiscalYear;
    }

    // Add source filter if provided
    if (source) {
      filter.source = source;
    }

    // Add status filter if provided
    if (status) {
      filter.status = status;
    }

    // Get income transactions with pagination
    const totalCount = await Income.countDocuments(filter);
    const totalPages = Math.ceil(totalCount / limit);
    const skip = (page - 1) * limit;

    const incomeTransactions = await Income.find(filter)
      .sort({ date: -1 })
      .skip(skip)
      .limit(limit)
      .populate('createdBy', 'name')
      .populate('budgetCategory', 'name type')
      .populate('budgetSubcategory', 'name')
      .populate('bankAccount', 'name accountNumber');

    // Get income summary
    const summary = await Income.aggregate([
      { $match: { fiscalYear: fiscalYear || { $exists: true } } },
      { $group: { _id: '$source', total: { $sum: '$amount' } } }
    ]);

    // Format summary data
    const incomeData = summary.map(item => {
      const sourceName = item._id === 'government_subvention' ? 'Government Subvention' :
        item._id === 'registration_fees' ? 'Registration Fees' :
        item._id === 'licensing_fees' ? 'Licensing Fees' :
        item._id === 'donations' ? 'Donations' : 'Other';

      return {
        name: sourceName,
        value: item.total,
        source: item._id
      };
    });

    // Calculate total income
    const totalIncome = incomeData.reduce((sum, item) => sum + item.value, 0);

    // Return income data
    return NextResponse.json({
      incomeTransactions,
      incomeData,
      totalIncome,
      pagination: {
        totalCount,
        totalPages,
        currentPage: page
      }
    });
  } catch (error: unknown) {
    logger.error('Error fetching income data', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get request body
    const data = await req.json();

    // Validate request body
    const validationResult = incomeSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    // Add created by
    const incomeData = {
      ...validationResult.data,
      createdBy: user.id
    };

    // Create income transaction
    const income = await Income.create(incomeData);

    // Link income to budget if budget and category are provided
    if (income.budget && income.budgetCategory && income.appliedToBudget) {
      try {
        // Validate budget and category before linking
        const budget = await Budget.findById(income.budget);
        if (!budget) {
          throw new Error(`Budget with ID ${income.budget} not found`);
        }

        // Check if budget is active
        if (budget.status !== 'active' && budget.status !== 'approved') {
          throw new Error(`Budget ${budget.name} is not active or approved`);
        }

        // Check if income date is within budget period
        const incomeDate = new Date(income.date);
        if (budget.startDate && budget.endDate) {
          const startDate = new Date(budget.startDate);
          const endDate = new Date(budget.endDate);

          if (incomeDate < startDate || incomeDate > endDate) {
            throw new Error(`Income date ${income.date} is outside the budget period (${budget.startDate} to ${budget.endDate})`);
          }
        }

        // Check if fiscal year matches
        if (budget.fiscalYear !== income.fiscalYear) {
          throw new Error(`Income fiscal year ${income.fiscalYear} does not match budget fiscal year ${budget.fiscalYear}`);
        }

        // Validate category
        const category = await BudgetCategory.findById(income.budgetCategory);
        if (!category) {
          throw new Error(`Category with ID ${income.budgetCategory} not found`);
        }

        // Check if category belongs to budget
        if (category.budget.toString() !== income.budget.toString()) {
          throw new Error(`Category ${category.name} does not belong to the selected budget`);
        }

        // Check if category is income type
        if (category.type !== 'income') {
          throw new Error(`Category ${category.name} is not an income category`);
        }

        // Validate subcategory if provided
        if (income.budgetSubcategory) {
          const subcategory = await BudgetSubcategory.findById(income.budgetSubcategory);
          if (!subcategory) {
            throw new Error(`Subcategory with ID ${income.budgetSubcategory} not found`);
          }

          // Check if subcategory belongs to category
          if (subcategory.parentCategory.toString() !== income.budgetCategory.toString()) {
            throw new Error(`Subcategory ${subcategory.name} does not belong to the selected category`);
          }
        }

        // Use the budget transaction service to link income to budget
        await budgetTransactionService.linkIncomeToBudget(
          income._id.toString(),
          income.budget.toString(),
          income.budgetCategory.toString(),
          income.budgetSubcategory ? income.budgetSubcategory.toString() : undefined
        );

        logger.info('Income linked to budget successfully', {
          incomeId: income._id,
          budgetId: income.budget,
          categoryId: income.budgetCategory
        });
      } catch (error) {
        logger.error('Error linking income to budget', error);
        // Continue even if budget update fails, but log the error
        // In a production environment, you might want to handle this differently
      }
    }

    return NextResponse.json(
      {
        success: true,
        message: 'Income transaction recorded successfully',
        income
      },
      { status: 201 }
    );
  } catch (error: unknown) {
    logger.error('Error recording income transaction', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
