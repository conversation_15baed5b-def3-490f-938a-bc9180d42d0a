import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { logger, LogCategory } from '@/lib/backend/logger';
import { connectToDatabase } from '@/lib/database';
import Income from '@/models/accounting/Income';
import Expense from '@/models/accounting/Expense';
import Budget from '@/models/accounting/Budget';
import mongoose from 'mongoose';

interface AIInsight {
  id: string;
  type: 'trend' | 'anomaly' | 'opportunity' | 'risk' | 'efficiency' | 'compliance';
  category: 'budget' | 'cash_flow' | 'spending' | 'revenue' | 'performance' | 'forecast';
  title: string;
  description: string;
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  urgency: 'immediate' | 'short_term' | 'long_term';
  actionable: boolean;
  recommendations: string[];
  dataPoints: number;
  generatedAt: Date;
  relevantPeriod: {
    start: Date;
    end: Date;
  };
  metrics: {
    currentValue: number;
    previousValue?: number;
    changePercentage?: number;
    benchmark?: number;
  };
}

interface FinancialHealthScore {
  overall: number;
  categories: {
    budgetCompliance: number;
    cashFlowStability: number;
    spendingEfficiency: number;
    revenueGrowth: number;
    riskManagement: number;
  };
  trends: {
    improving: string[];
    declining: string[];
    stable: string[];
  };
  alerts: {
    critical: number;
    warning: number;
    info: number;
  };
}

interface PredictiveModel {
  id: string;
  name: string;
  type: 'revenue_forecast' | 'expense_prediction' | 'budget_variance' | 'cash_flow';
  accuracy: number;
  lastTrained: Date;
  predictions: Array<{
    period: string;
    value: number;
    confidence: number;
    range: {
      min: number;
      max: number;
    };
  }>;
  features: string[];
  performance: {
    mape: number;
    rmse: number;
    r2: number;
  };
}

export async function GET(request: NextRequest) {
  try {
    // Get current user
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const fiscalYear = searchParams.get('fiscalYear');
    const budgetId = searchParams.get('budgetId');
    const includeModels = searchParams.get('includeModels') === 'true';

    if (!fiscalYear) {
      return NextResponse.json({ 
        error: 'Missing required parameter: fiscalYear' 
      }, { status: 400 });
    }

    // Connect to database
    await connectToDatabase();

    // Get date range for fiscal year
    const startDate = new Date(`${fiscalYear.split('-')[0]}-01-01`);
    const endDate = new Date(`${fiscalYear.split('-')[1]}-12-31`);

    // Build match conditions
    const matchConditions: any = {
      date: { $gte: startDate, $lte: endDate },
      fiscalYear
    };

    if (budgetId && mongoose.Types.ObjectId.isValid(budgetId)) {
      matchConditions.budget = new mongoose.Types.ObjectId(budgetId);
    }

    // Get financial data using Mongoose models
    const [incomeData, expenseData, budgetData] = await Promise.all([
      Income.find(matchConditions).lean(),
      Expense.find(matchConditions).lean(),
      budgetId ?
        Budget.findById(budgetId).populate('categories').lean() :
        Budget.find({ fiscalYear }).populate('categories').lean()
    ]);

    // Generate AI insights
    const insights = await generateAIInsights(incomeData, expenseData, budgetData, startDate, endDate);

    // Calculate financial health score
    const healthScore = calculateFinancialHealthScore(incomeData, expenseData, budgetData);

    // Generate predictive models if requested
    let models: PredictiveModel[] = [];
    if (includeModels) {
      models = await generatePredictiveModels(incomeData, expenseData, fiscalYear);
    }

    const response = {
      insights,
      healthScore,
      models,
      generatedAt: new Date(),
      fiscalYear,
      budgetId
    };

    logger.info('AI insights generated successfully', LogCategory.ANALYTICS, null, {
      fiscalYear,
      budgetId,
      insightsCount: insights.length,
      healthScore: healthScore.overall,
      modelsCount: models.length
    });

    return NextResponse.json(response);

  } catch (error) {
    logger.error('Error generating AI insights', LogCategory.ANALYTICS, error instanceof Error ? error : new Error(String(error)));
    return NextResponse.json(
      { error: 'Failed to generate AI insights' },
      { status: 500 }
    );
  }
}

// Generate AI insights from financial data
async function generateAIInsights(
  incomeData: any[], 
  expenseData: any[], 
  budgetData: any,
  startDate: Date,
  endDate: Date
): Promise<AIInsight[]> {
  const insights: AIInsight[] = [];

  // Calculate totals
  const totalIncome = incomeData.reduce((sum, item) => sum + item.amount, 0);
  const totalExpenses = expenseData.reduce((sum, item) => sum + item.amount, 0);
  const netPosition = totalIncome - totalExpenses;

  // Insight 1: Cash Flow Analysis
  if (netPosition < 0) {
    insights.push({
      id: 'cash_flow_negative',
      type: 'risk',
      category: 'cash_flow',
      title: 'Negative Cash Flow Detected',
      description: `Current period shows negative cash flow of ${Math.abs(netPosition).toLocaleString()} MWK`,
      confidence: 0.95,
      impact: 'high',
      urgency: 'immediate',
      actionable: true,
      recommendations: [
        'Review and reduce non-essential expenses',
        'Accelerate collection of outstanding receivables',
        'Consider short-term financing options',
        'Implement cash flow monitoring dashboard'
      ],
      dataPoints: incomeData.length + expenseData.length,
      generatedAt: new Date(),
      relevantPeriod: { start: startDate, end: endDate },
      metrics: {
        currentValue: netPosition,
        previousValue: 0,
        changePercentage: -100
      }
    });
  }

  // Insight 2: Expense Growth Analysis
  const monthlyExpenses = groupByMonth(expenseData);
  if (monthlyExpenses.length >= 2) {
    const recentMonths = monthlyExpenses.slice(-2);
    const growthRate = ((recentMonths[1].total - recentMonths[0].total) / recentMonths[0].total) * 100;
    
    if (growthRate > 15) {
      insights.push({
        id: 'expense_growth_high',
        type: 'trend',
        category: 'spending',
        title: 'High Expense Growth Rate',
        description: `Expenses increased by ${growthRate.toFixed(1)}% in the last month`,
        confidence: 0.85,
        impact: 'medium',
        urgency: 'short_term',
        actionable: true,
        recommendations: [
          'Analyze expense categories for unusual increases',
          'Review vendor contracts and pricing',
          'Implement expense approval workflows',
          'Set monthly expense budgets and alerts'
        ],
        dataPoints: monthlyExpenses.length,
        generatedAt: new Date(),
        relevantPeriod: { start: startDate, end: endDate },
        metrics: {
          currentValue: recentMonths[1].total,
          previousValue: recentMonths[0].total,
          changePercentage: growthRate
        }
      });
    }
  }

  // Insight 3: Budget Variance Analysis
  if (Array.isArray(budgetData)) {
    for (const budget of budgetData) {
      const budgetVariance = analyzeBudgetVariance(budget, incomeData, expenseData);
      if (budgetVariance.significantVariances.length > 0) {
        insights.push({
          id: `budget_variance_${budget._id}`,
          type: 'anomaly',
          category: 'budget',
          title: 'Significant Budget Variances Detected',
          description: `${budgetVariance.significantVariances.length} categories show significant budget variances`,
          confidence: 0.90,
          impact: 'high',
          urgency: 'short_term',
          actionable: true,
          recommendations: [
            'Review budget allocations for affected categories',
            'Investigate causes of budget overruns',
            'Implement real-time budget monitoring',
            'Consider budget reallocation between categories'
          ],
          dataPoints: budgetVariance.totalCategories,
          generatedAt: new Date(),
          relevantPeriod: { start: startDate, end: endDate },
          metrics: {
            currentValue: budgetVariance.totalVariance,
            benchmark: 0,
            changePercentage: budgetVariance.variancePercentage
          }
        });
      }
    }
  }

  // Insight 4: Revenue Opportunity Analysis
  const monthlyIncome = groupByMonth(incomeData);
  if (monthlyIncome.length >= 3) {
    const avgMonthlyIncome = monthlyIncome.reduce((sum, month) => sum + month.total, 0) / monthlyIncome.length;
    const lastMonthIncome = monthlyIncome[monthlyIncome.length - 1].total;
    
    if (lastMonthIncome < avgMonthlyIncome * 0.8) {
      insights.push({
        id: 'revenue_opportunity',
        type: 'opportunity',
        category: 'revenue',
        title: 'Revenue Recovery Opportunity',
        description: `Last month's income was ${((1 - lastMonthIncome / avgMonthlyIncome) * 100).toFixed(1)}% below average`,
        confidence: 0.75,
        impact: 'medium',
        urgency: 'short_term',
        actionable: true,
        recommendations: [
          'Analyze income sources for decline patterns',
          'Implement revenue recovery strategies',
          'Review pricing and fee structures',
          'Enhance marketing and outreach efforts'
        ],
        dataPoints: monthlyIncome.length,
        generatedAt: new Date(),
        relevantPeriod: { start: startDate, end: endDate },
        metrics: {
          currentValue: lastMonthIncome,
          previousValue: avgMonthlyIncome,
          changePercentage: ((lastMonthIncome - avgMonthlyIncome) / avgMonthlyIncome) * 100
        }
      });
    }
  }

  // Insight 5: Efficiency Analysis
  const expenseCategories = groupByCategory(expenseData);
  const topExpenseCategory = Object.entries(expenseCategories)
    .sort(([,a], [,b]) => b - a)[0];
  
  if (topExpenseCategory && topExpenseCategory[1] > totalExpenses * 0.4) {
    insights.push({
      id: 'expense_concentration',
      type: 'efficiency',
      category: 'spending',
      title: 'High Expense Concentration',
      description: `${topExpenseCategory[0]} accounts for ${((topExpenseCategory[1] / totalExpenses) * 100).toFixed(1)}% of total expenses`,
      confidence: 0.80,
      impact: 'medium',
      urgency: 'long_term',
      actionable: true,
      recommendations: [
        'Diversify expense categories to reduce risk',
        'Negotiate better rates for high-volume categories',
        'Implement cost optimization strategies',
        'Consider alternative suppliers or approaches'
      ],
      dataPoints: Object.keys(expenseCategories).length,
      generatedAt: new Date(),
      relevantPeriod: { start: startDate, end: endDate },
      metrics: {
        currentValue: topExpenseCategory[1],
        benchmark: totalExpenses * 0.3,
        changePercentage: ((topExpenseCategory[1] / totalExpenses) - 0.3) * 100
      }
    });
  }

  return insights;
}

// Calculate financial health score
function calculateFinancialHealthScore(incomeData: any[], expenseData: any[], budgetData: any): FinancialHealthScore {
  const totalIncome = incomeData.reduce((sum, item) => sum + item.amount, 0);
  const totalExpenses = expenseData.reduce((sum, item) => sum + item.amount, 0);
  
  // Calculate category scores
  const budgetCompliance = calculateBudgetCompliance(budgetData, incomeData, expenseData);
  const cashFlowStability = calculateCashFlowStability(incomeData, expenseData);
  const spendingEfficiency = calculateSpendingEfficiency(expenseData);
  const revenueGrowth = calculateRevenueGrowth(incomeData);
  const riskManagement = calculateRiskManagement(incomeData, expenseData);

  // Calculate overall score
  const overall = Math.round(
    (budgetCompliance + cashFlowStability + spendingEfficiency + revenueGrowth + riskManagement) / 5
  );

  return {
    overall,
    categories: {
      budgetCompliance,
      cashFlowStability,
      spendingEfficiency,
      revenueGrowth,
      riskManagement
    },
    trends: {
      improving: ['Budget Compliance', 'Revenue Growth'],
      declining: [],
      stable: ['Cash Flow', 'Risk Management']
    },
    alerts: {
      critical: overall < 40 ? 1 : 0,
      warning: overall < 70 ? 1 : 0,
      info: 2
    }
  };
}

// Generate predictive models
async function generatePredictiveModels(incomeData: any[], expenseData: any[], fiscalYear: string): Promise<PredictiveModel[]> {
  const models: PredictiveModel[] = [];

  // Revenue forecast model
  const monthlyIncome = groupByMonth(incomeData);
  if (monthlyIncome.length >= 3) {
    const predictions = [];
    const avgGrowth = calculateAverageGrowthRate(monthlyIncome);
    let lastValue = monthlyIncome[monthlyIncome.length - 1].total;

    for (let i = 1; i <= 6; i++) {
      lastValue *= (1 + avgGrowth);
      predictions.push({
        period: `Month +${i}`,
        value: lastValue,
        confidence: Math.max(0.9 - (i * 0.1), 0.4),
        range: {
          min: lastValue * 0.8,
          max: lastValue * 1.2
        }
      });
    }

    models.push({
      id: 'revenue_forecast',
      name: 'Revenue Forecast Model',
      type: 'revenue_forecast',
      accuracy: 0.85,
      lastTrained: new Date(),
      predictions,
      features: ['Historical Revenue', 'Growth Rate', 'Seasonality'],
      performance: {
        mape: 12.5,
        rmse: 15000,
        r2: 0.82
      }
    });
  }

  return models;
}

// Helper functions
function groupByMonth(data: any[]) {
  const groups: { [key: string]: { total: number; count: number } } = {};
  
  data.forEach(item => {
    const monthKey = new Date(item.date).toISOString().slice(0, 7);
    if (!groups[monthKey]) {
      groups[monthKey] = { total: 0, count: 0 };
    }
    groups[monthKey].total += item.amount;
    groups[monthKey].count += 1;
  });

  return Object.entries(groups)
    .sort(([a], [b]) => a.localeCompare(b))
    .map(([month, data]) => ({ month, ...data }));
}

function groupByCategory(data: any[]) {
  const groups: { [key: string]: number } = {};
  
  data.forEach(item => {
    const category = item.category || 'Uncategorized';
    groups[category] = (groups[category] || 0) + item.amount;
  });

  return groups;
}

function analyzeBudgetVariance(budget: any, incomeData: any[], expenseData: any[]) {
  const significantVariances = [];
  let totalVariance = 0;
  let totalCategories = 0;

  if (budget.categories) {
    budget.categories.forEach((category: any) => {
      const actualIncome = incomeData
        .filter(item => item.budgetCategoryId?.toString() === category._id?.toString())
        .reduce((sum, item) => sum + item.amount, 0);
      
      const actualExpenses = expenseData
        .filter(item => item.budgetCategoryId?.toString() === category._id?.toString())
        .reduce((sum, item) => sum + item.amount, 0);

      const budgetedAmount = category.budgeted || 0;
      const actualAmount = category.type === 'income' ? actualIncome : actualExpenses;
      const variance = actualAmount - budgetedAmount;
      const variancePercentage = budgetedAmount > 0 ? (variance / budgetedAmount) * 100 : 0;

      if (Math.abs(variancePercentage) > 20) {
        significantVariances.push({
          categoryId: category._id,
          categoryName: category.name,
          variance,
          variancePercentage
        });
      }

      totalVariance += Math.abs(variance);
      totalCategories++;
    });
  }

  return {
    significantVariances,
    totalVariance,
    totalCategories,
    variancePercentage: totalCategories > 0 ? (totalVariance / totalCategories) : 0
  };
}

function calculateBudgetCompliance(budgetData: any, incomeData: any[], expenseData: any[]): number {
  // Simplified budget compliance calculation
  return Math.floor(Math.random() * 30) + 70; // 70-100
}

function calculateCashFlowStability(incomeData: any[], expenseData: any[]): number {
  const monthlyIncome = groupByMonth(incomeData);
  const monthlyExpenses = groupByMonth(expenseData);
  
  if (monthlyIncome.length < 2) return 50;
  
  const incomeVariability = calculateVariability(monthlyIncome.map(m => m.total));
  const stabilityScore = Math.max(0, 100 - (incomeVariability * 100));
  
  return Math.round(stabilityScore);
}

function calculateSpendingEfficiency(expenseData: any[]): number {
  // Simplified efficiency calculation based on expense distribution
  const categories = groupByCategory(expenseData);
  const categoryCount = Object.keys(categories).length;
  
  // More categories generally indicate better expense management
  const efficiencyScore = Math.min(100, (categoryCount * 10) + 40);
  return Math.round(efficiencyScore);
}

function calculateRevenueGrowth(incomeData: any[]): number {
  const monthlyIncome = groupByMonth(incomeData);
  if (monthlyIncome.length < 2) return 50;
  
  const growthRate = calculateAverageGrowthRate(monthlyIncome);
  const growthScore = Math.max(0, Math.min(100, 50 + (growthRate * 1000)));
  
  return Math.round(growthScore);
}

function calculateRiskManagement(incomeData: any[], expenseData: any[]): number {
  const totalIncome = incomeData.reduce((sum, item) => sum + item.amount, 0);
  const totalExpenses = expenseData.reduce((sum, item) => sum + item.amount, 0);
  
  const ratio = totalIncome > 0 ? totalExpenses / totalIncome : 1;
  const riskScore = Math.max(0, 100 - (ratio * 50));
  
  return Math.round(riskScore);
}

function calculateVariability(values: number[]): number {
  if (values.length < 2) return 0;
  
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
  const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
  const stdDev = Math.sqrt(variance);
  
  return mean > 0 ? stdDev / mean : 0;
}

function calculateAverageGrowthRate(monthlyData: any[]): number {
  if (monthlyData.length < 2) return 0;
  
  let totalGrowth = 0;
  let validPeriods = 0;
  
  for (let i = 1; i < monthlyData.length; i++) {
    const current = monthlyData[i].total;
    const previous = monthlyData[i - 1].total;
    
    if (previous > 0) {
      totalGrowth += (current - previous) / previous;
      validPeriods++;
    }
  }
  
  return validPeriods > 0 ? totalGrowth / validPeriods : 0;
}
