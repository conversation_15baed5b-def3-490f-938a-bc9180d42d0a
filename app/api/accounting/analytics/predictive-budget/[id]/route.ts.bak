// app/api/accounting/analytics/predictive-budget/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '../../../../../../lib/backend/auth/auth';
import { logger, LogCategory } from '../../../../../../lib/backend/utils/logger';
import { connectToDatabase } from '../../../../../../lib/backend/database';
import { ObjectId } from 'mongodb';
import mongoose from 'mongoose';

interface PredictiveAnalytics {
  budgetId: string;
  budgetName: string;
  fiscalYear: string;
  predictions: {
    nextMonth: {
      income: number;
      expenses: number;
      variance: number;
      confidence: number;
    };
    nextQuarter: {
      income: number;
      expenses: number;
      variance: number;
      confidence: number;
    };
    endOfYear: {
      income: number;
      expenses: number;
      variance: number;
      confidence: number;
    };
  };
  trends: {
    incomeGrowth: number;
    expenseGrowth: number;
    seasonality: 'high' | 'medium' | 'low';
    volatility: 'high' | 'medium' | 'low';
  };
  riskFactors: Array<{
    id: string;
    type: 'budget_overrun' | 'revenue_shortfall' | 'seasonal_impact' | 'external_factor';
    severity: 'high' | 'medium' | 'low';
    probability: number;
    impact: number;
    description: string;
    mitigation: string;
  }>;
  opportunities: Array<{
    id: string;
    type: 'cost_savings' | 'revenue_increase' | 'efficiency_gain' | 'optimization';
    potential: number;
    effort: 'high' | 'medium' | 'low';
    timeframe: 'immediate' | 'short_term' | 'long_term';
    description: string;
    actionItems: string[];
  }>;
  aiInsights: {
    summary: string;
    keyFindings: string[];
    recommendations: Array<{
      priority: 'high' | 'medium' | 'low';
      category: 'budget' | 'process' | 'strategy' | 'risk';
      title: string;
      description: string;
      expectedImpact: number;
    }>;
  };
  lastUpdated: Date;
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get current user
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get budget ID from params
    const { id: budgetId } = await params;
    
    if (!budgetId) {
      return NextResponse.json({ error: 'Budget ID is required' }, { status: 400 });
    }

    // Validate ObjectId
    if (!ObjectId.isValid(budgetId)) {
      return NextResponse.json({ error: 'Invalid budget ID' }, { status: 400 });
    }

    // Connect to database
    await connectToDatabase();
    const db = mongoose.connection.db;

    if (!db) {
      return NextResponse.json({ error: 'Database connection failed' }, { status: 500 });
    }

    // Get budget details
    const budget = await db.collection('budgets').findOne({
      _id: new ObjectId(budgetId)
    });

    if (!budget) {
      return NextResponse.json({ error: 'Budget not found' }, { status: 404 });
    }

    // Get historical data for predictions
    const currentDate = new Date();
    const startOfYear = new Date(currentDate.getFullYear(), 0, 1);
    const endOfYear = new Date(currentDate.getFullYear(), 11, 31);

    // Get income and expense data for the current fiscal year
    const [incomeData, expenseData] = await Promise.all([
      db.collection('incomes').aggregate([
        {
          $match: {
            budget: new ObjectId(budgetId),
            date: { $gte: startOfYear, $lte: endOfYear }
          }
        },
        {
          $group: {
            _id: {
              year: { $year: '$date' },
              month: { $month: '$date' }
            },
            totalAmount: { $sum: '$amount' },
            count: { $sum: 1 }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1 } }
      ]).toArray(),

      db.collection('expenses').aggregate([
        {
          $match: {
            budget: new ObjectId(budgetId),
            date: { $gte: startOfYear, $lte: endOfYear }
          }
        },
        {
          $group: {
            _id: {
              year: { $year: '$date' },
              month: { $month: '$date' }
            },
            totalAmount: { $sum: '$amount' },
            count: { $sum: 1 }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1 } }
      ]).toArray()
    ]);

    // Calculate current totals
    const currentIncome = incomeData.reduce((sum: number, item: any) => sum + (item.totalAmount || 0), 0);
    const currentExpenses = expenseData.reduce((sum: number, item: any) => sum + (item.totalAmount || 0), 0);

    // AI-powered predictions (simplified algorithm - in production, use ML models)
    const monthsElapsed = currentDate.getMonth() + 1;
    const monthsRemaining = 12 - monthsElapsed;

    // Calculate growth rates
    const incomeGrowthRate = incomeData.length > 1 ? 
      ((incomeData[incomeData.length - 1]?.totalAmount || 0) - (incomeData[0]?.totalAmount || 0)) / (incomeData[0]?.totalAmount || 1) : 0.05;
    const expenseGrowthRate = expenseData.length > 1 ? 
      ((expenseData[expenseData.length - 1]?.totalAmount || 0) - (expenseData[0]?.totalAmount || 0)) / (expenseData[0]?.totalAmount || 1) : 0.03;

    // Predict next month
    const avgMonthlyIncome = currentIncome / monthsElapsed;
    const avgMonthlyExpenses = currentExpenses / monthsElapsed;

    const nextMonthIncome = avgMonthlyIncome * (1 + incomeGrowthRate);
    const nextMonthExpenses = avgMonthlyExpenses * (1 + expenseGrowthRate);

    // Predict next quarter (3 months)
    const nextQuarterIncome = nextMonthIncome * 3 * (1 + incomeGrowthRate * 0.5);
    const nextQuarterExpenses = nextMonthExpenses * 3 * (1 + expenseGrowthRate * 0.5);

    // Predict end of year
    const endOfYearIncome = currentIncome + (avgMonthlyIncome * monthsRemaining * (1 + incomeGrowthRate));
    const endOfYearExpenses = currentExpenses + (avgMonthlyExpenses * monthsRemaining * (1 + expenseGrowthRate));

    // Calculate volatility and seasonality
    const incomeVolatility = incomeData.length > 1 ?
      Math.sqrt(incomeData.reduce((sum: number, item: any, index: number) => {
        if (index === 0) return 0;
        const currentAmount = item.totalAmount || 0;
        const previousAmount = incomeData[index - 1]?.totalAmount || 0;
        const diff = currentAmount - previousAmount;
        return sum + (diff * diff);
      }, 0) / (incomeData.length - 1)) / (avgMonthlyIncome || 1) : 0;

    const volatilityLevel = incomeVolatility > 0.3 ? 'high' : incomeVolatility > 0.15 ? 'medium' : 'low';
    const seasonalityLevel = incomeData.length >= 6 ? 'medium' : 'low'; // Simplified seasonality detection

    // Generate risk factors
    const riskFactors = [];
    
    if (nextMonthExpenses > nextMonthIncome) {
      riskFactors.push({
        id: 'budget_overrun_1',
        type: 'budget_overrun' as const,
        severity: 'high' as const,
        probability: 0.8,
        impact: Math.abs(nextMonthExpenses - nextMonthIncome),
        description: 'Projected expenses exceed income for next month',
        mitigation: 'Review and reduce non-essential expenses, or identify additional revenue sources'
      });
    }

    if (incomeGrowthRate < 0) {
      riskFactors.push({
        id: 'revenue_shortfall_1',
        type: 'revenue_shortfall' as const,
        severity: 'medium' as const,
        probability: 0.6,
        impact: Math.abs(incomeGrowthRate * currentIncome),
        description: 'Income growth rate is negative, indicating declining revenue',
        mitigation: 'Implement revenue enhancement strategies and review income sources'
      });
    }

    // Generate opportunities
    const opportunities = [];
    
    if (expenseGrowthRate > incomeGrowthRate * 1.5) {
      opportunities.push({
        id: 'cost_optimization_1',
        type: 'cost_savings' as const,
        potential: currentExpenses * 0.1, // 10% potential savings
        effort: 'medium' as const,
        timeframe: 'short_term' as const,
        description: 'Expense growth is outpacing income growth - optimization opportunity',
        actionItems: [
          'Conduct expense audit and identify cost reduction areas',
          'Negotiate better rates with suppliers',
          'Implement cost control measures'
        ]
      });
    }

    if (incomeGrowthRate > 0.1) {
      opportunities.push({
        id: 'revenue_expansion_1',
        type: 'revenue_increase' as const,
        potential: currentIncome * 0.15, // 15% potential increase
        effort: 'low' as const,
        timeframe: 'immediate' as const,
        description: 'Strong income growth trend presents expansion opportunity',
        actionItems: [
          'Scale successful revenue streams',
          'Explore new income sources',
          'Optimize pricing strategies'
        ]
      });
    }

    // Generate AI insights
    const aiInsights = {
      summary: `Based on ${incomeData.length + expenseData.length} data points, the budget shows ${incomeGrowthRate > 0 ? 'positive' : 'negative'} income growth of ${(incomeGrowthRate * 100).toFixed(1)}% and expense growth of ${(expenseGrowthRate * 100).toFixed(1)}%. ${volatilityLevel === 'high' ? 'High volatility detected - recommend closer monitoring.' : 'Financial patterns are relatively stable.'}`,
      keyFindings: [
        `Current income-to-expense ratio: ${(currentIncome / (currentExpenses || 1)).toFixed(2)}`,
        `Projected end-of-year variance: ${formatCurrency(endOfYearIncome - endOfYearExpenses)}`,
        `Volatility level: ${volatilityLevel} (${(incomeVolatility * 100).toFixed(1)}%)`,
        `${riskFactors.length} risk factors and ${opportunities.length} opportunities identified`
      ],
      recommendations: [
        {
          priority: 'high' as const,
          category: 'budget' as const,
          title: 'Monthly Budget Review',
          description: 'Implement monthly budget reviews to track variance and adjust projections',
          expectedImpact: currentIncome * 0.05
        },
        {
          priority: 'medium' as const,
          category: 'process' as const,
          title: 'Automated Alerts',
          description: 'Set up automated alerts for budget threshold breaches',
          expectedImpact: currentExpenses * 0.02
        }
      ]
    };

    // Helper function to format currency (simplified)
    function formatCurrency(amount: number): string {
      return `MWK ${Math.round(amount).toLocaleString()}`;
    }

    const analytics: PredictiveAnalytics = {
      budgetId,
      budgetName: budget.name,
      fiscalYear: budget.fiscalYear,
      predictions: {
        nextMonth: {
          income: nextMonthIncome,
          expenses: nextMonthExpenses,
          variance: nextMonthIncome - nextMonthExpenses,
          confidence: 0.85
        },
        nextQuarter: {
          income: nextQuarterIncome,
          expenses: nextQuarterExpenses,
          variance: nextQuarterIncome - nextQuarterExpenses,
          confidence: 0.75
        },
        endOfYear: {
          income: endOfYearIncome,
          expenses: endOfYearExpenses,
          variance: endOfYearIncome - endOfYearExpenses,
          confidence: 0.65
        }
      },
      trends: {
        incomeGrowth: incomeGrowthRate,
        expenseGrowth: expenseGrowthRate,
        seasonality: seasonalityLevel,
        volatility: volatilityLevel
      },
      riskFactors,
      opportunities,
      aiInsights,
      lastUpdated: new Date()
    };

    logger.info('Predictive budget analytics generated successfully', LogCategory.ANALYTICS, {
      budgetId,
      budgetName: budget.name,
      dataPoints: incomeData.length + expenseData.length,
      riskFactors: riskFactors.length,
      opportunities: opportunities.length
    });

    return NextResponse.json(analytics);

  } catch (error) {
    logger.error('Error generating predictive budget analytics', LogCategory.ANALYTICS, error);
    return NextResponse.json(
      { error: 'Failed to generate predictive analytics' },
      { status: 500 }
    );
  }
}
