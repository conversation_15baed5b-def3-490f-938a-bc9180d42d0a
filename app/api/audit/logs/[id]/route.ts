import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { auditService } from '@/lib/services/payroll/audit-service';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * GET /api/audit/logs/[id] - Get specific audit log by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only admin and finance roles can view audit logs
    const allowedRoles = [
      'Super Admin',
      'System Admin',
      'Finance Director',
      'Finance Manager',
      'HR Director',
      'Audit Manager'
    ];

    if (!allowedRoles.includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view audit logs' },
        { status: 403 }
      );
    }

    const { id } = await params;

    // Validate ID format
    if (!id || id.length !== 24) {
      return NextResponse.json(
        { error: 'Invalid audit log ID format' },
        { status: 400 }
      );
    }

    // Get audit log
    const auditLog = await auditService.getAuditLogById(id);

    if (!auditLog) {
      return NextResponse.json(
        { error: 'Audit log not found' },
        { status: 404 }
      );
    }

    logger.info('Audit log retrieved by ID', LogCategory.AUDIT, {
      auditLogId: id,
      userId: user._id?.toString(),
      viewedBy: user.email,
    });

    return NextResponse.json({
      success: true,
      data: auditLog,
    });

  } catch (error) {
    logger.error('Error retrieving audit log by ID', LogCategory.AUDIT, error);
    return NextResponse.json(
      { error: 'Failed to retrieve audit log' },
      { status: 500 }
    );
  }
}
