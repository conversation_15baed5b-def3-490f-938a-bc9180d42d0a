import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { Employee } from '@/models/Employee';

/**
 * GET /api/debug/employees
 * Debug endpoint to check employee data
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get employee count
    const totalEmployees = await Employee.countDocuments();
    
    // Get sample employees
    const sampleEmployees = await Employee.find()
      .populate('departmentId', 'name code')
      .select('firstName lastName email employeeId position status hireDate')
      .limit(5)
      .lean();

    // Get employees by status
    const activeEmployees = await Employee.countDocuments({ status: 'active' });
    const inactiveEmployees = await Employee.countDocuments({ status: 'inactive' });

    // Get employees by department
    const employeesByDept = await Employee.aggregate([
      {
        $lookup: {
          from: 'departments',
          localField: 'departmentId',
          foreignField: '_id',
          as: 'department'
        }
      },
      {
        $group: {
          _id: '$departmentId',
          count: { $sum: 1 },
          departmentName: { $first: '$department.name' }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    const result = {
      success: true,
      summary: {
        totalEmployees,
        activeEmployees,
        inactiveEmployees,
        departmentCount: employeesByDept.length
      },
      sampleEmployees: sampleEmployees.map(emp => ({
        id: emp._id,
        name: `${emp.firstName} ${emp.lastName}`,
        email: emp.email,
        employeeId: emp.employeeId,
        position: emp.position,
        status: emp.status,
        department: emp.departmentId ? (emp.departmentId as any).name : 'No Department',
        hireDate: emp.hireDate
      })),
      departmentBreakdown: employeesByDept,
      timestamp: new Date().toISOString()
    };

    logger.info('Employee debug check completed', LogCategory.DEBUG, {
      userId: user.id,
      totalEmployees,
      activeEmployees
    });

    return NextResponse.json(result);

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Employee debug check failed';
    
    logger.error('Employee debug check failed', LogCategory.DEBUG, {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined
    });

    return NextResponse.json({
      success: false,
      error: errorMessage,
      summary: {
        totalEmployees: 0,
        activeEmployees: 0,
        inactiveEmployees: 0,
        departmentCount: 0
      },
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
