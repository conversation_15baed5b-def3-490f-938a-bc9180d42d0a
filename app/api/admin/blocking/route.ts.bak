// app/api/admin/blocking/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { blockingService } from '@/lib/backend/services/auth/BlockingService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { UserRole } from '@/types/user-roles';
import { BlockedEntityType } from '@/types/blocked-entity';
import mongoose from 'mongoose';

/**
 * GET handler for blocked entities
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  logger.info('Admin blocking API request received', LogCategory.API, {
    path: req.nextUrl.pathname,
    method: req.method
  });

  try {
    // Get current user
    const user = await getCurrentUser(req);
    if (!user) {
      logger.warn('Admin blocking API: User not authenticated', LogCategory.API);
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is super_admin
    if (user.role !== UserRole.SUPER_ADMIN) {
      logger.warn('Admin blocking API: Unauthorized access attempt', LogCategory.API, {
        userId: (user._id as mongoose.Types.ObjectId).toString(),
        role: user.role
      });
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const limit = parseInt(url.searchParams.get('limit') || '100');
    const skip = parseInt(url.searchParams.get('skip') || '0');
    const search = url.searchParams.get('search') || undefined;
    const typeParam = url.searchParams.get('type');
    const isActiveParam = url.searchParams.get('isActive');

    // Parse type filter
    let typeFilter: BlockedEntityType[] | undefined;
    if (typeParam) {
      typeFilter = typeParam.split(',').map(t => t.trim() as BlockedEntityType);
    }

    // Parse isActive filter
    let isActiveFilter: boolean | undefined;
    if (isActiveParam !== null) {
      isActiveFilter = isActiveParam === 'true';
    }

    // Get all blocked entities
    logger.debug('Getting all blocked entities', LogCategory.API, {
      limit, skip, search, typeFilter, isActiveFilter
    });

    const { entities, total } = await blockingService.getAllBlockedEntities(limit, skip, {
      type: typeFilter,
      isActive: isActiveFilter,
      search
    });

    // Return entities
    logger.info(`Retrieved ${entities.length} blocked entities`, LogCategory.API);
    return NextResponse.json({
      status: 'success',
      data: {
        entities,
        total,
        limit,
        skip
      }
    });
  } catch (error: unknown) {
    logger.error('Admin blocking API error', LogCategory.API,
      error instanceof Error ? error : new Error(typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : 'Unknown error'),
      { path: req.nextUrl.pathname }
    );

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred while retrieving blocked entities' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating or updating blocked entities
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  logger.info('Admin blocking create/update API request received', LogCategory.API, {
    path: req.nextUrl.pathname,
    method: req.method
  });

  try {
    // Get current user
    const user = await getCurrentUser(req);
    if (!user) {
      logger.warn('Admin blocking create/update API: User not authenticated', LogCategory.API);
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is super_admin
    if (user.role !== UserRole.SUPER_ADMIN) {
      logger.warn('Admin blocking create/update API: Unauthorized access attempt', LogCategory.API, {
        userId: (user._id as mongoose.Types.ObjectId).toString(),
        role: user.role
      });
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await req.json();
    const {
      type,
      value,
      reason,
      description,
      expiresAt
    } = body;

    // Validate input
    if (!type || !value || !reason) {
      logger.warn('Admin blocking create/update API: Invalid input', LogCategory.API, {
        hasType: !!type,
        hasValue: !!value,
        hasReason: !!reason
      });
      return NextResponse.json(
        { error: 'Please provide type, value, and reason' },
        { status: 400 }
      );
    }

    // Parse expiration date if provided
    let parsedExpiresAt: Date | undefined;
    if (expiresAt) {
      parsedExpiresAt = new Date(expiresAt);
      if (isNaN(parsedExpiresAt.getTime())) {
        return NextResponse.json(
          { error: 'Invalid expiration date' },
          { status: 400 }
        );
      }
    }

    // Create or update blocked entity based on type
    let entity;
    switch (type) {
      case BlockedEntityType.IP_ADDRESS:
        entity = await blockingService.blockIpAddress(
          value,
          reason,
          description || 'Blocked by administrator',
          (user._id as mongoose.Types.ObjectId).toString(),
          parsedExpiresAt
        );
        break;
      case BlockedEntityType.IP_RANGE:
        entity = await blockingService.blockIpRange(
          value,
          reason,
          description || 'Blocked by administrator',
          (user._id as mongoose.Types.ObjectId).toString(),
          parsedExpiresAt
        );
        break;
      case BlockedEntityType.DEVICE:
        entity = await blockingService.blockDevice(
          value,
          reason,
          description || 'Blocked by administrator',
          (user._id as mongoose.Types.ObjectId).toString(),
          parsedExpiresAt
        );
        break;
      case BlockedEntityType.COUNTRY:
        entity = await blockingService.blockCountry(
          value,
          reason,
          description || 'Blocked by administrator',
          (user._id as mongoose.Types.ObjectId).toString(),
          parsedExpiresAt
        );
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid entity type' },
          { status: 400 }
        );
    }

    // Return success response
    logger.info(`Entity of type ${type} blocked successfully`, LogCategory.API, {
      entityId: (entity._id as mongoose.Types.ObjectId).toString(),
      type,
      value
    });
    return NextResponse.json({
      status: 'success',
      message: `${type} blocked successfully`,
      data: {
        entity
      }
    });
  } catch (error: unknown) {
    logger.error('Admin blocking create/update API error', LogCategory.API,
      error instanceof Error ? error : new Error(typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : 'Unknown error'),
      { path: req.nextUrl.pathname }
    );

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred while blocking entity' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for removing blocked entities
 */
export async function DELETE(req: NextRequest): Promise<NextResponse> {
  logger.info('Admin blocking delete API request received', LogCategory.API, {
    path: req.nextUrl.pathname,
    method: req.method
  });

  try {
    // Get current user
    const user = await getCurrentUser(req);
    if (!user) {
      logger.warn('Admin blocking delete API: User not authenticated', LogCategory.API);
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is super_admin
    if (user.role !== UserRole.SUPER_ADMIN) {
      logger.warn('Admin blocking delete API: Unauthorized access attempt', LogCategory.API, {
        userId: (user._id as mongoose.Types.ObjectId).toString(),
        role: user.role
      });
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await req.json();
    const { id } = body;

    // Validate input
    if (!id) {
      logger.warn('Admin blocking delete API: Missing entity ID', LogCategory.API);
      return NextResponse.json(
        { error: 'Please provide entity ID' },
        { status: 400 }
      );
    }

    // Deactivate the blocked entity
    const entity = await blockingService.unblockEntity(id, (user._id as mongoose.Types.ObjectId).toString());
    if (!entity) {
      logger.warn('Admin blocking delete API: Entity not found', LogCategory.API, { id });
      return NextResponse.json(
        { error: 'Entity not found' },
        { status: 404 }
      );
    }

    // Return success response
    logger.info('Blocked entity deactivated successfully', LogCategory.API, { id });
    return NextResponse.json({
      status: 'success',
      message: 'Entity unblocked successfully'
    });
  } catch (error: unknown) {
    logger.error('Admin blocking delete API error', LogCategory.API,
      error instanceof Error ? error : new Error(typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : 'Unknown error'),
      { path: req.nextUrl.pathname }
    );

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred while unblocking entity' },
      { status: 500 }
    );
  }
}
