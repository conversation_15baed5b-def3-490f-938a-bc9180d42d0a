import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';

/**
 * POST /api/admin/test-auth
 * Test authentication and permissions
 */
export async function POST(req: NextRequest) {
  try {
    console.log('🔍 Testing authentication...');
    
    // Check authentication
    const user = await getCurrentUser(req);
    console.log('👤 User:', user ? { id: user.id, email: user.email, role: user.role } : 'null');
    
    if (!user) {
      return NextResponse.json({ 
        error: 'Unauthorized',
        debug: 'No user found from getCurrentUser'
      }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ]);

    console.log('🔐 Has permission:', hasPermission);
    console.log('👤 User role:', user.role);
    console.log('✅ Required roles:', [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN]);

    if (!hasPermission) {
      return NextResponse.json({
        error: 'Forbidden: Only system administrators can access this endpoint',
        debug: {
          userRole: user.role,
          requiredRoles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN],
          hasPermission
        }
      }, { status: 403 });
    }

    return NextResponse.json({
      success: true,
      message: 'Authentication and permissions test passed',
      data: {
        user: {
          id: user.id,
          email: user.email,
          role: user.role
        },
        hasPermission,
        requiredRoles: [UserRole.SUPER_ADMIN, UserRole.SYSTEM_ADMIN]
      }
    });

  } catch (error: unknown) {
    console.error('❌ Auth test error:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'An error occurred during auth test',
      debug: {
        errorType: error?.constructor?.name,
        stack: error instanceof Error ? error.stack : undefined
      }
    }, { status: 500 });
  }
}
