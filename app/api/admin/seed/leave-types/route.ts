import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { seedLeaveTypes } from '@/lib/backend/seeders/leave-types-seeder';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * POST /api/admin/seed/leave-types
 * Seed default leave types into the database
 */
export async function POST(req: NextRequest) {
  try {
    console.log('🌱 Starting leave types seeding...');

    // Check authentication
    const user = await getCurrentUser(req);
    console.log('👤 User:', user ? { id: user.id, email: user.email, role: user.role } : 'null');

    if (!user) {
      console.log('❌ No user found');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only system admins can seed data
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ]);

    console.log('🔐 Has permission:', hasPermission);

    if (!hasPermission) {
      console.log('❌ Permission denied');
      return NextResponse.json(
        { error: 'Forbidden: Only system administrators can seed leave types' },
        { status: 403 }
      );
    }

    // Run the seeder
    console.log('🚀 Running seeder...');
    const result = await seedLeaveTypes();
    console.log('✅ Seeder completed:', result);

    logger.info('Leave types seeded successfully', LogCategory.SYSTEM, {
      seededBy: user.id,
      created: result.created,
      skipped: result.skipped,
      total: result.total
    });

    let message = '';
    if (result.created === 0) {
      message = `All ${result.total} default leave types already exist. No new types were created.`;
    } else if (result.skipped === 0) {
      message = `Successfully created all ${result.created} default leave types.`;
    } else {
      message = `Successfully created ${result.created} new leave types. ${result.skipped} types already existed.`;
    }

    return NextResponse.json({
      success: true,
      message: message,
      data: result
    });

  } catch (error: unknown) {
    logger.error('Error seeding leave types', LogCategory.SYSTEM, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred while seeding leave types' },
      { status: 500 }
    );
  }
}
