import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { seedLeaveTypes } from '@/lib/backend/seeders/leave-types-seeder';
import logger, { LogCategory } from '@/lib/backend/utils/logger';

/**
 * POST /api/admin/seed/leave-types
 * Seed default leave types into the database
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only system admins can seed data
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Only system administrators can seed leave types' },
        { status: 403 }
      );
    }

    // Run the seeder
    await seedLeaveTypes();

    logger.info('Leave types seeded successfully', LogCategory.SYSTEM, { 
      seededBy: user.id 
    });

    return NextResponse.json({
      success: true,
      message: 'Leave types seeded successfully'
    });

  } catch (error: unknown) {
    logger.error('Error seeding leave types', LogCategory.SYSTEM, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred while seeding leave types' },
      { status: 500 }
    );
  }
}
