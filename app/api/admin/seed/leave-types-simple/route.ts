import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { connectToDatabase } from '@/lib/backend/database';
import LeaveType from '@/models/leave/LeaveType';
import User from '@/models/User';

/**
 * POST /api/admin/seed/leave-types-simple
 * Simple seed endpoint for debugging
 */
export async function POST(req: NextRequest) {
  try {
    console.log('🌱 Starting simple seed...');
    
    // Check authentication
    const user = await getCurrentUser(req);
    console.log('👤 User:', user ? { id: user.id, email: user.email, role: user.role } : 'null');
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ]);

    console.log('🔐 Has permission:', hasPermission);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Only system administrators can seed leave types' },
        { status: 403 }
      );
    }

    // Connect to database
    console.log('🔌 Connecting to database...');
    await connectToDatabase();
    console.log('✅ Database connected');

    // Check existing leave types
    const existingCount = await LeaveType.countDocuments();
    console.log('📊 Existing leave types:', existingCount);

    // Find system admin
    const systemAdmin = await User.findOne({ 
      role: { $in: ['SUPER_ADMIN', 'SYSTEM_ADMIN'] } 
    });
    console.log('👨‍💼 System admin found:', systemAdmin ? systemAdmin.email : 'none');

    if (!systemAdmin) {
      return NextResponse.json(
        { error: 'No system admin user found' },
        { status: 500 }
      );
    }

    // Create a simple leave type
    const testLeaveType = {
      name: 'Test Annual Leave',
      code: 'TEST_ANNUAL',
      description: 'Test leave type created by simple seeder',
      defaultDays: 20,
      isPaid: true,
      requiresApproval: true,
      maxConsecutiveDays: 10,
      minNoticeInDays: 3,
      allowCarryOver: true,
      maxCarryOverDays: 5,
      color: '#3B82F6',
      isActive: true,
      createdBy: systemAdmin._id
    };

    // Check if it already exists
    const existing = await LeaveType.findOne({ code: testLeaveType.code });
    if (existing) {
      console.log('⚠️ Test leave type already exists');
      return NextResponse.json({
        success: true,
        message: 'Test leave type already exists',
        data: { created: 0, skipped: 1, total: 1 }
      });
    }

    // Create the leave type
    console.log('🆕 Creating test leave type...');
    const leaveType = new LeaveType(testLeaveType);
    await leaveType.save();
    console.log('✅ Test leave type created:', leaveType._id);

    return NextResponse.json({
      success: true,
      message: 'Test leave type created successfully',
      data: { 
        created: 1, 
        skipped: 0, 
        total: 1,
        leaveType: {
          id: leaveType._id,
          name: leaveType.name,
          code: leaveType.code
        }
      }
    });

  } catch (error: unknown) {
    console.error('❌ Simple seed error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred',
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}
