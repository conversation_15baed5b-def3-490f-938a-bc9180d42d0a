import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { Employee } from '@/models/Employee';
import Department from '@/models/Department';

/**
 * POST /api/admin/seed/employees
 * Seed the database with sample employees for testing
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only super admin and system admin can seed
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Super Admin access required' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Check if employees already exist
    const existingEmployees = await Employee.countDocuments();
    if (existingEmployees > 0) {
      return NextResponse.json({
        success: true,
        message: `Employees already exist (${existingEmployees} found). Skipping seeding.`,
        data: {
          existing: existingEmployees,
          created: 0
        }
      });
    }

    // Get or create a default department
    let defaultDepartment = await Department.findOne({ code: 'HR' });
    if (!defaultDepartment) {
      defaultDepartment = await Department.create({
        name: 'Human Resources',
        code: 'HR',
        description: 'Human Resources Department',
        isActive: true,
        createdBy: user.id,
        updatedBy: user.id
      });
    }

    // Sample employees data
    const sampleEmployees = [
      {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        employeeId: 'EMP001',
        position: 'Software Developer',
        departmentId: defaultDepartment._id,
        hireDate: new Date('2023-01-15'),
        status: 'active',
        phone: '+265 991 234 567',
        address: 'Blantyre, Malawi'
      },
      {
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        employeeId: 'EMP002',
        position: 'HR Manager',
        departmentId: defaultDepartment._id,
        hireDate: new Date('2022-06-01'),
        status: 'active',
        phone: '+265 991 234 568',
        address: 'Lilongwe, Malawi'
      },
      {
        firstName: 'Michael',
        lastName: 'Johnson',
        email: '<EMAIL>',
        employeeId: 'EMP003',
        position: 'Project Manager',
        departmentId: defaultDepartment._id,
        hireDate: new Date('2023-03-10'),
        status: 'active',
        phone: '+265 991 234 569',
        address: 'Mzuzu, Malawi'
      },
      {
        firstName: 'Sarah',
        lastName: 'Williams',
        email: '<EMAIL>',
        employeeId: 'EMP004',
        position: 'Business Analyst',
        departmentId: defaultDepartment._id,
        hireDate: new Date('2023-05-20'),
        status: 'active',
        phone: '+265 991 234 570',
        address: 'Blantyre, Malawi'
      },
      {
        firstName: 'David',
        lastName: 'Brown',
        email: '<EMAIL>',
        employeeId: 'EMP005',
        position: 'QA Engineer',
        departmentId: defaultDepartment._id,
        hireDate: new Date('2023-08-01'),
        status: 'active',
        phone: '+265 991 234 571',
        address: 'Zomba, Malawi'
      }
    ];

    // Create employees
    const createdEmployees = [];
    for (const empData of sampleEmployees) {
      const employee = new Employee({
        ...empData,
        createdBy: user.id,
        updatedBy: user.id
      });
      await employee.save();
      createdEmployees.push(employee);
    }

    logger.info('Sample employees seeded successfully', LogCategory.ADMIN, {
      userId: user.id,
      employeesCreated: createdEmployees.length,
      departmentId: defaultDepartment._id
    });

    return NextResponse.json({
      success: true,
      message: `Successfully created ${createdEmployees.length} sample employees`,
      data: {
        existing: 0,
        created: createdEmployees.length,
        employees: createdEmployees.map(emp => ({
          id: emp._id,
          name: `${emp.firstName} ${emp.lastName}`,
          employeeId: emp.employeeId,
          position: emp.position,
          email: emp.email
        })),
        department: {
          id: defaultDepartment._id,
          name: defaultDepartment.name,
          code: defaultDepartment.code
        }
      }
    }, { status: 201 });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Employee seeding failed';
    
    logger.error('Error seeding employees', LogCategory.ADMIN, {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined
    });

    return NextResponse.json({
      success: false,
      error: errorMessage,
      message: 'Failed to seed sample employees'
    }, { status: 500 });
  }
}
