import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { Employee } from '@/models/Employee';

/**
 * GET /api/employees
 * Get all employees with pagination and filtering
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD,
      UserRole.PAYROLL_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const department = searchParams.get('department') || '';
    const status = searchParams.get('status') || '';
    const position = searchParams.get('position') || '';

    // Build query
    const query: any = {};

    // Search filter
    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { employeeId: { $regex: search, $options: 'i' } }
      ];
    }

    // Department filter
    if (department) {
      query.departmentId = department;
    }

    // Status filter
    if (status) {
      query.status = status;
    }

    // Position filter
    if (position) {
      query.position = { $regex: position, $options: 'i' };
    }

    // Get total count
    const total = await Employee.countDocuments(query);

    // Get employees with pagination
    const employees = await Employee.find(query)
      .populate('departmentId', 'name code')
      .select('firstName lastName email employeeId position status avatar departmentId hireDate')
      .sort({ lastName: 1, firstName: 1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean();

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    const pagination = {
      page,
      limit,
      total,
      totalPages,
      hasNextPage,
      hasPrevPage
    };

    logger.info('Employees retrieved successfully', LogCategory.HR, {
      userId: user.id,
      page,
      limit,
      total,
      search,
      department,
      status
    });

    return NextResponse.json({
      success: true,
      data: employees,
      pagination
    });

  } catch (error: unknown) {
    logger.error('Error getting employees', LogCategory.HR, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/employees
 * Create a new employee
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get request body
    const body = await req.json();

    // Create employee
    const employee = new Employee({
      ...body,
      createdBy: user.id,
      updatedBy: user.id
    });

    await employee.save();

    // Populate department info
    await employee.populate('departmentId', 'name code');

    logger.info('Employee created successfully', LogCategory.HR, {
      employeeId: employee._id,
      createdBy: user.id,
      employeeNumber: employee.employeeId
    });

    return NextResponse.json({
      success: true,
      message: 'Employee created successfully',
      data: employee
    }, { status: 201 });

  } catch (error: unknown) {
    logger.error('Error creating employee', LogCategory.HR, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
