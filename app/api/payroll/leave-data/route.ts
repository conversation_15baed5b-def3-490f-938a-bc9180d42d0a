import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/backend/database';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { payrollIntegrationService } from '@/services/payroll/PayrollIntegrationService';

/**
 * GET /api/payroll/leave-data
 * Generate payroll data for leave deductions and encashments
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only HR and Payroll can access payroll data
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.PAYROLL_MANAGER,
      UserRole.FINANCE_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions to access payroll data' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const periodStartParam = searchParams.get('periodStart');
    const periodEndParam = searchParams.get('periodEnd');
    const format = searchParams.get('format'); // 'json' or 'csv'

    // Validate dates
    if (!periodStartParam || !periodEndParam) {
      return NextResponse.json(
        { error: 'Missing required parameters: periodStart, periodEnd' },
        { status: 400 }
      );
    }

    const periodStart = new Date(periodStartParam);
    const periodEnd = new Date(periodEndParam);

    if (isNaN(periodStart.getTime()) || isNaN(periodEnd.getTime())) {
      return NextResponse.json(
        { error: 'Invalid date format. Use YYYY-MM-DD format' },
        { status: 400 }
      );
    }

    if (periodStart > periodEnd) {
      return NextResponse.json(
        { error: 'Period start date must be before end date' },
        { status: 400 }
      );
    }

    // Generate payroll data
    const payrollData = await payrollIntegrationService.generatePayrollData(periodStart, periodEnd);

    // Handle CSV export
    if (format === 'csv') {
      const csvContent = await this.generatePayrollCSV(payrollData);
      
      return new NextResponse(csvContent, {
        status: 200,
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="payroll-leave-data-${periodStartParam}-to-${periodEndParam}.csv"`
        }
      });
    }

    return NextResponse.json({
      success: true,
      data: payrollData,
      metadata: {
        reportType: 'payroll-leave-data',
        periodStart: periodStartParam,
        periodEnd: periodEndParam,
        generatedAt: new Date().toISOString(),
        generatedBy: user.id
      }
    });

  } catch (error: unknown) {
    logger.error('Error generating payroll leave data', LogCategory.PAYROLL, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while generating payroll data' 
      },
      { status: 500 }
    );
  }

  /**
   * Generate CSV content for payroll data
   * @param payrollData - Payroll data
   * @returns CSV string
   */
  private async generatePayrollCSV(payrollData: any): Promise<string> {
    let csv = 'Payroll Leave Data Report\n\n';
    
    // Summary
    csv += 'Summary\n';
    csv += 'Period Start,Period End,Total Employees,Total Leave Deductions,Total Encashment Payments,Net Impact\n';
    csv += `${payrollData.periodStart},${payrollData.periodEnd},${payrollData.totalEmployees},${payrollData.summary.totalLeaveDeductions},${payrollData.summary.totalEncashmentPayments},${payrollData.summary.netPayrollImpact}\n\n`;

    // Leave Deductions
    csv += 'Leave Deductions\n';
    csv += 'Employee ID,Employee Name,Employee Code,Total Unpaid Days,Total Deduction Amount,Basic Salary,Daily Rate\n';
    
    payrollData.leaveDeductions.forEach((emp: any) => {
      csv += `${emp.employeeId},${emp.employeeName},${emp.employeeCode},${emp.totalUnpaidDays},${emp.totalDeductionAmount},${emp.basicSalary},${emp.dailyRate}\n`;
    });

    csv += '\nLeave Deduction Details\n';
    csv += 'Employee ID,Employee Name,Leave Type,Start Date,End Date,Days,Is Paid,Deduction Amount\n';
    
    payrollData.leaveDeductions.forEach((emp: any) => {
      emp.leaveRequests.forEach((req: any) => {
        csv += `${emp.employeeId},${emp.employeeName},${req.leaveType},${req.startDate},${req.endDate},${req.days},${req.isPaid},${req.deductionAmount}\n`;
      });
    });

    // Encashment Payments
    csv += '\nEncashment Payments\n';
    csv += 'Employee ID,Employee Name,Employee Code,Total Encashment Amount,Taxable Amount,Non-Taxable Amount\n';
    
    payrollData.encashmentPayments.forEach((emp: any) => {
      csv += `${emp.employeeId},${emp.employeeName},${emp.employeeCode},${emp.totalEncashmentAmount},${emp.taxableAmount},${emp.nonTaxableAmount}\n`;
    });

    csv += '\nEncashment Payment Details\n';
    csv += 'Employee ID,Employee Name,Encashment ID,Leave Type,Days Encashed,Rate Per Day,Total Amount,Taxable,Payroll Component\n';
    
    payrollData.encashmentPayments.forEach((emp: any) => {
      emp.encashments.forEach((enc: any) => {
        csv += `${emp.employeeId},${emp.employeeName},${enc.encashmentId},${enc.leaveType},${enc.daysEncashed},${enc.ratePerDay},${enc.totalAmount},${enc.taxable},${enc.payrollComponent}\n`;
      });
    });

    return csv;
  }
}
