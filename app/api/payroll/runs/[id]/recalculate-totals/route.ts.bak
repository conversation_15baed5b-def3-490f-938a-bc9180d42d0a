// app/api/payroll/runs/[id]/recalculate-totals/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import PayrollRun from '@/models/payroll/PayrollRun';
import PayrollRecord from '@/models/payroll/PayrollRecord';
import mongoose from 'mongoose';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { errorService } from '@/lib/backend/services/error-service';
import { comprehensiveCalculationService } from '@/lib/services/payroll/comprehensive-calculation-service';

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectToDatabase();

    const currentUser = await getCurrentUser(req);
    if (!currentUser) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;

    // Get payroll run
    const payrollRun = await PayrollRun.findById(id);
    if (!payrollRun) {
      return NextResponse.json(
        { error: 'Payroll run not found' },
        { status: 404 }
      );
    }

    // Only allow recalculation for completed, approved, or paid payroll runs
    if (!['completed', 'approved', 'paid'].includes(payrollRun.status)) {
      return NextResponse.json(
        { error: `Cannot recalculate totals for payroll run with status '${payrollRun.status}'. Only completed, approved, or paid payroll runs can be recalculated.` },
        { status: 400 }
      );
    }

    logger.info(`Recalculating totals for payroll run ${id}`, LogCategory.PAYROLL, {
      payrollRunId: id,
      currentStatus: payrollRun.status,
      currentTotals: {
        totalGrossSalary: payrollRun.totalGrossSalary,
        totalDeductions: payrollRun.totalDeductions,
        totalTax: payrollRun.totalTax,
        totalNetSalary: payrollRun.totalNetSalary
      }
    });

    // First, let's check if any payroll records exist at all for this payroll run
    const allPayrollRecords = await PayrollRecord.find({
      payrollRunId: new mongoose.Types.ObjectId(id)
    }).lean();

    logger.info(`Found ${allPayrollRecords.length} total payroll records for payroll run ${id}`, LogCategory.PAYROLL, {
      payrollRunId: id,
      totalRecords: allPayrollRecords.length,
      recordStatuses: allPayrollRecords.map(r => r.status)
    });

    if (allPayrollRecords.length === 0) {
      // Use error service for structured error response
      return errorService.handlePayrollError(
        'NO_RECORDS',
        {
          userId: currentUser.id,
          userRole: currentUser.role,
          endpoint: `/api/payroll/runs/${id}/recalculate-totals`,
          method: 'POST',
          additionalData: {
            payrollRunId: id,
            payrollRunStatus: payrollRun.status,
            totalRecordsFound: 0
          }
        },
        {
          payrollRunId: id,
          payrollRunStatus: payrollRun.status,
          operation: 'recalculate-totals'
        }
      );
    }

    // Get payroll records excluding cancelled ones
    const payrollRecords = allPayrollRecords.filter(record => record.status !== 'cancelled');

    if (payrollRecords.length === 0) {
      return NextResponse.json(
        {
          error: 'No active payroll records found for this payroll run',
          details: `Found ${allPayrollRecords.length} total records, but all are cancelled.`,
          totalRecordsFound: allPayrollRecords.length,
          cancelledRecords: allPayrollRecords.length
        },
        { status: 404 }
      );
    }

    // Use comprehensive calculation service to recalculate totals with proper deductions and tax
    logger.info(`Using comprehensive calculation service for payroll run ${id}`, LogCategory.PAYROLL, {
      recordCount: payrollRecords.length,
      userId: currentUser.id
    });

    const comprehensiveTotals = await comprehensiveCalculationService.calculatePayrollRunTotals(id);

    // Map to the expected format
    const totals = {
      totalGrossSalary: comprehensiveTotals.totalGrossSalary,
      totalDeductions: comprehensiveTotals.totalDeductions,
      totalTax: comprehensiveTotals.totalTax,
      totalNetSalary: comprehensiveTotals.totalNetSalary
    };

    logger.info(`Comprehensive totals calculated`, LogCategory.PAYROLL, {
      payrollRunId: id,
      totals: comprehensiveTotals,
      breakdown: comprehensiveTotals.breakdown,
      employeeCount: comprehensiveTotals.totalEmployees
    });

    // Store original totals for comparison
    const originalTotals = {
      totalGrossSalary: payrollRun.totalGrossSalary,
      totalDeductions: payrollRun.totalDeductions,
      totalTax: payrollRun.totalTax,
      totalNetSalary: payrollRun.totalNetSalary
    };

    // Update payroll run with recalculated totals
    payrollRun.totalGrossSalary = totals.totalGrossSalary;
    payrollRun.totalDeductions = totals.totalDeductions;
    payrollRun.totalTax = totals.totalTax;
    payrollRun.totalNetSalary = totals.totalNetSalary;
    payrollRun.processedEmployees = payrollRecords.length;
    payrollRun.updatedBy = new mongoose.Types.ObjectId(currentUser.id as string);
    payrollRun.updatedAt = new Date();

    await payrollRun.save();

    // Calculate differences
    const differences = {
      totalGrossSalary: totals.totalGrossSalary - originalTotals.totalGrossSalary,
      totalDeductions: totals.totalDeductions - originalTotals.totalDeductions,
      totalTax: totals.totalTax - originalTotals.totalTax,
      totalNetSalary: totals.totalNetSalary - originalTotals.totalNetSalary
    };

    logger.info(`Totals recalculated successfully for payroll run ${id}`, LogCategory.PAYROLL, {
      payrollRunId: id,
      recordsProcessed: payrollRecords.length,
      originalTotals,
      newTotals: totals,
      differences
    });

    return NextResponse.json({
      success: true,
      message: 'Payroll run totals recalculated successfully',
      data: {
        payrollRunId: id,
        recordsProcessed: payrollRecords.length,
        originalTotals,
        newTotals: totals,
        differences,
        hasChanges: Object.values(differences).some(diff => diff !== 0)
      }
    });

  } catch (error) {
    logger.error(`Error recalculating totals for payroll run ${(await params).id}`, LogCategory.API, error);
    return NextResponse.json(
      {
        error: 'Failed to recalculate totals',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
