// app/api/payroll/runs/[id]/reprocess/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/backend/database';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import PayrollRun from '@/models/payroll/PayrollRun';
import PayrollRecord from '@/models/payroll/PayrollRecord';
import mongoose from 'mongoose';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { errorService } from '@/lib/backend/services/error-service';
import { comprehensiveCalculationService } from '@/lib/services/payroll/comprehensive-calculation-service';
import Employee from '@/models/Employee';

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectToDatabase();

    const currentUser = await getCurrentUser(req);
    if (!currentUser) {
      return errorService.handlePayrollError(
        'UNAUTHORIZED_ACCESS',
        {
          endpoint: req.nextUrl.pathname,
          method: req.method
        }
      );
    }

    const { id } = await params;

    // Get payroll run
    const payrollRun = await PayrollRun.findById(id);
    if (!payrollRun) {
      return errorService.handlePayrollError(
        'RESOURCE_NOT_FOUND',
        {
          userId: currentUser.id,
          endpoint: req.nextUrl.pathname,
          method: req.method,
          additionalData: { payrollRunId: id }
        }
      );
    }

    // Only allow reprocessing for completed, approved, or paid payroll runs with no records
    if (!['completed', 'approved', 'paid'].includes(payrollRun.status)) {
      return NextResponse.json(
        { error: `Cannot reprocess payroll run with status '${payrollRun.status}'. Only completed, approved, or paid payroll runs can be reprocessed.` },
        { status: 400 }
      );
    }

    logger.info(`Reprocessing payroll run ${id}`, LogCategory.PAYROLL, {
      payrollRunId: id,
      currentStatus: payrollRun.status,
      currentTotals: {
        totalGrossSalary: payrollRun.totalGrossSalary,
        totalDeductions: payrollRun.totalDeductions,
        totalTax: payrollRun.totalTax,
        totalNetSalary: payrollRun.totalNetSalary
      }
    });

    // Check if payroll records already exist
    const existingRecords = await PayrollRecord.find({
      payrollRunId: new mongoose.Types.ObjectId(id)
    }).lean();

    if (existingRecords.length > 0) {
      return NextResponse.json(
        {
          error: 'Payroll records already exist for this payroll run',
          details: `Found ${existingRecords.length} existing records. Use "Recalculate Totals" instead.`,
          existingRecords: existingRecords.length
        },
        { status: 400 }
      );
    }

    // Store original status to restore later
    const originalStatus = payrollRun.status;

    try {
      // Reset payroll run to draft status for reprocessing
      payrollRun.status = 'draft';
      payrollRun.processedEmployees = 0;
      payrollRun.totalGrossSalary = 0;
      payrollRun.totalDeductions = 0;
      payrollRun.totalTax = 0;
      payrollRun.totalNetSalary = 0;
      payrollRun.updatedBy = new mongoose.Types.ObjectId(currentUser.id as string);
      payrollRun.updatedAt = new Date();
      await payrollRun.save();

      logger.info(`Reset payroll run ${id} to draft status for reprocessing`, LogCategory.PAYROLL, {
        payrollRunId: id,
        originalStatus,
        newStatus: 'draft'
      });

      // Get all active employees for this payroll run
      let employees;
      if (payrollRun.departments && payrollRun.departments.length > 0) {
        employees = await Employee.find({
          departmentId: { $in: payrollRun.departments },
          status: 'active'
        }).populate('departmentId').lean();
      } else {
        employees = await Employee.find({
          status: 'active'
        }).populate('departmentId').lean();
      }

      if (employees.length === 0) {
        throw new Error('No active employees found for reprocessing');
      }

      // Process each employee with comprehensive calculations
      const processedEmployees = [];
      const failedEmployees = [];
      let totalProcessed = 0;

      for (const employee of employees) {
        try {
          // Calculate comprehensive payroll for this employee
          const calculation = await comprehensiveCalculationService.calculateEmployeePayroll(
            (employee as any)._id.toString(),
            id,
            {
              startDate: payrollRun.payPeriod.startDate,
              endDate: payrollRun.payPeriod.endDate
            }
          );

          // Create payroll record with comprehensive data
          const payrollRecord = new PayrollRecord({
            payrollRunId: id,
            employeeId: (employee as any)._id,
            basicSalary: calculation.basicSalary,
            allowances: calculation.allowances,
            totalAllowances: calculation.allowances.reduce((sum, a) => sum + a.amount, 0),
            grossSalary: calculation.grossSalary,
            deductions: calculation.deductions,
            totalDeductions: calculation.totalDeductions,
            taxableIncome: calculation.taxableIncome,
            incomeTax: calculation.incomeTax,
            netSalary: calculation.netSalary,
            status: 'completed',
            calculationDetails: calculation.calculationDetails,
            processedAt: new Date(),
            createdBy: currentUser.id,
            updatedBy: currentUser.id
          });

          await payrollRecord.save();
          processedEmployees.push(calculation.employeeName);
          totalProcessed++;

        } catch (error) {
          logger.error(`Failed to process employee ${(employee as any)._id}`, LogCategory.PAYROLL, error);
          failedEmployees.push(`${(employee as any).firstName} ${(employee as any).lastName}`);
        }
      }

      // Recalculate payroll run totals using comprehensive service
      const comprehensiveTotals = await comprehensiveCalculationService.calculatePayrollRunTotals(id);

      // Update payroll run with comprehensive totals
      payrollRun.totalEmployees = employees.length;
      payrollRun.processedEmployees = totalProcessed;
      payrollRun.totalGrossSalary = comprehensiveTotals.totalGrossSalary;
      payrollRun.totalDeductions = comprehensiveTotals.totalDeductions;
      payrollRun.totalTax = comprehensiveTotals.totalTax;
      payrollRun.totalNetSalary = comprehensiveTotals.totalNetSalary;
      payrollRun.status = 'completed';
      payrollRun.processedAt = new Date();
      await payrollRun.save();

      const result = {
        success: true,
        processedEmployees: totalProcessed,
        failedEmployees: failedEmployees.length,
        totals: comprehensiveTotals
      };

      // Get the updated payroll run to return current totals
      const updatedPayrollRun = await PayrollRun.findById(id);

      logger.info(`Payroll run ${id} reprocessed successfully`, LogCategory.PAYROLL, {
        payrollRunId: id,
        originalStatus,
        newStatus: updatedPayrollRun?.status,
        processedEmployees: result.processedEmployees,
        newTotals: {
          totalGrossSalary: updatedPayrollRun?.totalGrossSalary,
          totalDeductions: updatedPayrollRun?.totalDeductions,
          totalTax: updatedPayrollRun?.totalTax,
          totalNetSalary: updatedPayrollRun?.totalNetSalary
        }
      });

      // If original status was approved or paid, restore it
      if (originalStatus === 'approved' || originalStatus === 'paid') {
        if (updatedPayrollRun) {
          updatedPayrollRun.status = originalStatus;
          await updatedPayrollRun.save();

          logger.info(`Restored payroll run ${id} to original status`, LogCategory.PAYROLL, {
            payrollRunId: id,
            restoredStatus: originalStatus
          });
        }
      }

      return NextResponse.json({
        success: true,
        message: 'Payroll run reprocessed successfully with comprehensive calculations',
        data: {
          payrollRunId: id,
          originalStatus,
          currentStatus: originalStatus === 'approved' || originalStatus === 'paid' ? originalStatus : 'completed',
          processedEmployees: result.processedEmployees,
          failedEmployees: result.failedEmployees,
          newTotals: {
            totalGrossSalary: result.totals.totalGrossSalary,
            totalDeductions: result.totals.totalDeductions,
            totalTax: result.totals.totalTax,
            totalNetSalary: result.totals.totalNetSalary
          },
          breakdown: result.totals.breakdown,
          hasDeductionsAndTax: result.totals.totalDeductions > 0 || result.totals.totalTax > 0,
          comprehensiveCalculation: true
        }
      });

    } catch (processingError) {
      // If reprocessing fails, restore original status
      try {
        payrollRun.status = originalStatus;
        await payrollRun.save();
        logger.warn(`Restored payroll run ${id} to original status after processing failure`, LogCategory.PAYROLL, {
          payrollRunId: id,
          restoredStatus: originalStatus
        });
      } catch (restoreError) {
        logger.error(`Failed to restore payroll run ${id} status after processing failure`, LogCategory.PAYROLL, restoreError);
      }

      throw processingError;
    }

  } catch (error) {
    logger.error(`Error reprocessing payroll run ${params ? (await params).id : 'unknown'}`, LogCategory.API, error);
    return NextResponse.json(
      {
        error: 'Failed to reprocess payroll run',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
