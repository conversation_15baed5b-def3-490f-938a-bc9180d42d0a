import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { PayrollBudgetIntegrationService } from '@/lib/services/integration/payroll-budget-integration';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import mongoose from 'mongoose';

/**
 * POST /api/integration/payroll/update-budget
 * Manually trigger budget update for a specific payroll run
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { payrollRunId } = body;

    if (!payrollRunId) {
      return NextResponse.json(
        { error: 'Payroll run ID is required' },
        { status: 400 }
      );
    }

    logger.info('Manual budget update triggered', LogCategory.INTEGRATION, {
      payrollRunId,
      userId: (user._id as mongoose.Types.ObjectId).toString()
    });

    // Create budget integration service and update budget
    const budgetIntegrationService = new PayrollBudgetIntegrationService();
    await budgetIntegrationService.updateBudgetActuals(payrollRunId, (user._id as mongoose.Types.ObjectId).toString());

    logger.info('Manual budget update completed successfully', LogCategory.INTEGRATION, {
      payrollRunId,
      userId: (user._id as mongoose.Types.ObjectId).toString()
    });

    return NextResponse.json({
      success: true,
      message: 'Budget update completed successfully',
      payrollRunId
    });

  } catch (error) {
    logger.error('Manual budget update failed', LogCategory.INTEGRATION, error);
    
    return NextResponse.json(
      { 
        error: 'Failed to update budget',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/integration/payroll/update-budget
 * Get budget update status for payroll runs
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const payrollRunId = searchParams.get('payrollRunId');

    if (!payrollRunId) {
      return NextResponse.json(
        { error: 'Payroll run ID is required' },
        { status: 400 }
      );
    }

    // Get budget impact for the payroll run
    const { PayrollBudgetImpact } = await import('@/models/payroll/PayrollBudgetImpact');
    const budgetImpact = await PayrollBudgetImpact.findOne({ payrollRunId })
      .populate('budgetId', 'name fiscalYear')
      .populate('payrollRunId', 'name');

    if (!budgetImpact) {
      return NextResponse.json({
        success: true,
        data: null,
        message: 'No budget impact found for this payroll run'
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        id: budgetImpact._id,
        payrollRunId: budgetImpact.payrollRunId,
        budgetId: budgetImpact.budgetId,
        budgetedAmount: budgetImpact.budgetedAmount,
        actualAmount: budgetImpact.actualAmount,
        variance: budgetImpact.variance,
        variancePercentage: budgetImpact.variancePercentage,
        varianceStatus: budgetImpact.varianceStatus,
        alertLevel: budgetImpact.alertLevel,
        requiresApproval: budgetImpact.requiresApproval,
        approvalStatus: budgetImpact.approvalStatus,
        categoryImpacts: budgetImpact.categoryImpacts,
        createdAt: budgetImpact.createdAt,
        updatedAt: budgetImpact.updatedAt
      }
    });

  } catch (error) {
    logger.error('Failed to get budget update status', LogCategory.INTEGRATION, error);
    
    return NextResponse.json(
      { 
        error: 'Failed to get budget update status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
