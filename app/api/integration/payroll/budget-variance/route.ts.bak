import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { PayrollAccountingAutomationService } from '@/lib/services/integration/payroll-accounting-automation';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import mongoose from 'mongoose';

/**
 * GET /api/integration/payroll/budget-variance
 * Get budget variance summary for payroll runs
 * Query parameters:
 * - fiscalYear: fiscal year to get variance for (required)
 * - period: specific period (optional)
 * - departmentId: filter by department (optional)
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const fiscalYear = searchParams.get('fiscalYear');
    const period = searchParams.get('period');
    const departmentId = searchParams.get('departmentId');

    if (!fiscalYear) {
      return NextResponse.json(
        { error: 'Fiscal year is required' },
        { status: 400 }
      );
    }

    logger.info('Getting budget variance summary', LogCategory.INTEGRATION, {
      fiscalYear,
      period,
      departmentId,
      userId: (user._id as mongoose.Types.ObjectId).toString()
    });

    // Create automation service and get variance summary
    const automationService = new PayrollAccountingAutomationService();
    const varianceSummary = await automationService.getBudgetVarianceSummary(fiscalYear, period);

    // Filter by department if specified
    let filteredSummary = varianceSummary;
    if (departmentId && varianceSummary.impacts) {
      filteredSummary = {
        ...varianceSummary,
        impacts: varianceSummary.impacts.filter((impact: any) => 
          impact.categoryImpacts.some((cat: any) => cat.departmentId?.toString() === departmentId)
        )
      };
    }

    return NextResponse.json({
      success: true,
      data: filteredSummary,
      fiscalYear,
      period,
      departmentId
    });

  } catch (error) {
    logger.error('Failed to get budget variance summary', LogCategory.INTEGRATION, error);
    
    return NextResponse.json(
      { 
        error: 'Failed to get budget variance summary',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
