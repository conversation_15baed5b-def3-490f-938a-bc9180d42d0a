import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { SuppliersPage } from "@/components/procurement/suppliers/suppliers-page"

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Supplier Management | Kawandama Hills Plantation Management System',
  description: 'Manage procurement suppliers, contracts, and vendor relationships',
};

export default async function Suppliers() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN,
    UserRole.SYSTEM_ADMIN,
    UserRole.PROCUREMENT_MANAGER,
    UserRole.PROCUREMENT_OFFICER,
    UserRole.FINANCE_DIRECTOR,
    UserRole.FINANCE_MANAGER
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Supplier Management"
        text="Manage procurement suppliers, contracts, and vendor relationships"
      />
      <div className="flex-1 space-y-6">
        <SuppliersPage />
      </div>
    </DashboardShell>
  );
}
