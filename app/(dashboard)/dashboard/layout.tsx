"use client"

import { UserNav } from "@/components/user-nav"
import { MobileNav } from "@/components/mobile-nav"
import { ThemeSwitcher } from "@/components/theme-switcher"
import { CurrencySelector } from "@/components/currency-selector"
import { Toaster } from "@/components/ui/toaster"
import DataPrefetcher from "@/components/data-prefetcher"
import { NavigationEvents } from "@/components/navigation-events"
import { NavigationOverlay } from "@/components/navigation-overlay"
import { Button } from "@/components/ui/button"
import { BookOpen, FileText } from "lucide-react"
import Link from "next/link"
import { ConvertDocumentButton } from "@/components/document-conversion/convert-document-button"

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-40 border-b bg-background">
        <div className="flex h-16 items-center justify-between px-4 py-4">
          <div className="flex items-center gap-2 md:gap-4">
            <MobileNav />
            <span className="hidden font-bold md:inline-block">
              Kawandama Hills Plantation
            </span>
          </div>
          <div className="flex items-center gap-2">
            <ConvertDocumentButton />
            <Button variant="ghost" size="icon" asChild className="h-9 w-9">
              <Link href="/docs">
                <BookOpen className="h-4 w-4" />
                <span className="sr-only">Documentation</span>
              </Link>
            </Button>
            <CurrencySelector size="sm" />
            <ThemeSwitcher />
            <UserNav />
          </div>
        </div>
      </header>
      <div className="flex-1 w-full">
        <main className="flex w-full flex-col overflow-hidden">
          {children}
        </main>
      </div>
      <Toaster />
      <DataPrefetcher />
      <NavigationEvents />
      <NavigationOverlay />
    </div>
  )
}





