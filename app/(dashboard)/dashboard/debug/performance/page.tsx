import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { PerformanceDebugPage } from '@/components/debug-services/performance-debug-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Performance Debug | Kawandama Hills Plantation',
  description: 'Performance monitoring, load testing, and optimization tools',
};

export default async function PerformanceDebugPageWrapper() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions - only admin roles can access performance debugging
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, 
    UserRole.SYSTEM_ADMIN
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Performance Debug"
        text="Performance monitoring, load testing, and optimization tools"
      />
      <div className="flex-1 space-y-6">
        <PerformanceDebugPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}
