import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { AuthDebugPage } from '@/components/debug-services/auth-debug-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Authentication Debug | Kawandama Hills Plantation',
  description: 'Debug tools for user sessions, permissions, and security validation',
};

export default async function AuthDebugPageWrapper() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions - only admin roles can access auth debugging
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, 
    UserRole.SYSTEM_ADMIN
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Authentication Debug"
        text="Debug tools for user sessions, permissions, and security validation"
      />
      <div className="flex-1 space-y-6">
        <AuthDebugPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}
