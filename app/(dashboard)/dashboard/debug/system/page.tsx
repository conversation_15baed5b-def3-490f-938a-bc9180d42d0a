import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { SystemDebugPage } from '@/components/debug-services/system-debug-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'System Debug | Kawandama Hills Plantation',
  description: 'System debugging tools for database, server, and environment validation',
};

export default async function SystemDebugPageWrapper() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions - only admin roles can access debug services
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, 
    UserRole.SYSTEM_ADMIN
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="System Debug"
        text="Database connectivity, server health, and environment validation tools"
      />
      <div className="flex-1 space-y-6">
        <SystemDebugPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}
