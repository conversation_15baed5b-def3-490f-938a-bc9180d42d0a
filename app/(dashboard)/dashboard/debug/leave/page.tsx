import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { LeaveDebugPage } from '@/components/debug-services/leave-debug-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Leave Management Debug | Kawandama Hills Plantation',
  description: 'Debug tools for leave types, requests, and policy validation',
};

export default async function LeaveDebugPageWrapper() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions - HR roles can access leave debugging
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, 
    UserRole.SYSTEM_ADMIN, 
    UserRole.HR_DIRECTOR, 
    UserRole.HR_MANAGER
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Leave Management Debug"
        text="Debug tools for leave types, requests, and policy validation"
      />
      <div className="flex-1 space-y-6">
        <LeaveDebugPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}
