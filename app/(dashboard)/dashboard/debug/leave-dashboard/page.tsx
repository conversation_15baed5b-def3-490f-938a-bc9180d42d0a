import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { LeaveDashboardDebug } from '@/components/debug-services/leave-dashboard-debug';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Leave Dashboard Debug | Kawandama Hills Plantation',
  description: 'Debug tools for leave dashboard functionality',
};

export default async function LeaveDashboardDebugPage() {
  const session = await getServerSession();
  
  if (!session?.user) {
    redirect('/auth/login');
  }

  // Check if user has permission to access debug tools
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN,
    UserRole.SYSTEM_ADMIN,
    UserRole.HR_DIRECTOR,
    UserRole.HR_MANAGER
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Leave Dashboard Debug"
        text="Comprehensive debugging and testing tools for the main leave dashboard."
      />
      <div className="grid gap-6">
        <LeaveDashboardDebug />
      </div>
    </DashboardShell>
  );
}
