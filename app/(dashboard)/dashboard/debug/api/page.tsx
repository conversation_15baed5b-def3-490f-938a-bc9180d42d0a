import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { ApiDebugPage } from '@/components/debug-services/api-debug-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'API Debug | Kawandama Hills Plantation',
  description: 'REST API validation, endpoint testing, and integration debugging',
};

export default async function ApiDebugPageWrapper() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions - only admin roles can access API debugging
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, 
    UserRole.SYSTEM_ADMIN
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="API Debug"
        text="REST API validation, endpoint testing, and integration debugging"
      />
      <div className="flex-1 space-y-6">
        <ApiDebugPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}
