import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { LeaveRequestsDebug } from '@/components/debug-services/leave-requests-debug';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Leave Requests Debug | Kawandama Hills Plantation',
  description: 'Debug tools for leave requests functionality',
};

export default async function LeaveRequestsDebugPage() {
  const session = await getServerSession();
  
  if (!session?.user) {
    redirect('/auth/login');
  }

  // Check if user has permission to access debug tools
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN,
    UserRole.SYSTEM_ADMIN,
    UserRole.HR_DIRECTOR,
    UserRole.HR_MANAGER
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Leave Requests Debug"
        text="Comprehensive debugging and testing tools for leave requests functionality."
      />
      <div className="grid gap-6">
        <LeaveRequestsDebug />
      </div>
    </DashboardShell>
  );
}
