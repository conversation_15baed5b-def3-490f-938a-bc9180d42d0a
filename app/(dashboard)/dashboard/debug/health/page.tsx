import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { HealthDebugPage } from '@/components/debug-services/health-debug-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Health Checks | Kawandama Hills Plantation',
  description: 'Comprehensive system health monitoring and diagnostics',
};

export default async function HealthDebugPageWrapper() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions - HR and admin roles can access health checks
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, 
    UserRole.SYSTEM_ADMIN, 
    UserRole.HR_DIRECTOR, 
    UserRole.HR_MANAGER
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Health Checks"
        text="Comprehensive system health monitoring and diagnostics"
      />
      <div className="flex-1 space-y-6">
        <HealthDebugPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}
