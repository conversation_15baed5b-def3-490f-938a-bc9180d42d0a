import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { DebugDashboardPage } from '@/components/debug-services/debug-dashboard-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Debug Services | Kawandama Hills Plantation',
  description: 'Comprehensive debugging and testing tools for system monitoring and troubleshooting',
};

export default async function DebugPageWrapper() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions - only admin roles can access debug services
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN, 
    UserRole.SYSTEM_ADMIN, 
    UserRole.HR_DIRECTOR, 
    UserRole.HR_MANAGER
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Debug Services"
        text="Comprehensive debugging and testing tools for system monitoring and troubleshooting"
      />
      <div className="flex-1 space-y-6">
        <DebugDashboardPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}
