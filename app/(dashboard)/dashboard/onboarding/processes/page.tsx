import { Metadata } from 'next';
import { getServerSession } from '@/lib/backend/auth/session';
import { redirect } from 'next/navigation';
import { hasRequiredPermissions } from '@/lib/backend/utils/permissions';
import { UserRole } from '@/types/user-roles';
import { DashboardShell } from '@/components/dashboard-shell';
import { DashboardHeader } from '@/components/dashboard-header';
import { OnboardingProcessesPage } from '@/components/onboarding/onboarding-processes-page';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Onboarding Processes | Kawandama Hills Plantation Management System',
  description: 'Manage employee onboarding processes',
};

export default async function ProcessesPage() {
  // Get session
  const session = await getServerSession();

  // Redirect if not authenticated
  if (!session) {
    redirect('/login');
  }

  // Check permissions
  const hasPermission = hasRequiredPermissions(session.user, [
    UserRole.SUPER_ADMIN,
    UserRole.SYSTEM_ADMIN,
    UserRole.HR_DIRECTOR,
    UserRole.HR_MANAGER,
    UserRole.HR_SPECIALIST,
    UserRole.RECRUITER,
    UserRole.DEPARTMENT_HEAD,
    UserRole.TEAM_LEADER
  ]);

  if (!hasPermission) {
    redirect('/dashboard');
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Onboarding Processes"
        text="Manage employee onboarding processes"
      />
      <div className="flex-1 space-y-6">
        <OnboardingProcessesPage userId={session.user.id} />
      </div>
    </DashboardShell>
  );
}
