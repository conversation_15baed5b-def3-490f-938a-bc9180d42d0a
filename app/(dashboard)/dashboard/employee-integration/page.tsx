"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

import { LeaveBalanceDashboard } from "@/components/leave-management/leave-balance-dashboard"
import { LeaveRequestFormWithStats } from "@/components/leave-management/leave-request-form-with-stats"
import { LeaveApprovalWorkflow } from "@/components/leave-management/leave-approval-workflow"
import { LeaveCalendarView } from "@/components/leave-management/leave-calendar-view"
import { LoanApplicationForm, LoanApprovalWorkflow, LoanRepaymentSchedule } from "@/components/loan-management"
import { SalaryStructureViewer, SalaryHistoryViewer, SalaryRevisionForm } from "@/components/salary-management"

export default function EmployeeIntegrationPage() {
  const [activeTab, setActiveTab] = useState("leave")
  const [activeSubTab, setActiveSubTab] = useState({
    leave: "balance",
    loan: "application",
    salary: "structure"
  })

  // Sample employee ID - in a real app, this would come from the route or context
  const sampleEmployeeId = "employee123"
  // Sample loan ID - in a real app, this would come from the route or context
  const sampleLoanId = "loan123"

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Employee Module Integration</h1>
        <p className="text-muted-foreground">
          Demo of the integrated employee module components
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="leave">Leave Management</TabsTrigger>
          <TabsTrigger value="loan">Loan Management</TabsTrigger>
          <TabsTrigger value="salary">Salary Management</TabsTrigger>
        </TabsList>

        {/* Leave Management Tab */}
        <TabsContent value="leave" className="space-y-4">
          <Tabs value={activeSubTab.leave} onValueChange={(value) => setActiveSubTab({...activeSubTab, leave: value})}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="balance">Leave Balance</TabsTrigger>
              <TabsTrigger value="request">Request Leave</TabsTrigger>
              <TabsTrigger value="approval">Approval Workflow</TabsTrigger>
              <TabsTrigger value="calendar">Calendar View</TabsTrigger>
            </TabsList>

            <TabsContent value="balance" className="mt-4">
              <LeaveBalanceDashboard employeeId={sampleEmployeeId} />
            </TabsContent>

            <TabsContent value="request" className="mt-4">
              <LeaveRequestFormWithStats
                onSuccess={() => alert("Leave request submitted successfully")}
                onCancel={() => alert("Leave request cancelled")}
              />
            </TabsContent>

            <TabsContent value="approval" className="mt-4">
              <LeaveApprovalWorkflow />
            </TabsContent>

            <TabsContent value="calendar" className="mt-4">
              <LeaveCalendarView />
            </TabsContent>
          </Tabs>
        </TabsContent>

        {/* Loan Management Tab */}
        <TabsContent value="loan" className="space-y-4">
          <Tabs value={activeSubTab.loan} onValueChange={(value) => setActiveSubTab({...activeSubTab, loan: value})}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="application">Loan Application</TabsTrigger>
              <TabsTrigger value="approval">Approval Workflow</TabsTrigger>
              <TabsTrigger value="repayment">Repayment Schedule</TabsTrigger>
            </TabsList>

            <TabsContent value="application" className="mt-4">
              <LoanApplicationForm
                employeeId={sampleEmployeeId}
                onSuccess={() => alert("Loan application submitted successfully")}
                onCancel={() => alert("Loan application cancelled")}
              />
            </TabsContent>

            <TabsContent value="approval" className="mt-4">
              <LoanApprovalWorkflow />
            </TabsContent>

            <TabsContent value="repayment" className="mt-4">
              <LoanRepaymentSchedule loanId={sampleLoanId} />
            </TabsContent>
          </Tabs>
        </TabsContent>

        {/* Salary Management Tab */}
        <TabsContent value="salary" className="space-y-4">
          <Tabs value={activeSubTab.salary} onValueChange={(value) => setActiveSubTab({...activeSubTab, salary: value})}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="structure">Salary Structure</TabsTrigger>
              <TabsTrigger value="history">Salary History</TabsTrigger>
              <TabsTrigger value="revision">Salary Revision</TabsTrigger>
            </TabsList>

            <TabsContent value="structure" className="mt-4">
              <SalaryStructureViewer
                onEdit={(id) => alert(`Edit salary structure ${id}`)}
                onDelete={(id) => alert(`Delete salary structure ${id}`)}
              />
            </TabsContent>

            <TabsContent value="history" className="mt-4">
              <SalaryHistoryViewer employeeId={sampleEmployeeId} />
            </TabsContent>

            <TabsContent value="revision" className="mt-4">
              <SalaryRevisionForm
                employeeId={sampleEmployeeId}
                onSuccess={() => alert("Salary revision submitted successfully")}
                onCancel={() => alert("Salary revision cancelled")}
              />
            </TabsContent>
          </Tabs>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle>Implementation Notes</CardTitle>
          <CardDescription>
            Important information about the implemented components
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold">API Integration</h3>
            <p className="text-sm text-muted-foreground">
              These components are designed to work with the API endpoints implemented in the backend services.
              The demo page uses placeholder data, but in a real application, these components would fetch data
              from the API endpoints.
            </p>
          </div>

          <div>
            <h3 className="text-lg font-semibold">Component Reusability</h3>
            <p className="text-sm text-muted-foreground">
              All components are designed to be reusable across different parts of the application. They accept
              props for customization and can be used in different contexts.
            </p>
          </div>

          <div>
            <h3 className="text-lg font-semibold">State Management</h3>
            <p className="text-sm text-muted-foreground">
              For a production application, these components should be integrated with a state management solution
              like Zustand (as per project requirements) to maintain application state across components.
            </p>
          </div>

          <div>
            <h3 className="text-lg font-semibold">Next Steps</h3>
            <p className="text-sm text-muted-foreground">
              The next steps would be to implement the API endpoints for these components and integrate them with
              the accounting module for comprehensive financial tracking.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
