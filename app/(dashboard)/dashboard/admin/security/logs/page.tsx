import { Metadata } from "next";
import { LoginLogsDashboard } from "@/components/admin/login-logs-dashboard";
import { DashboardShell } from "@/components/dashboard-shell";
import { DashboardHeader } from "@/components/dashboard-header";

export const metadata: Metadata = {
  title: "Login Logs",
  description: "Monitor and analyze user login activity across the system",
};

export default function AdminLoginLogsPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Login Logs"
        text="Monitor and analyze user login activity across the system"
      />
      <div className="grid gap-8">
        <LoginLogsDashboard />
      </div>
    </DashboardShell>
  );
}
