"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  RefreshCw,
  User,
  Database,
  Server
} from "lucide-react"

interface TestResult {
  endpoint: string
  status: 'success' | 'error' | 'loading'
  response?: any
  error?: string
  duration?: number
}

export default function APIDebugPage() {
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const apiEndpoints = [
    { name: 'Organization API', url: '/api/dashboard/organization?type=departments' },
    { name: 'Leave API', url: '/api/dashboard/leave?limit=5' },
    { name: 'Approvals API', url: '/api/dashboard/approvals?limit=5' },
    { name: 'Auth Check', url: '/api/auth/me' },
  ]

  const testEndpoint = async (endpoint: { name: string; url: string }): Promise<TestResult> => {
    const startTime = Date.now()
    
    try {
      const response = await fetch(endpoint.url)
      const duration = Date.now() - startTime
      
      // Check content type
      const contentType = response.headers.get('content-type')
      
      if (contentType && contentType.includes('text/html')) {
        return {
          endpoint: endpoint.name,
          status: 'error',
          error: `Server returned HTML (${response.status}): Likely an error page`,
          duration
        }
      }
      
      if (!response.ok) {
        const errorText = await response.text()
        return {
          endpoint: endpoint.name,
          status: 'error',
          error: `HTTP ${response.status}: ${errorText}`,
          duration
        }
      }
      
      const data = await response.json()
      return {
        endpoint: endpoint.name,
        status: 'success',
        response: data,
        duration
      }
    } catch (error) {
      const duration = Date.now() - startTime
      return {
        endpoint: endpoint.name,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        duration
      }
    }
  }

  const runAllTests = async () => {
    setIsRunning(true)
    setTestResults([])
    
    for (const endpoint of apiEndpoints) {
      // Update with loading state
      setTestResults(prev => [...prev, {
        endpoint: endpoint.name,
        status: 'loading'
      }])
      
      const result = await testEndpoint(endpoint)
      
      // Update with actual result
      setTestResults(prev => 
        prev.map(r => r.endpoint === endpoint.name ? result : r)
      )
    }
    
    setIsRunning(false)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'loading':
        return <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'error':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'loading':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">API Debug Tool</h1>
          <p className="text-muted-foreground">
            Test dashboard API endpoints and diagnose issues
          </p>
        </div>
        <Button 
          onClick={runAllTests} 
          disabled={isRunning}
          className="flex items-center gap-2"
        >
          {isRunning ? (
            <RefreshCw className="h-4 w-4 animate-spin" />
          ) : (
            <Server className="h-4 w-4" />
          )}
          {isRunning ? 'Testing...' : 'Run Tests'}
        </Button>
      </div>

      <Tabs defaultValue="results" className="space-y-4">
        <TabsList>
          <TabsTrigger value="results">Test Results</TabsTrigger>
          <TabsTrigger value="info">System Info</TabsTrigger>
          <TabsTrigger value="help">Troubleshooting</TabsTrigger>
        </TabsList>

        <TabsContent value="results" className="space-y-4">
          {testResults.length === 0 ? (
            <Card>
              <CardContent className="py-6 text-center">
                <Server className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                <p className="text-sm text-muted-foreground">
                  Click "Run Tests" to test API endpoints
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {testResults.map((result, index) => (
                <Card key={index}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg flex items-center gap-2">
                        {getStatusIcon(result.status)}
                        {result.endpoint}
                      </CardTitle>
                      <div className="flex items-center gap-2">
                        {result.duration && (
                          <Badge variant="outline" className="text-xs">
                            {result.duration}ms
                          </Badge>
                        )}
                        <Badge className={getStatusColor(result.status)}>
                          {result.status}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {result.error && (
                      <div className="mb-3">
                        <p className="text-sm font-medium text-red-600 mb-1">Error:</p>
                        <p className="text-sm text-red-700 bg-red-50 p-2 rounded">
                          {result.error}
                        </p>
                      </div>
                    )}
                    {result.response && (
                      <div>
                        <p className="text-sm font-medium mb-1">Response:</p>
                        <Textarea
                          value={JSON.stringify(result.response, null, 2)}
                          readOnly
                          className="font-mono text-xs h-32"
                        />
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="info" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                System Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">User Agent:</span>
                  <p className="text-muted-foreground break-all">
                    {navigator.userAgent}
                  </p>
                </div>
                <div>
                  <span className="font-medium">Current URL:</span>
                  <p className="text-muted-foreground break-all">
                    {window.location.href}
                  </p>
                </div>
                <div>
                  <span className="font-medium">Cookies Enabled:</span>
                  <p className="text-muted-foreground">
                    {navigator.cookieEnabled ? 'Yes' : 'No'}
                  </p>
                </div>
                <div>
                  <span className="font-medium">Local Storage:</span>
                  <p className="text-muted-foreground">
                    {typeof Storage !== 'undefined' ? 'Available' : 'Not Available'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="help" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Common Issues & Solutions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium text-red-600">HTML Response (Unexpected token '&lt;')</h4>
                  <p className="text-sm text-muted-foreground">
                    The server is returning an HTML error page instead of JSON. This usually means:
                  </p>
                  <ul className="text-sm text-muted-foreground list-disc list-inside mt-1 space-y-1">
                    <li>You're not logged in or your session expired</li>
                    <li>The API route doesn't exist or has syntax errors</li>
                    <li>The development server isn't running</li>
                    <li>Database connection issues</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-medium text-yellow-600">Authentication Errors</h4>
                  <p className="text-sm text-muted-foreground">
                    If you see "Insufficient permissions" or 401/403 errors:
                  </p>
                  <ul className="text-sm text-muted-foreground list-disc list-inside mt-1 space-y-1">
                    <li>Make sure you're logged in to the application</li>
                    <li>Check if your user role has the required permissions</li>
                    <li>Clear browser cookies and log in again</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-medium text-blue-600">Network Errors</h4>
                  <p className="text-sm text-muted-foreground">
                    If you see network or fetch errors:
                  </p>
                  <ul className="text-sm text-muted-foreground list-disc list-inside mt-1 space-y-1">
                    <li>Check if the development server is running (npm run dev)</li>
                    <li>Verify the correct port (usually 3000)</li>
                    <li>Check browser console for additional error details</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
