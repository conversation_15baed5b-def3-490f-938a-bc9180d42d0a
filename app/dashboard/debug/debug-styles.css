/**
 * Debug Services - Custom Styles
 * 
 * Additional styling for debug components
 */

/* Debug Panel Styles */
.debug-panel {
  @apply border border-slate-200 rounded-lg bg-white shadow-sm;
}

.debug-test-running {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.debug-status-success {
  @apply text-green-700 bg-green-50 border-green-200;
}

.debug-status-warning {
  @apply text-yellow-700 bg-yellow-50 border-yellow-200;
}

.debug-status-error {
  @apply text-red-700 bg-red-50 border-red-200;
}

.debug-log-entry {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  @apply text-sm leading-relaxed;
}

.debug-metric-card {
  @apply transition-all duration-200 ease-in-out;
}

.debug-metric-card:hover {
  @apply -translate-y-0.5 shadow-md;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .debug-panel {
    @apply bg-slate-800 border-slate-600;
  }
  
  .debug-status-success {
    @apply text-green-400 bg-green-900/20 border-green-700;
  }
  
  .debug-status-warning {
    @apply text-yellow-400 bg-yellow-900/20 border-yellow-700;
  }
  
  .debug-status-error {
    @apply text-red-400 bg-red-900/20 border-red-700;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .debug-panel {
    @apply mx-2 rounded-md;
  }
  
  .debug-metric-card {
    @apply mb-4;
  }
}

/* Print styles */
@media print {
  .debug-test-running {
    animation: none !important;
  }
  
  .debug-panel {
    @apply border-black shadow-none;
  }
}
