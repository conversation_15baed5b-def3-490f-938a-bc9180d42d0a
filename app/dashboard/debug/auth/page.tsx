/**
 * Debug Services - Authentication Tests Page
 * 
 * User authentication, permissions, and security validation
 */

"use client";

import React, { useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  Shield, 
  Key, 
  Users, 
  Lock, 
  ArrowLeft,
  Activity,
  UserCheck,
  Settings
} from 'lucide-react';
import Link from 'next/link';
import { 
  DebugServices, 
  DebugPanel,
  DebugTestBuilder,
  DebugTestSuiteBuilder,
  CommonTests,
  DebugTestType,
  DebugTestStatus,
  useDebugServices
} from '@/lib/debug-services';

export default function AuthDebugPage() {
  const { service, isInitialized } = useDebugServices();

  useEffect(() => {
    if (!isInitialized) {
      // Initialize debug services
      const debugService = DebugServices.initialize({
        environment: 'development',
        uiEnabled: true,
        allowedRoles: ['super_admin', 'system_admin'],
        maxLogEntries: 2000,
        defaultTimeout: 30000
      });

      // Create authentication specific test suite
      const authTestSuite = createAuthenticationTestSuite();
      
      // Register the test suite
      debugService.registerTestSuite(authTestSuite);
    }
  }, [isInitialized]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/debug">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Debug Dashboard
            </Link>
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
              <Shield className="h-8 w-8 text-primary" />
              Authentication Tests
            </h1>
            <p className="text-muted-foreground mt-2">
              User authentication, permissions, and security validation
            </p>
          </div>
        </div>
        <Badge variant="outline" className="text-xs">
          Security Module
        </Badge>
      </div>

      {/* Authentication Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <UserCheck className="h-4 w-4" />
              Authentication
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">User Login</div>
            <p className="text-xs text-muted-foreground">Session management</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Key className="h-4 w-4" />
              Authorization
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Permissions</div>
            <p className="text-xs text-muted-foreground">Role-based access</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Users className="h-4 w-4" />
              User Roles
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">RBAC</div>
            <p className="text-xs text-muted-foreground">Role validation</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Lock className="h-4 w-4" />
              Security
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Protection</div>
            <p className="text-xs text-muted-foreground">Security measures</p>
          </CardContent>
        </Card>
      </div>

      {/* Debug Panel */}
      <DebugPanel
        title="Authentication Debug Tests"
        defaultExpanded={true}
        onTestComplete={(result) => {
          console.log('Authentication Test Completed:', result);
        }}
        onSessionComplete={(session) => {
          console.log('Authentication Debug Session Completed:', session);
        }}
      />

      {/* Test Categories */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UserCheck className="h-5 w-5" />
              Authentication Tests
            </CardTitle>
            <CardDescription>
              User login, session management, and authentication flow
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">User Session Check</p>
                <p className="text-xs text-muted-foreground">Verify active user session</p>
              </div>
              <Badge variant="outline">Session</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Login Flow</p>
                <p className="text-xs text-muted-foreground">Test authentication process</p>
              </div>
              <Badge variant="outline">Flow</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Token Validation</p>
                <p className="text-xs text-muted-foreground">Verify JWT tokens</p>
              </div>
              <Badge variant="outline">Token</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Key className="h-5 w-5" />
              Authorization Tests
            </CardTitle>
            <CardDescription>
              Role-based permissions and access control validation
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Role Permissions</p>
                <p className="text-xs text-muted-foreground">Test role-based access</p>
              </div>
              <Badge variant="outline">RBAC</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Admin Access</p>
                <p className="text-xs text-muted-foreground">Admin-only functionality</p>
              </div>
              <Badge variant="outline">Admin</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Protected Routes</p>
                <p className="text-xs text-muted-foreground">Route access validation</p>
              </div>
              <Badge variant="outline">Routes</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Quick Authentication Checks
          </CardTitle>
          <CardDescription>
            Run common authentication and security tests
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <UserCheck className="h-6 w-6" />
              <span>Session Check</span>
              <span className="text-xs text-muted-foreground">Verify user session</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Key className="h-6 w-6" />
              <span>Permission Test</span>
              <span className="text-xs text-muted-foreground">Check role permissions</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Shield className="h-6 w-6" />
              <span>Security Audit</span>
              <span className="text-xs text-muted-foreground">Security validation</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function createAuthenticationTestSuite() {
  // Basic authentication test
  const authTest = CommonTests.authenticationTest('User Authentication Check');

  // User session test
  const sessionTest = DebugTestBuilder
    .create('User Session Validation')
    .description('Verify active user session and user data')
    .type(DebugTestType.AUTHENTICATION)
    .category('session')
    .execute(async (context) => {
      try {
        context.logger.info('Testing user session validation');
        
        const response = await fetch('/api/auth/me');
        const data = await response.json();
        
        if (!response.ok) {
          return {
            id: context.test.id,
            name: context.test.name,
            type: context.test.type,
            status: DebugTestStatus.FAILED,
            message: `Session validation failed: ${data.error || 'No active session'}`,
            details: { status: response.status, data },
            timestamp: new Date()
          };
        }

        const user = data.user;
        if (!user || !user.id) {
          return {
            id: context.test.id,
            name: context.test.name,
            type: context.test.type,
            status: DebugTestStatus.FAILED,
            message: 'Invalid user data in session',
            details: { user },
            timestamp: new Date()
          };
        }

        context.logger.success(`Session valid for user: ${user.email}`);

        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.SUCCESS,
          message: `Valid session for user: ${user.email} (${user.role})`,
          details: { 
            userId: user.id,
            email: user.email,
            role: user.role,
            sessionValid: true
          },
          timestamp: new Date()
        };
      } catch (error) {
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.FAILED,
          message: error instanceof Error ? error.message : 'Session test failed',
          timestamp: new Date(),
          error: error instanceof Error ? error : new Error(String(error))
        };
      }
    });

  // Role permissions test
  const permissionsTest = DebugTestBuilder
    .create('Role Permissions Check')
    .description('Test role-based access control')
    .type(DebugTestType.PERMISSIONS)
    .category('authorization')
    .execute(async (context) => {
      try {
        context.logger.info('Testing role permissions');
        
        // Test accessing a protected admin endpoint
        const response = await fetch('/api/admin/seed/leave-types', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        const data = await response.json();

        if (response.status === 401) {
          return {
            id: context.test.id,
            name: context.test.name,
            type: context.test.type,
            status: DebugTestStatus.FAILED,
            message: 'User not authenticated',
            details: { status: response.status, data },
            timestamp: new Date()
          };
        }

        if (response.status === 403) {
          return {
            id: context.test.id,
            name: context.test.name,
            type: context.test.type,
            status: DebugTestStatus.WARNING,
            message: 'User lacks admin permissions (this may be expected)',
            details: { status: response.status, data },
            timestamp: new Date()
          };
        }

        // If we get here, user has admin permissions
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.SUCCESS,
          message: 'User has admin permissions',
          details: { 
            status: response.status, 
            hasAdminAccess: true,
            endpoint: '/api/admin/seed/leave-types'
          },
          timestamp: new Date()
        };
      } catch (error) {
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.FAILED,
          message: error instanceof Error ? error.message : 'Permissions test failed',
          timestamp: new Date(),
          error: error instanceof Error ? error : new Error(String(error))
        };
      }
    });

  // Protected route access test
  const protectedRouteTest = DebugTestBuilder
    .create('Protected Route Access')
    .description('Test access to protected API endpoints')
    .type(DebugTestType.API_ENDPOINT)
    .category('authorization')
    .execute(async (context) => {
      try {
        context.logger.info('Testing protected route access');
        
        // Test multiple protected endpoints
        const endpoints = [
          '/api/leave/types',
          '/api/auth/me',
          '/api/users/profile'
        ];

        const results = [];
        
        for (const endpoint of endpoints) {
          try {
            const response = await fetch(endpoint);
            results.push({
              endpoint,
              status: response.status,
              accessible: response.status !== 401 && response.status !== 403
            });
          } catch (error) {
            results.push({
              endpoint,
              status: 'error',
              accessible: false,
              error: error instanceof Error ? error.message : 'Unknown error'
            });
          }
        }

        const accessibleCount = results.filter(r => r.accessible).length;
        const totalCount = results.length;

        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: accessibleCount > 0 ? DebugTestStatus.SUCCESS : DebugTestStatus.FAILED,
          message: `${accessibleCount}/${totalCount} protected endpoints accessible`,
          details: { 
            endpoints: results,
            accessibleCount,
            totalCount
          },
          timestamp: new Date()
        };
      } catch (error) {
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.FAILED,
          message: error instanceof Error ? error.message : 'Protected route test failed',
          timestamp: new Date(),
          error: error instanceof Error ? error : new Error(String(error))
        };
      }
    });

  // Build the test suite
  return DebugTestSuiteBuilder
    .create('Authentication Debug Tests')
    .description('Comprehensive authentication and authorization testing')
    .category('security')
    .addTest(authTest)
    .addTest(sessionTest)
    .addTest(permissionsTest)
    .addTest(protectedRouteTest)
    .build();
}
