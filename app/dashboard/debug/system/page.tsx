/**
 * Debug Services - System Tests Page
 * 
 * Database connectivity, server health, and core system functionality tests
 */

"use client";

import React, { useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  Database, 
  Server, 
  HardDrive, 
  Wifi, 
  FileText, 
  Settings,
  ArrowLeft,
  Activity
} from 'lucide-react';
import Link from 'next/link';
import { 
  DebugServices, 
  DebugPanel,
  DebugTestBuilder,
  DebugTestSuiteBuilder,
  CommonTests,
  DebugTestType,
  DebugTestStatus,
  useDebugServices
} from '@/lib/debug-services';

export default function SystemDebugPage() {
  const { service, isInitialized } = useDebugServices();

  useEffect(() => {
    if (!isInitialized) {
      try {
        // Initialize debug services
        const debugService = DebugServices.initialize({
          environment: 'development',
          uiEnabled: true,
          allowedRoles: ['super_admin', 'system_admin'],
          maxLogEntries: 2000,
          defaultTimeout: 30000
        });

        // Create system-specific test suite
        const systemTestSuite = createSystemTestSuite();

        // Register the test suite
        debugService.registerTestSuite(systemTestSuite);
      } catch (error) {
        console.error('Failed to initialize system debug services:', error);
      }
    }
  }, [isInitialized]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/debug">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Debug Dashboard
            </Link>
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
              <Database className="h-8 w-8 text-primary" />
              System Tests
            </h1>
            <p className="text-muted-foreground mt-2">
              Database connectivity, server health, and core system functionality
            </p>
          </div>
        </div>
        <Badge variant="outline" className="text-xs">
          System Module
        </Badge>
      </div>

      {/* System Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Database className="h-4 w-4" />
              Database
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">MongoDB</div>
            <p className="text-xs text-muted-foreground">Connection & Operations</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Server className="h-4 w-4" />
              Server
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Next.js</div>
            <p className="text-xs text-muted-foreground">Runtime & Performance</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <HardDrive className="h-4 w-4" />
              Storage
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">File System</div>
            <p className="text-xs text-muted-foreground">Read/Write Operations</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Wifi className="h-4 w-4" />
              Network
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Connectivity</div>
            <p className="text-xs text-muted-foreground">External Services</p>
          </CardContent>
        </Card>
      </div>

      {/* Debug Panel */}
      <DebugPanel
        title="System Debug Tests"
        defaultExpanded={true}
        onTestComplete={(result) => {
          console.log('System Test Completed:', result);
        }}
        onSessionComplete={(session) => {
          console.log('System Debug Session Completed:', session);
        }}
      />

      {/* Test Categories */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Database Tests
            </CardTitle>
            <CardDescription>
              MongoDB connection, queries, and data integrity checks
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Connection Test</p>
                <p className="text-xs text-muted-foreground">Verify database connectivity</p>
              </div>
              <Badge variant="outline">Core</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Query Performance</p>
                <p className="text-xs text-muted-foreground">Test query response times</p>
              </div>
              <Badge variant="outline">Performance</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Data Integrity</p>
                <p className="text-xs text-muted-foreground">Validate data consistency</p>
              </div>
              <Badge variant="outline">Validation</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Server className="h-5 w-5" />
              Server Tests
            </CardTitle>
            <CardDescription>
              Runtime environment, memory usage, and system resources
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Memory Usage</p>
                <p className="text-xs text-muted-foreground">Check memory consumption</p>
              </div>
              <Badge variant="outline">Resource</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Environment Variables</p>
                <p className="text-xs text-muted-foreground">Validate configuration</p>
              </div>
              <Badge variant="outline">Config</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">File System</p>
                <p className="text-xs text-muted-foreground">Read/write permissions</p>
              </div>
              <Badge variant="outline">Access</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Quick System Checks
          </CardTitle>
          <CardDescription>
            Run common system diagnostic tests
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Database className="h-6 w-6" />
              <span>Database Health</span>
              <span className="text-xs text-muted-foreground">Connection & performance</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Server className="h-6 w-6" />
              <span>Server Status</span>
              <span className="text-xs text-muted-foreground">Memory & resources</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Settings className="h-6 w-6" />
              <span>Configuration</span>
              <span className="text-xs text-muted-foreground">Environment & settings</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function createSystemTestSuite() {
  // Database connection test
  const dbConnectionTest = CommonTests.databaseConnectionTest('MongoDB Connection');

  // Database query performance test
  const dbPerformanceTest = DebugTestBuilder
    .create('Database Query Performance')
    .description('Test database query response times')
    .type(DebugTestType.PERFORMANCE)
    .category('database')
    .execute(async (context) => {
      try {
        context.logger.info('Testing database query performance');
        
        const startTime = Date.now();
        
        // Test a simple database operation
        const { connectToDatabase } = require('@/lib/backend/database');
        await connectToDatabase();
        
        const mongoose = require('mongoose');
        await mongoose.connection.db.admin().ping();
        
        const duration = Date.now() - startTime;
        const isGood = duration < 100; // Less than 100ms is good
        
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: isGood ? DebugTestStatus.SUCCESS : DebugTestStatus.WARNING,
          message: `Database query completed in ${duration}ms`,
          details: { duration, threshold: 100 },
          timestamp: new Date()
        };
      } catch (error) {
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.FAILED,
          message: error instanceof Error ? error.message : 'Database performance test failed',
          timestamp: new Date(),
          error: error instanceof Error ? error : new Error(String(error))
        };
      }
    });

  // Environment variables test
  const envTest = DebugTestBuilder
    .create('Environment Configuration')
    .description('Validate required environment variables')
    .type(DebugTestType.CUSTOM)
    .category('configuration')
    .execute(async (context) => {
      try {
        context.logger.info('Checking environment configuration');
        
        const requiredVars = ['MONGODB_URI', 'NEXTAUTH_SECRET'];
        const missingVars = requiredVars.filter(varName => !process.env[varName]);
        
        if (missingVars.length > 0) {
          return {
            id: context.test.id,
            name: context.test.name,
            type: context.test.type,
            status: DebugTestStatus.FAILED,
            message: `Missing environment variables: ${missingVars.join(', ')}`,
            details: { required: requiredVars, missing: missingVars },
            timestamp: new Date()
          };
        }
        
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.SUCCESS,
          message: 'All required environment variables are set',
          details: { required: requiredVars, allPresent: true },
          timestamp: new Date()
        };
      } catch (error) {
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.FAILED,
          message: error instanceof Error ? error.message : 'Environment test failed',
          timestamp: new Date(),
          error: error instanceof Error ? error : new Error(String(error))
        };
      }
    });

  // Memory usage test
  const memoryTest = DebugTestBuilder
    .create('Memory Usage Check')
    .description('Monitor server memory consumption')
    .type(DebugTestType.PERFORMANCE)
    .category('server')
    .execute(async (context) => {
      try {
        context.logger.info('Checking memory usage');
        
        const memoryUsage = process.memoryUsage();
        const percentage = Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100);
        
        let status = DebugTestStatus.SUCCESS;
        let message = `Memory usage: ${percentage}% (${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB)`;
        
        if (percentage > 90) {
          status = DebugTestStatus.FAILED;
          message = `Critical memory usage: ${percentage}%`;
        } else if (percentage > 75) {
          status = DebugTestStatus.WARNING;
          message = `High memory usage: ${percentage}%`;
        }
        
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status,
          message,
          details: {
            percentage,
            heapUsed: memoryUsage.heapUsed,
            heapTotal: memoryUsage.heapTotal,
            rss: memoryUsage.rss
          },
          timestamp: new Date()
        };
      } catch (error) {
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.FAILED,
          message: error instanceof Error ? error.message : 'Memory test failed',
          timestamp: new Date(),
          error: error instanceof Error ? error : new Error(String(error))
        };
      }
    });

  // Build the test suite
  return DebugTestSuiteBuilder
    .create('System Debug Tests')
    .description('Comprehensive system functionality and performance tests')
    .category('system')
    .addTest(dbConnectionTest)
    .addTest(dbPerformanceTest)
    .addTest(envTest)
    .addTest(memoryTest)
    .build();
}
