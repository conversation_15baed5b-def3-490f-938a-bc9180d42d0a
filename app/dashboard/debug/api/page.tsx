/**
 * Debug Services - API Endpoints Tests Page
 * 
 * REST API testing, endpoint validation, and integration tests
 */

"use client";

import React, { useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowRightLeft, 
  Globe, 
  CheckCircle, 
  XCircle, 
  ArrowLeft,
  Activity,
  Database,
  Shield,
  Zap
} from 'lucide-react';
import Link from 'next/link';
import { 
  DebugServices, 
  DebugPanel,
  DebugTestBuilder,
  DebugTestSuiteBuilder,
  CommonTests,
  DebugTestType,
  DebugTestStatus,
  useDebugServices
} from '@/lib/debug-services';

export default function APIDebugPage() {
  const { service, isInitialized } = useDebugServices();

  useEffect(() => {
    if (!isInitialized) {
      try {
        // Initialize debug services
        const debugService = DebugServices.initialize({
          environment: 'development',
          uiEnabled: true,
          allowedRoles: ['super_admin', 'system_admin'],
          maxLogEntries: 2000,
          defaultTimeout: 30000
        });

        // Create API specific test suite
        const apiTestSuite = createAPITestSuite();

        // Register the test suite
        debugService.registerTestSuite(apiTestSuite);
      } catch (error) {
        console.error('Failed to initialize API debug services:', error);
      }
    }
  }, [isInitialized]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/debug">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Debug Dashboard
            </Link>
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
              <ArrowRightLeft className="h-8 w-8 text-primary" />
              API Endpoints Tests
            </h1>
            <p className="text-muted-foreground mt-2">
              REST API testing, endpoint validation, and integration tests
            </p>
          </div>
        </div>
        <Badge variant="outline" className="text-xs">
          API Module
        </Badge>
      </div>

      {/* API Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Globe className="h-4 w-4" />
              REST API
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Endpoints</div>
            <p className="text-xs text-muted-foreground">HTTP methods</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Validation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Response</div>
            <p className="text-xs text-muted-foreground">Status codes</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Database className="h-4 w-4" />
              Integration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Data Flow</div>
            <p className="text-xs text-muted-foreground">End-to-end</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Security
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Auth</div>
            <p className="text-xs text-muted-foreground">Protected routes</p>
          </CardContent>
        </Card>
      </div>

      {/* Debug Panel */}
      <DebugPanel
        title="API Endpoints Debug Tests"
        defaultExpanded={true}
        onTestComplete={(result) => {
          console.log('API Test Completed:', result);
        }}
        onSessionComplete={(session) => {
          console.log('API Debug Session Completed:', session);
        }}
      />

      {/* Test Categories */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Core API Tests
            </CardTitle>
            <CardDescription>
              Basic API functionality and response validation
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Health Check</p>
                <p className="text-xs text-muted-foreground">API availability</p>
              </div>
              <Badge variant="outline">Health</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Leave Types API</p>
                <p className="text-xs text-muted-foreground">CRUD operations</p>
              </div>
              <Badge variant="outline">CRUD</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Authentication API</p>
                <p className="text-xs text-muted-foreground">Auth endpoints</p>
              </div>
              <Badge variant="outline">Auth</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Response Validation
            </CardTitle>
            <CardDescription>
              Status codes, headers, and response format validation
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Status Codes</p>
                <p className="text-xs text-muted-foreground">HTTP response codes</p>
              </div>
              <Badge variant="outline">HTTP</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Response Format</p>
                <p className="text-xs text-muted-foreground">JSON structure</p>
              </div>
              <Badge variant="outline">JSON</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Error Handling</p>
                <p className="text-xs text-muted-foreground">Error responses</p>
              </div>
              <Badge variant="outline">Errors</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Quick API Checks
          </CardTitle>
          <CardDescription>
            Run common API endpoint tests
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Globe className="h-6 w-6" />
              <span>Health Check</span>
              <span className="text-xs text-muted-foreground">API availability</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <CheckCircle className="h-6 w-6" />
              <span>Response Test</span>
              <span className="text-xs text-muted-foreground">Validate responses</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Shield className="h-6 w-6" />
              <span>Auth Test</span>
              <span className="text-xs text-muted-foreground">Protected endpoints</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function createAPITestSuite() {
  // Health check API test
  const healthCheckTest = CommonTests.apiEndpointTest('/api/debug-services/health', 200, 'Health Check API');

  // Leave types API test
  const leaveTypesTest = CommonTests.apiEndpointTest('/api/leave/types', 200, 'Leave Types API');

  // Authentication API test
  const authAPITest = CommonTests.apiEndpointTest('/api/auth/me', 200, 'Authentication API');

  // API response format test
  const responseFormatTest = DebugTestBuilder
    .create('API Response Format')
    .description('Validate API response structure and format')
    .type(DebugTestType.API_ENDPOINT)
    .category('validation')
    .execute(async (context) => {
      try {
        context.logger.info('Testing API response format');
        
        const response = await fetch('/api/debug-services/health');
        const data = await response.json();
        
        // Check if response has expected structure
        const hasSuccess = typeof data.success === 'boolean';
        const hasMessage = typeof data.message === 'string';
        const hasTimestamp = data.timestamp && !isNaN(new Date(data.timestamp).getTime());
        
        const validFormat = hasSuccess && hasMessage && hasTimestamp;
        
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: validFormat ? DebugTestStatus.SUCCESS : DebugTestStatus.FAILED,
          message: validFormat 
            ? 'API response format is valid'
            : 'API response format is invalid',
          details: {
            hasSuccess,
            hasMessage,
            hasTimestamp,
            responseStructure: Object.keys(data),
            sampleResponse: data
          },
          timestamp: new Date()
        };
      } catch (error) {
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.FAILED,
          message: error instanceof Error ? error.message : 'Response format test failed',
          timestamp: new Date(),
          error: error instanceof Error ? error : new Error(String(error))
        };
      }
    });

  // Error handling test
  const errorHandlingTest = DebugTestBuilder
    .create('API Error Handling')
    .description('Test API error responses and status codes')
    .type(DebugTestType.API_ENDPOINT)
    .category('error-handling')
    .execute(async (context) => {
      try {
        context.logger.info('Testing API error handling');
        
        // Test a non-existent endpoint
        const response = await fetch('/api/non-existent-endpoint');
        
        // Should return 404
        if (response.status !== 404) {
          return {
            id: context.test.id,
            name: context.test.name,
            type: context.test.type,
            status: DebugTestStatus.WARNING,
            message: `Expected 404 for non-existent endpoint, got ${response.status}`,
            details: { status: response.status },
            timestamp: new Date()
          };
        }
        
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.SUCCESS,
          message: 'API correctly handles non-existent endpoints with 404',
          details: { status: response.status },
          timestamp: new Date()
        };
      } catch (error) {
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.FAILED,
          message: error instanceof Error ? error.message : 'Error handling test failed',
          timestamp: new Date(),
          error: error instanceof Error ? error : new Error(String(error))
        };
      }
    });

  // Multiple endpoints test
  const multipleEndpointsTest = DebugTestBuilder
    .create('Multiple Endpoints Test')
    .description('Test multiple API endpoints simultaneously')
    .type(DebugTestType.API_ENDPOINT)
    .category('integration')
    .execute(async (context) => {
      try {
        context.logger.info('Testing multiple API endpoints');
        
        const endpoints = [
          { url: '/api/debug-services/health', expectedStatus: 200 },
          { url: '/api/leave/types', expectedStatus: 200 },
          { url: '/api/auth/me', expectedStatus: 200 }
        ];
        
        const results = await Promise.allSettled(
          endpoints.map(async (endpoint) => {
            const response = await fetch(endpoint.url);
            return {
              url: endpoint.url,
              status: response.status,
              expectedStatus: endpoint.expectedStatus,
              success: response.status === endpoint.expectedStatus
            };
          })
        );
        
        const successful = results.filter(r => 
          r.status === 'fulfilled' && r.value.success
        ).length;
        
        const total = endpoints.length;
        const successRate = (successful / total) * 100;
        
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: successRate >= 80 ? DebugTestStatus.SUCCESS : DebugTestStatus.WARNING,
          message: `${successful}/${total} endpoints responded correctly (${successRate}%)`,
          details: {
            endpoints: results.map(r => r.status === 'fulfilled' ? r.value : { error: r.reason }),
            successRate,
            successful,
            total
          },
          timestamp: new Date()
        };
      } catch (error) {
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.FAILED,
          message: error instanceof Error ? error.message : 'Multiple endpoints test failed',
          timestamp: new Date(),
          error: error instanceof Error ? error : new Error(String(error))
        };
      }
    });

  // Build the test suite
  return DebugTestSuiteBuilder
    .create('API Endpoints Debug Tests')
    .description('Comprehensive API endpoint testing and validation')
    .category('api')
    .addTest(healthCheckTest)
    .addTest(leaveTypesTest)
    .addTest(authAPITest)
    .addTest(responseFormatTest)
    .addTest(errorHandlingTest)
    .addTest(multipleEndpointsTest)
    .build();
}
