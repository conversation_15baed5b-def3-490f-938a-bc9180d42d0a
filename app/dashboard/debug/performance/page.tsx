/**
 * Debug Services - Performance Tests Page
 * 
 * Response times, load testing, and performance monitoring
 */

"use client";

import React, { useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  Zap, 
  Clock, 
  TrendingUp, 
  Activity, 
  ArrowLeft,
  Timer,
  BarChart3,
  Gauge
} from 'lucide-react';
import Link from 'next/link';
import { 
  DebugServices, 
  DebugPanel,
  DebugTestBuilder,
  DebugTestSuiteBuilder,
  CommonTests,
  DebugTestType,
  DebugTestStatus,
  useDebugServices
} from '@/lib/debug-services';

export default function PerformanceDebugPage() {
  const { service, isInitialized } = useDebugServices();

  useEffect(() => {
    if (!isInitialized) {
      // Initialize debug services
      const debugService = DebugServices.initialize({
        environment: 'development',
        uiEnabled: true,
        allowedRoles: ['super_admin', 'system_admin'],
        maxLogEntries: 2000,
        defaultTimeout: 30000
      });

      // Create performance specific test suite
      const performanceTestSuite = createPerformanceTestSuite();
      
      // Register the test suite
      debugService.registerTestSuite(performanceTestSuite);
    }
  }, [isInitialized]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/debug">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Debug Dashboard
            </Link>
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
              <Zap className="h-8 w-8 text-primary" />
              Performance Tests
            </h1>
            <p className="text-muted-foreground mt-2">
              Response times, load testing, and performance monitoring
            </p>
          </div>
        </div>
        <Badge variant="outline" className="text-xs">
          Performance Module
        </Badge>
      </div>

      {/* Performance Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Response Time
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">API Speed</div>
            <p className="text-xs text-muted-foreground">Endpoint latency</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Throughput
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Load Capacity</div>
            <p className="text-xs text-muted-foreground">Concurrent requests</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Gauge className="h-4 w-4" />
              Memory
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Usage</div>
            <p className="text-xs text-muted-foreground">Memory consumption</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Metrics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Analytics</div>
            <p className="text-xs text-muted-foreground">Performance data</p>
          </CardContent>
        </Card>
      </div>

      {/* Debug Panel */}
      <DebugPanel
        title="Performance Debug Tests"
        defaultExpanded={true}
        onTestComplete={(result) => {
          console.log('Performance Test Completed:', result);
        }}
        onSessionComplete={(session) => {
          console.log('Performance Debug Session Completed:', session);
        }}
      />

      {/* Test Categories */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Response Time Tests
            </CardTitle>
            <CardDescription>
              API endpoint response time and latency measurements
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">API Response Time</p>
                <p className="text-xs text-muted-foreground">Measure endpoint latency</p>
              </div>
              <Badge variant="outline">Latency</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Database Query Speed</p>
                <p className="text-xs text-muted-foreground">Query execution time</p>
              </div>
              <Badge variant="outline">Database</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Page Load Time</p>
                <p className="text-xs text-muted-foreground">Full page rendering</p>
              </div>
              <Badge variant="outline">Frontend</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Load & Stress Tests
            </CardTitle>
            <CardDescription>
              System performance under load and stress conditions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Concurrent Requests</p>
                <p className="text-xs text-muted-foreground">Multiple simultaneous calls</p>
              </div>
              <Badge variant="outline">Load</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Memory Usage</p>
                <p className="text-xs text-muted-foreground">Memory consumption patterns</p>
              </div>
              <Badge variant="outline">Memory</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Resource Limits</p>
                <p className="text-xs text-muted-foreground">System resource usage</p>
              </div>
              <Badge variant="outline">Resources</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Quick Performance Checks
          </CardTitle>
          <CardDescription>
            Run common performance and load tests
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Clock className="h-6 w-6" />
              <span>Response Time</span>
              <span className="text-xs text-muted-foreground">API latency check</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Gauge className="h-6 w-6" />
              <span>Memory Usage</span>
              <span className="text-xs text-muted-foreground">Memory consumption</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <TrendingUp className="h-6 w-6" />
              <span>Load Test</span>
              <span className="text-xs text-muted-foreground">Concurrent requests</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function createPerformanceTestSuite() {
  // API response time test
  const apiResponseTest = CommonTests.performanceTest(
    'API Response Time',
    async () => {
      const response = await fetch('/api/leave/types');
      await response.json();
    },
    1000 // Should respond within 1 second
  );

  // Database query performance test
  const dbPerformanceTest = CommonTests.performanceTest(
    'Database Query Performance',
    async () => {
      const response = await fetch('/api/debug-services/health');
      await response.json();
    },
    500 // Should complete within 500ms
  );

  // Memory usage test
  const memoryTest = DebugTestBuilder
    .create('Memory Usage Monitor')
    .description('Monitor server memory consumption')
    .type(DebugTestType.PERFORMANCE)
    .category('memory')
    .execute(async (context) => {
      try {
        context.logger.info('Monitoring memory usage');
        
        const memoryUsage = process.memoryUsage();
        const percentage = Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100);
        const usedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
        const totalMB = Math.round(memoryUsage.heapTotal / 1024 / 1024);
        
        let status = DebugTestStatus.SUCCESS;
        let message = `Memory usage: ${percentage}% (${usedMB}MB / ${totalMB}MB)`;
        
        if (percentage > 90) {
          status = DebugTestStatus.FAILED;
          message = `Critical memory usage: ${percentage}%`;
        } else if (percentage > 75) {
          status = DebugTestStatus.WARNING;
          message = `High memory usage: ${percentage}%`;
        }
        
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status,
          message,
          details: {
            percentage,
            usedMB,
            totalMB,
            heapUsed: memoryUsage.heapUsed,
            heapTotal: memoryUsage.heapTotal,
            rss: memoryUsage.rss,
            external: memoryUsage.external
          },
          timestamp: new Date()
        };
      } catch (error) {
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.FAILED,
          message: error instanceof Error ? error.message : 'Memory test failed',
          timestamp: new Date(),
          error: error instanceof Error ? error : new Error(String(error))
        };
      }
    });

  // Concurrent requests test
  const concurrentTest = DebugTestBuilder
    .create('Concurrent Requests Test')
    .description('Test system performance under concurrent load')
    .type(DebugTestType.PERFORMANCE)
    .category('load')
    .execute(async (context) => {
      try {
        context.logger.info('Testing concurrent request handling');
        
        const concurrentRequests = 5;
        const endpoint = '/api/debug-services/health';
        
        const startTime = Date.now();
        
        // Create multiple concurrent requests
        const promises = Array.from({ length: concurrentRequests }, () =>
          fetch(endpoint).then(response => ({
            status: response.status,
            ok: response.ok,
            responseTime: Date.now() - startTime
          }))
        );
        
        const results = await Promise.allSettled(promises);
        const totalTime = Date.now() - startTime;
        
        const successful = results.filter(r => 
          r.status === 'fulfilled' && r.value.ok
        ).length;
        
        const averageTime = totalTime / concurrentRequests;
        const successRate = (successful / concurrentRequests) * 100;
        
        let status = DebugTestStatus.SUCCESS;
        let message = `${successful}/${concurrentRequests} requests successful (${successRate}%) in ${totalTime}ms`;
        
        if (successRate < 80) {
          status = DebugTestStatus.FAILED;
          message = `Poor success rate: ${successRate}%`;
        } else if (averageTime > 2000) {
          status = DebugTestStatus.WARNING;
          message = `Slow response time: ${averageTime}ms average`;
        }
        
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status,
          message,
          details: {
            concurrentRequests,
            successful,
            successRate,
            totalTime,
            averageTime,
            results: results.map(r => r.status === 'fulfilled' ? r.value : { error: r.reason })
          },
          timestamp: new Date()
        };
      } catch (error) {
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.FAILED,
          message: error instanceof Error ? error.message : 'Concurrent test failed',
          timestamp: new Date(),
          error: error instanceof Error ? error : new Error(String(error))
        };
      }
    });

  // Build the test suite
  return DebugTestSuiteBuilder
    .create('Performance Debug Tests')
    .description('Comprehensive performance and load testing')
    .category('performance')
    .parallel(true) // Run performance tests in parallel
    .addTest(apiResponseTest)
    .addTest(dbPerformanceTest)
    .addTest(memoryTest)
    .addTest(concurrentTest)
    .build();
}
