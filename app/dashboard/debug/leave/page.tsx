/**
 * Debug Services - Leave Management Tests Page
 * 
 * Leave types, requests, approvals, and business logic validation
 */

"use client";

import React, { useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  FileText, 
  Calendar, 
  Users, 
  CheckCircle, 
  ArrowLeft,
  Activity,
  Database,
  Shield,
  Zap
} from 'lucide-react';
import Link from 'next/link';
import { 
  DebugServices, 
  DebugPanel,
  DebugTestBuilder,
  DebugTestSuiteBuilder,
  CommonTests,
  DebugTestType,
  DebugTestStatus,
  useDebugServices
} from '@/lib/debug-services';

export default function LeaveDebugPage() {
  const { service, isInitialized } = useDebugServices();

  useEffect(() => {
    if (!isInitialized) {
      // Initialize debug services
      const debugService = DebugServices.initialize({
        environment: 'development',
        uiEnabled: true,
        allowedRoles: ['super_admin', 'system_admin', 'hr_director', 'hr_manager'],
        maxLogEntries: 2000,
        defaultTimeout: 30000
      });

      // Create leave management specific test suite
      const leaveTestSuite = createLeaveManagementTestSuite();
      
      // Register the test suite
      debugService.registerTestSuite(leaveTestSuite);
    }
  }, [isInitialized]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/debug">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Debug Dashboard
            </Link>
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
              <FileText className="h-8 w-8 text-primary" />
              Leave Management Tests
            </h1>
            <p className="text-muted-foreground mt-2">
              Leave types, requests, approvals, and business logic validation
            </p>
          </div>
        </div>
        <Badge variant="outline" className="text-xs">
          Application Module
        </Badge>
      </div>

      {/* Leave Management Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Leave Types
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">API & CRUD</div>
            <p className="text-xs text-muted-foreground">Types management</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Requests
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Workflow</div>
            <p className="text-xs text-muted-foreground">Request processing</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Approvals
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Business Logic</div>
            <p className="text-xs text-muted-foreground">Approval rules</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Users className="h-4 w-4" />
              Permissions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Access Control</div>
            <p className="text-xs text-muted-foreground">Role-based access</p>
          </CardContent>
        </Card>
      </div>

      {/* Debug Panel */}
      <DebugPanel
        title="Leave Management Debug Tests"
        defaultExpanded={true}
        onTestComplete={(result) => {
          console.log('Leave Management Test Completed:', result);
        }}
        onSessionComplete={(session) => {
          console.log('Leave Management Debug Session Completed:', session);
        }}
      />

      {/* Test Categories */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Leave Types Tests
            </CardTitle>
            <CardDescription>
              CRUD operations, validation, and seeding functionality
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Get Leave Types API</p>
                <p className="text-xs text-muted-foreground">Test fetching leave types</p>
              </div>
              <Badge variant="outline">API</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Seed Leave Types</p>
                <p className="text-xs text-muted-foreground">Test default data seeding</p>
              </div>
              <Badge variant="outline">Database</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Data Validation</p>
                <p className="text-xs text-muted-foreground">Test input validation rules</p>
              </div>
              <Badge variant="outline">Validation</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">CRUD Operations</p>
                <p className="text-xs text-muted-foreground">Create, read, update, delete</p>
              </div>
              <Badge variant="outline">Operations</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Security & Permissions
            </CardTitle>
            <CardDescription>
              Authentication, authorization, and role-based access
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Authentication Check</p>
                <p className="text-xs text-muted-foreground">Verify user authentication</p>
              </div>
              <Badge variant="outline">Auth</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Role Permissions</p>
                <p className="text-xs text-muted-foreground">Test role-based access</p>
              </div>
              <Badge variant="outline">Permissions</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Admin Operations</p>
                <p className="text-xs text-muted-foreground">Admin-only functionality</p>
              </div>
              <Badge variant="outline">Admin</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Quick Leave Management Checks
          </CardTitle>
          <CardDescription>
            Run common leave management diagnostic tests
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <FileText className="h-6 w-6" />
              <span>Leave Types Health</span>
              <span className="text-xs text-muted-foreground">API & database checks</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Database className="h-6 w-6" />
              <span>Seed Default Types</span>
              <span className="text-xs text-muted-foreground">Initialize leave types</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Shield className="h-6 w-6" />
              <span>Permission Check</span>
              <span className="text-xs text-muted-foreground">Validate access rights</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function createLeaveManagementTestSuite() {
  // Authentication test
  const authTest = CommonTests.authenticationTest('Leave Management Auth');

  // Get leave types API test
  const getLeaveTypesTest = DebugTestBuilder
    .create('Get Leave Types API')
    .description('Test fetching leave types from API')
    .type(DebugTestType.API_ENDPOINT)
    .category('leave-types')
    .execute(async (context) => {
      try {
        context.logger.info('Testing GET /api/leave/types endpoint');
        
        const response = await fetch('/api/leave/types');
        const data = await response.json();
        
        if (!response.ok) {
          return {
            id: context.test.id,
            name: context.test.name,
            type: context.test.type,
            status: DebugTestStatus.FAILED,
            message: `API returned ${response.status}: ${data.error || 'Unknown error'}`,
            details: { status: response.status, data },
            timestamp: new Date()
          };
        }

        const leaveTypes = data.data || [];
        context.logger.success(`Found ${leaveTypes.length} leave types`);

        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.SUCCESS,
          message: `Successfully fetched ${leaveTypes.length} leave types`,
          details: { 
            count: leaveTypes.length, 
            leaveTypes: leaveTypes.slice(0, 3) // Show first 3 for brevity
          },
          timestamp: new Date()
        };
      } catch (error) {
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.FAILED,
          message: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date(),
          error: error instanceof Error ? error : new Error(String(error))
        };
      }
    });

  // Seed leave types test
  const seedLeaveTypesTest = DebugTestBuilder
    .create('Seed Leave Types')
    .description('Test seeding default leave types')
    .type(DebugTestType.DATABASE)
    .category('leave-types')
    .execute(async (context) => {
      try {
        context.logger.info('Testing POST /api/admin/seed/leave-types endpoint');
        
        const response = await fetch('/api/admin/seed/leave-types', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        const data = await response.json();

        if (!response.ok) {
          return {
            id: context.test.id,
            name: context.test.name,
            type: context.test.type,
            status: DebugTestStatus.FAILED,
            message: `Seed failed: ${data.error || 'Unknown error'}`,
            details: { status: response.status, data },
            timestamp: new Date()
          };
        }

        context.logger.success('Leave types seeded successfully');

        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.SUCCESS,
          message: data.message || 'Leave types seeded successfully',
          details: data.data,
          timestamp: new Date()
        };
      } catch (error) {
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.FAILED,
          message: error instanceof Error ? error.message : 'Seed test failed',
          timestamp: new Date(),
          error: error instanceof Error ? error : new Error(String(error))
        };
      }
    });

  // Leave type validation test
  const validationTest = DebugTestBuilder
    .create('Leave Type Validation')
    .description('Test leave type data validation')
    .type(DebugTestType.CUSTOM)
    .category('leave-types')
    .execute(async (context) => {
      try {
        context.logger.info('Testing leave type validation logic');
        
        // Test creating a leave type with invalid data
        const invalidLeaveType = {
          name: '', // Invalid: empty name
          code: 'INVALID_CODE_TOO_LONG', // Invalid: too long
          defaultDays: -5, // Invalid: negative days
          isPaid: 'not_boolean', // Invalid: not boolean
        };

        const response = await fetch('/api/leave/types', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(invalidLeaveType),
        });

        const data = await response.json();

        // We expect this to fail with validation errors
        if (response.ok) {
          return {
            id: context.test.id,
            name: context.test.name,
            type: context.test.type,
            status: DebugTestStatus.FAILED,
            message: 'Validation test failed: Invalid data was accepted',
            details: { response: data },
            timestamp: new Date()
          };
        }

        // Check if we got proper validation errors
        const hasValidationErrors = response.status === 400 && data.error;
        
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: hasValidationErrors ? DebugTestStatus.SUCCESS : DebugTestStatus.WARNING,
          message: hasValidationErrors 
            ? 'Validation correctly rejected invalid data'
            : 'Validation response unclear',
          details: { 
            status: response.status, 
            error: data.error,
            expectedValidationFailure: true
          },
          timestamp: new Date()
        };
      } catch (error) {
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.FAILED,
          message: error instanceof Error ? error.message : 'Validation test failed',
          timestamp: new Date(),
          error: error instanceof Error ? error : new Error(String(error))
        };
      }
    });

  // Performance test for leave types loading
  const performanceTest = CommonTests.performanceTest(
    'Leave Types Load Performance',
    async () => {
      const response = await fetch('/api/leave/types');
      await response.json();
    },
    2000 // Should load within 2 seconds
  );

  // Build the test suite
  return DebugTestSuiteBuilder
    .create('Leave Management Debug Tests')
    .description('Comprehensive testing for leave management functionality')
    .category('leave-management')
    .addTest(authTest)
    .addTest(getLeaveTypesTest)
    .addTest(seedLeaveTypesTest)
    .addTest(validationTest)
    .addTest(performanceTest)
    .build();
}
