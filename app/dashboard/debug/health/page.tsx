/**
 * Debug Services - Health Checks Page
 * 
 * Comprehensive system health monitoring and diagnostics
 */

"use client";

import React, { useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  AlertCircle, 
  Heart, 
  CheckCircle, 
  XCircle, 
  ArrowLeft,
  Activity,
  Database,
  Shield,
  Server,
  Wifi
} from 'lucide-react';
import Link from 'next/link';
import { 
  DebugServices, 
  DebugPanel,
  DebugTestBuilder,
  DebugTestSuiteBuilder,
  CommonTests,
  DebugTestType,
  DebugTestStatus,
  useDebugServices
} from '@/lib/debug-services';

export default function HealthDebugPage() {
  const { service, isInitialized } = useDebugServices();

  useEffect(() => {
    if (!isInitialized) {
      // Initialize debug services
      const debugService = DebugServices.initialize({
        environment: 'development',
        uiEnabled: true,
        allowedRoles: ['super_admin', 'system_admin', 'hr_director', 'hr_manager'],
        maxLogEntries: 2000,
        defaultTimeout: 30000
      });

      // Create health check specific test suite
      const healthTestSuite = createHealthCheckTestSuite();
      
      // Register the test suite
      debugService.registerTestSuite(healthTestSuite);
    }
  }, [isInitialized]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/debug">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Debug Dashboard
            </Link>
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
              <AlertCircle className="h-8 w-8 text-primary" />
              Health Checks
            </h1>
            <p className="text-muted-foreground mt-2">
              Comprehensive system health monitoring and diagnostics
            </p>
          </div>
        </div>
        <Badge variant="outline" className="text-xs">
          Health Module
        </Badge>
      </div>

      {/* Health Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Heart className="h-4 w-4" />
              System Health
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Overall</div>
            <p className="text-xs text-muted-foreground">System status</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Database className="h-4 w-4" />
              Database
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Connection</div>
            <p className="text-xs text-muted-foreground">DB health</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Server className="h-4 w-4" />
              Server
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Resources</div>
            <p className="text-xs text-muted-foreground">Server health</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Wifi className="h-4 w-4" />
              Services
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">External</div>
            <p className="text-xs text-muted-foreground">Service health</p>
          </CardContent>
        </Card>
      </div>

      {/* Debug Panel */}
      <DebugPanel
        title="Health Check Debug Tests"
        defaultExpanded={true}
        onTestComplete={(result) => {
          console.log('Health Check Test Completed:', result);
        }}
        onSessionComplete={(session) => {
          console.log('Health Check Debug Session Completed:', session);
        }}
      />

      {/* Test Categories */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Heart className="h-5 w-5" />
              System Health Tests
            </CardTitle>
            <CardDescription>
              Overall system health and availability checks
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">System Status</p>
                <p className="text-xs text-muted-foreground">Overall health check</p>
              </div>
              <Badge variant="outline">Health</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Service Availability</p>
                <p className="text-xs text-muted-foreground">Core services status</p>
              </div>
              <Badge variant="outline">Services</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Resource Usage</p>
                <p className="text-xs text-muted-foreground">Memory and CPU</p>
              </div>
              <Badge variant="outline">Resources</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Infrastructure Health
            </CardTitle>
            <CardDescription>
              Database, network, and external service health
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Database Health</p>
                <p className="text-xs text-muted-foreground">Connection and queries</p>
              </div>
              <Badge variant="outline">Database</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">Network Connectivity</p>
                <p className="text-xs text-muted-foreground">External connections</p>
              </div>
              <Badge variant="outline">Network</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium text-sm">File System</p>
                <p className="text-xs text-muted-foreground">Storage access</p>
              </div>
              <Badge variant="outline">Storage</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Quick Health Checks
          </CardTitle>
          <CardDescription>
            Run comprehensive system health diagnostics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Heart className="h-6 w-6" />
              <span>System Health</span>
              <span className="text-xs text-muted-foreground">Overall status</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Database className="h-6 w-6" />
              <span>Database Check</span>
              <span className="text-xs text-muted-foreground">DB connectivity</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Server className="h-6 w-6" />
              <span>Resource Monitor</span>
              <span className="text-xs text-muted-foreground">Memory & CPU</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function createHealthCheckTestSuite() {
  // System health check test
  const systemHealthTest = DebugTestBuilder
    .create('System Health Check')
    .description('Comprehensive system health validation')
    .type(DebugTestType.HEALTH_CHECK)
    .category('system')
    .execute(async (context) => {
      try {
        context.logger.info('Running comprehensive system health check');
        
        const response = await fetch('/api/debug-services/health', {
          method: 'POST' // Use POST for comprehensive check
        });
        
        const data = await response.json();
        
        if (!response.ok) {
          return {
            id: context.test.id,
            name: context.test.name,
            type: context.test.type,
            status: DebugTestStatus.FAILED,
            message: `Health check failed: ${data.error || 'Unknown error'}`,
            details: { status: response.status, data },
            timestamp: new Date()
          };
        }

        const healthStatus = data.data.status;
        const healthChecks = data.data.healthChecks || [];
        
        let status = DebugTestStatus.SUCCESS;
        if (healthStatus === 'unhealthy') {
          status = DebugTestStatus.FAILED;
        } else if (healthStatus === 'degraded') {
          status = DebugTestStatus.WARNING;
        }

        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status,
          message: `System health: ${healthStatus}`,
          details: {
            overallStatus: healthStatus,
            healthChecks,
            checkCount: healthChecks.length
          },
          timestamp: new Date()
        };
      } catch (error) {
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.FAILED,
          message: error instanceof Error ? error.message : 'System health check failed',
          timestamp: new Date(),
          error: error instanceof Error ? error : new Error(String(error))
        };
      }
    });

  // Database health test
  const dbHealthTest = CommonTests.databaseConnectionTest('Database Health Check');

  // Service availability test
  const serviceAvailabilityTest = DebugTestBuilder
    .create('Service Availability Check')
    .description('Check availability of core application services')
    .type(DebugTestType.HEALTH_CHECK)
    .category('services')
    .execute(async (context) => {
      try {
        context.logger.info('Checking service availability');
        
        const services = [
          { name: 'Authentication', endpoint: '/api/auth/me' },
          { name: 'Leave Types', endpoint: '/api/leave/types' },
          { name: 'Debug Services', endpoint: '/api/debug-services/health' }
        ];
        
        const results = await Promise.allSettled(
          services.map(async (service) => {
            try {
              const response = await fetch(service.endpoint);
              return {
                name: service.name,
                endpoint: service.endpoint,
                status: response.status,
                available: response.status < 500
              };
            } catch (error) {
              return {
                name: service.name,
                endpoint: service.endpoint,
                status: 'error',
                available: false,
                error: error instanceof Error ? error.message : 'Unknown error'
              };
            }
          })
        );
        
        const serviceResults = results.map(r => 
          r.status === 'fulfilled' ? r.value : { error: r.reason }
        );
        
        const availableServices = serviceResults.filter(s => s.available).length;
        const totalServices = services.length;
        const availabilityRate = (availableServices / totalServices) * 100;
        
        let status = DebugTestStatus.SUCCESS;
        if (availabilityRate < 50) {
          status = DebugTestStatus.FAILED;
        } else if (availabilityRate < 100) {
          status = DebugTestStatus.WARNING;
        }
        
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status,
          message: `${availableServices}/${totalServices} services available (${availabilityRate}%)`,
          details: {
            services: serviceResults,
            availableServices,
            totalServices,
            availabilityRate
          },
          timestamp: new Date()
        };
      } catch (error) {
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.FAILED,
          message: error instanceof Error ? error.message : 'Service availability check failed',
          timestamp: new Date(),
          error: error instanceof Error ? error : new Error(String(error))
        };
      }
    });

  // Resource usage health test
  const resourceHealthTest = DebugTestBuilder
    .create('Resource Usage Health')
    .description('Monitor system resource usage and health')
    .type(DebugTestType.HEALTH_CHECK)
    .category('resources')
    .execute(async (context) => {
      try {
        context.logger.info('Checking resource usage health');
        
        const memoryUsage = process.memoryUsage();
        const uptime = process.uptime();
        
        const memoryPercentage = Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100);
        const memoryMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
        const totalMemoryMB = Math.round(memoryUsage.heapTotal / 1024 / 1024);
        
        let status = DebugTestStatus.SUCCESS;
        let message = `Memory: ${memoryPercentage}% (${memoryMB}MB), Uptime: ${Math.round(uptime)}s`;
        
        if (memoryPercentage > 90) {
          status = DebugTestStatus.FAILED;
          message = `Critical memory usage: ${memoryPercentage}%`;
        } else if (memoryPercentage > 75) {
          status = DebugTestStatus.WARNING;
          message = `High memory usage: ${memoryPercentage}%`;
        }
        
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status,
          message,
          details: {
            memory: {
              percentage: memoryPercentage,
              usedMB: memoryMB,
              totalMB: totalMemoryMB,
              heapUsed: memoryUsage.heapUsed,
              heapTotal: memoryUsage.heapTotal,
              rss: memoryUsage.rss
            },
            uptime: {
              seconds: uptime,
              formatted: `${Math.floor(uptime / 3600)}h ${Math.floor((uptime % 3600) / 60)}m ${Math.floor(uptime % 60)}s`
            },
            nodeVersion: process.version,
            platform: process.platform
          },
          timestamp: new Date()
        };
      } catch (error) {
        return {
          id: context.test.id,
          name: context.test.name,
          type: context.test.type,
          status: DebugTestStatus.FAILED,
          message: error instanceof Error ? error.message : 'Resource health check failed',
          timestamp: new Date(),
          error: error instanceof Error ? error : new Error(String(error))
        };
      }
    });

  // Build the test suite
  return DebugTestSuiteBuilder
    .create('Health Check Debug Tests')
    .description('Comprehensive system health monitoring and diagnostics')
    .category('health')
    .addTest(systemHealthTest)
    .addTest(dbHealthTest)
    .addTest(serviceAvailabilityTest)
    .addTest(resourceHealthTest)
    .build();
}
