/**
 * Debug Services - Layout Component
 *
 * Consistent layout and styling for all debug pages
 */

import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Debug Services - Kawandama HR System',
  description: 'Comprehensive debugging and testing tools for system monitoring and troubleshooting',
};

interface DebugLayoutProps {
  children: React.ReactNode;
}

export default function DebugLayout({ children }: DebugLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Debug Services Context Provider */}
      <div className="debug-services-context">
        {children}
      </div>
    </div>
  );
}
