/**
 * Debug Services - Layout Component
 * 
 * Consistent layout and styling for all debug pages
 */

import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Debug Services - Kawandama HR System',
  description: 'Comprehensive debugging and testing tools for system monitoring and troubleshooting',
};

interface DebugLayoutProps {
  children: React.ReactNode;
}

export default function DebugLayout({ children }: DebugLayoutProps) {
  return (
    <div className="debug-services-layout">
      {/* Debug Services specific styles and context */}
      <style jsx global>{`
        .debug-services-layout {
          min-height: 100vh;
          background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }
        
        .debug-services-layout .debug-panel {
          border: 1px solid #e2e8f0;
          border-radius: 8px;
          background: white;
          box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }
        
        .debug-services-layout .debug-test-running {
          animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        .debug-services-layout .debug-status-success {
          color: #059669;
          background-color: #d1fae5;
          border-color: #a7f3d0;
        }
        
        .debug-services-layout .debug-status-warning {
          color: #d97706;
          background-color: #fef3c7;
          border-color: #fde68a;
        }
        
        .debug-services-layout .debug-status-error {
          color: #dc2626;
          background-color: #fee2e2;
          border-color: #fecaca;
        }
        
        .debug-services-layout .debug-log-entry {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 0.875rem;
          line-height: 1.5;
        }
        
        .debug-services-layout .debug-metric-card {
          transition: all 0.2s ease-in-out;
        }
        
        .debug-services-layout .debug-metric-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        
        @keyframes pulse {
          0%, 100% {
            opacity: 1;
          }
          50% {
            opacity: .5;
          }
        }
        
        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
          .debug-services-layout {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
          }
          
          .debug-services-layout .debug-panel {
            background: #1e293b;
            border-color: #334155;
          }
          
          .debug-services-layout .debug-status-success {
            color: #10b981;
            background-color: #064e3b;
            border-color: #065f46;
          }
          
          .debug-services-layout .debug-status-warning {
            color: #f59e0b;
            background-color: #78350f;
            border-color: #92400e;
          }
          
          .debug-services-layout .debug-status-error {
            color: #ef4444;
            background-color: #7f1d1d;
            border-color: #991b1b;
          }
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
          .debug-services-layout .debug-panel {
            margin: 0.5rem;
            border-radius: 6px;
          }
          
          .debug-services-layout .debug-metric-card {
            margin-bottom: 1rem;
          }
        }
        
        /* Print styles */
        @media print {
          .debug-services-layout {
            background: white !important;
          }
          
          .debug-services-layout .debug-panel {
            border: 1px solid #000 !important;
            box-shadow: none !important;
          }
          
          .debug-services-layout .debug-test-running {
            animation: none !important;
          }
        }
      `}</style>
      
      {/* Debug Services Context Provider */}
      <div className="debug-services-context">
        {children}
      </div>
    </div>
  );
}
