/**
 * Debug Services - Main Dashboard Page
 * 
 * Overview page showing all available debug modules and system status
 */

"use client";

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Bug, 
  Database, 
  Shield, 
  FileText, 
  Zap, 
  ArrowRightLeft, 
  AlertCircle,
  LayoutDashboard,
  CheckCircle,
  XCircle,
  Clock,
  Activity,
  Users,
  Settings,
  TrendingUp,
  Server,
  Wifi,
  HardDrive
} from 'lucide-react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { DebugServices } from '@/lib/debug-services';

interface DebugModule {
  id: string;
  title: string;
  description: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  status: 'healthy' | 'warning' | 'error' | 'unknown';
  lastChecked?: Date;
  testCount?: number;
  category: 'system' | 'application' | 'performance' | 'security';
}

interface SystemMetric {
  label: string;
  value: string;
  status: 'good' | 'warning' | 'critical';
  icon: React.ComponentType<{ className?: string }>;
}

export default function DebugDashboardPage() {
  const [systemMetrics, setSystemMetrics] = useState<SystemMetric[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize debug services
  useEffect(() => {
    DebugServices.quickSetup('development');
  }, []);

  // Load system metrics
  useEffect(() => {
    const loadSystemMetrics = async () => {
      try {
        const response = await fetch('/api/debug-services/health');
        const data = await response.json();
        
        if (data.success) {
          const healthChecks = data.data.healthChecks || [];
          const systemInfo = data.data.systemInfo;
          
          const metrics: SystemMetric[] = [
            {
              label: 'System Status',
              value: data.data.status === 'healthy' ? 'Healthy' : 'Issues Detected',
              status: data.data.status === 'healthy' ? 'good' : 'warning',
              icon: data.data.status === 'healthy' ? CheckCircle : AlertCircle
            },
            {
              label: 'Database',
              value: healthChecks.find((h: any) => h.service === 'database')?.status || 'Unknown',
              status: healthChecks.find((h: any) => h.service === 'database')?.status === 'healthy' ? 'good' : 'critical',
              icon: Database
            },
            {
              label: 'Authentication',
              value: healthChecks.find((h: any) => h.service === 'authentication')?.status || 'Unknown',
              status: healthChecks.find((h: any) => h.service === 'authentication')?.status === 'healthy' ? 'good' : 'warning',
              icon: Shield
            }
          ];

          if (systemInfo) {
            metrics.push({
              label: 'Memory Usage',
              value: `${systemInfo.memory.percentage}%`,
              status: systemInfo.memory.percentage < 80 ? 'good' : systemInfo.memory.percentage < 95 ? 'warning' : 'critical',
              icon: HardDrive
            });
          }

          setSystemMetrics(metrics);
        }
      } catch (error) {
        console.error('Failed to load system metrics:', error);
        setSystemMetrics([
          {
            label: 'System Status',
            value: 'Unable to connect',
            status: 'critical',
            icon: XCircle
          }
        ]);
      } finally {
        setIsLoading(false);
      }
    };

    loadSystemMetrics();
  }, []);

  const debugModules: DebugModule[] = [
    {
      id: 'system',
      title: 'System Tests',
      description: 'Database connectivity, server health, and core system functionality',
      href: '/dashboard/debug/system',
      icon: Database,
      status: 'healthy',
      testCount: 8,
      category: 'system'
    },
    {
      id: 'auth',
      title: 'Authentication',
      description: 'User authentication, permissions, and security validation',
      href: '/dashboard/debug/auth',
      icon: Shield,
      status: 'healthy',
      testCount: 5,
      category: 'security'
    },
    {
      id: 'leave',
      title: 'Leave Management',
      description: 'Leave types, requests, approvals, and business logic validation',
      href: '/dashboard/debug/leave',
      icon: FileText,
      status: 'healthy',
      testCount: 12,
      category: 'application'
    },
    {
      id: 'performance',
      title: 'Performance',
      description: 'Response times, load testing, and performance monitoring',
      href: '/dashboard/debug/performance',
      icon: Zap,
      status: 'warning',
      testCount: 6,
      category: 'performance'
    },
    {
      id: 'api',
      title: 'API Endpoints',
      description: 'REST API testing, endpoint validation, and integration tests',
      href: '/dashboard/debug/api',
      icon: ArrowRightLeft,
      status: 'healthy',
      testCount: 15,
      category: 'system'
    },
    {
      id: 'health',
      title: 'Health Checks',
      description: 'Comprehensive system health monitoring and diagnostics',
      href: '/dashboard/debug/health',
      icon: AlertCircle,
      status: 'healthy',
      testCount: 4,
      category: 'system'
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'good':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'error':
      case 'critical':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'good':
        return 'border-green-500 bg-green-50';
      case 'warning':
        return 'border-yellow-500 bg-yellow-50';
      case 'error':
      case 'critical':
        return 'border-red-500 bg-red-50';
      default:
        return 'border-gray-500 bg-gray-50';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'system':
        return <Server className="h-5 w-5" />;
      case 'application':
        return <LayoutDashboard className="h-5 w-5" />;
      case 'performance':
        return <TrendingUp className="h-5 w-5" />;
      case 'security':
        return <Shield className="h-5 w-5" />;
      default:
        return <Bug className="h-5 w-5" />;
    }
  };

  const groupedModules = debugModules.reduce((acc, module) => {
    if (!acc[module.category]) {
      acc[module.category] = [];
    }
    acc[module.category].push(module);
    return acc;
  }, {} as Record<string, DebugModule[]>);

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
            <Bug className="h-8 w-8 text-primary" />
            Debug Services
          </h1>
          <p className="text-muted-foreground mt-2">
            Comprehensive debugging and testing tools for system monitoring and troubleshooting
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">
            {debugModules.length} modules
          </Badge>
          <Badge variant="outline" className="text-xs">
            {debugModules.reduce((sum, m) => sum + (m.testCount || 0), 0)} tests
          </Badge>
        </div>
      </div>

      {/* System Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            System Overview
          </CardTitle>
          <CardDescription>
            Real-time system health and performance metrics
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                <span className="text-sm text-muted-foreground">Loading system metrics...</span>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {systemMetrics.map((metric, index) => (
                <div key={index} className="flex items-center gap-3 p-3 rounded-lg border">
                  <metric.icon className={cn(
                    "h-5 w-5",
                    metric.status === 'good' && "text-green-500",
                    metric.status === 'warning' && "text-yellow-500",
                    metric.status === 'critical' && "text-red-500"
                  )} />
                  <div>
                    <p className="text-sm font-medium">{metric.label}</p>
                    <p className="text-xs text-muted-foreground">{metric.value}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Debug Modules by Category */}
      {Object.entries(groupedModules).map(([category, modules]) => (
        <div key={category}>
          <div className="flex items-center gap-2 mb-4">
            {getCategoryIcon(category)}
            <h2 className="text-xl font-semibold capitalize">{category} Debug Modules</h2>
            <Badge variant="secondary" className="text-xs">
              {modules.length} modules
            </Badge>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {modules.map((module) => (
              <Card key={module.id} className={cn("border-l-4", getStatusColor(module.status))}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <module.icon className="h-5 w-5 text-primary" />
                      <CardTitle className="text-lg">{module.title}</CardTitle>
                    </div>
                    {getStatusIcon(module.status)}
                  </div>
                  <CardDescription className="text-sm">
                    {module.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      {module.testCount && (
                        <span>{module.testCount} tests</span>
                      )}
                      {module.lastChecked && (
                        <span>Last: {module.lastChecked.toLocaleTimeString()}</span>
                      )}
                    </div>
                    <Button asChild size="sm">
                      <Link href={module.href}>
                        Open Module
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      ))}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Quick Actions
          </CardTitle>
          <CardDescription>
            Common debugging tasks and system operations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2" asChild>
              <Link href="/dashboard/debug/health">
                <AlertCircle className="h-6 w-6" />
                <span>Run Health Check</span>
                <span className="text-xs text-muted-foreground">Comprehensive system check</span>
              </Link>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2" asChild>
              <Link href="/dashboard/debug/performance">
                <Zap className="h-6 w-6" />
                <span>Performance Test</span>
                <span className="text-xs text-muted-foreground">Check response times</span>
              </Link>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2" asChild>
              <Link href="/dashboard/debug/system">
                <Database className="h-6 w-6" />
                <span>System Diagnostics</span>
                <span className="text-xs text-muted-foreground">Database and connectivity</span>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
