"use client";

import { Logo } from "@/components/ui/logo";
import Image from "next/image";

export default function LogoDemoPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Kawandama Hills Plantation Logo Options
          </h1>
          <p className="text-lg text-gray-600">
            Different logo variations with green color schemes
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          
          {/* Original PNG with Green Filter */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 text-center">Original PNG + Green Filter</h3>
            <div className="flex justify-center mb-4">
              <Image
                src="/images/logo.png"
                alt="Original Logo with Green Filter"
                width={120}
                height={120}
                className="logo-green-filter"
              />
            </div>
            <p className="text-sm text-gray-600 text-center">
              Uses CSS filters to apply green tinting to the original logo
            </p>
          </div>

          {/* New SVG Green Logo */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 text-center">Custom SVG Green Logo</h3>
            <div className="flex justify-center mb-4">
              <Image
                src="/images/kawandama-logo-green.svg"
                alt="Custom Green SVG Logo"
                width={120}
                height={120}
                className="transition-transform hover:scale-105"
              />
            </div>
            <p className="text-sm text-gray-600 text-center">
              Custom-designed SVG with plantation theme and green colors
            </p>
          </div>

          {/* Text-based Logo */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 text-center">Text-based Logo</h3>
            <div className="flex justify-center mb-4">
              <Logo size="lg" showImage={false} />
            </div>
            <p className="text-sm text-gray-600 text-center">
              Clean text-based logo with green color scheme
            </p>
          </div>

          {/* Logo with SVG Image */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 text-center">Complete Logo (SVG)</h3>
            <div className="flex justify-center mb-4">
              <Logo size="lg" showImage={true} logoType="svg" />
            </div>
            <p className="text-sm text-gray-600 text-center">
              Full logo with custom SVG image and text
            </p>
          </div>

          {/* Logo with PNG Image */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 text-center">Complete Logo (PNG + Filter)</h3>
            <div className="flex justify-center mb-4">
              <Logo size="lg" showImage={true} logoType="png" />
            </div>
            <p className="text-sm text-gray-600 text-center">
              Full logo with original PNG and green filter
            </p>
          </div>

          {/* Favicon Preview */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 text-center">Favicon (32x32)</h3>
            <div className="flex justify-center mb-4">
              <Image
                src="/favicon-green.svg"
                alt="Green Favicon"
                width={64}
                height={64}
                className="border border-gray-200 rounded"
              />
            </div>
            <p className="text-sm text-gray-600 text-center">
              Simplified green favicon for browser tabs
            </p>
          </div>
        </div>

        {/* Color Palette */}
        <div className="mt-16 bg-white rounded-lg shadow-lg p-8">
          <h2 className="text-2xl font-bold text-center mb-8">Green Color Palette</h2>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="w-full h-20 bg-green-400 rounded-lg mb-2"></div>
              <p className="text-sm font-medium">Green 400</p>
              <p className="text-xs text-gray-500">#4ade80</p>
            </div>
            <div className="text-center">
              <div className="w-full h-20 bg-green-500 rounded-lg mb-2"></div>
              <p className="text-sm font-medium">Green 500</p>
              <p className="text-xs text-gray-500">#22c55e</p>
            </div>
            <div className="text-center">
              <div className="w-full h-20 bg-green-600 rounded-lg mb-2"></div>
              <p className="text-sm font-medium">Green 600</p>
              <p className="text-xs text-gray-500">#16a34a</p>
            </div>
            <div className="text-center">
              <div className="w-full h-20 bg-green-700 rounded-lg mb-2"></div>
              <p className="text-sm font-medium">Green 700</p>
              <p className="text-xs text-gray-500">#15803d</p>
            </div>
            <div className="text-center">
              <div className="w-full h-20 bg-green-800 rounded-lg mb-2"></div>
              <p className="text-sm font-medium">Green 800</p>
              <p className="text-xs text-gray-500">#166534</p>
            </div>
          </div>
        </div>

        {/* Usage Instructions */}
        <div className="mt-12 bg-blue-50 rounded-lg p-8">
          <h2 className="text-2xl font-bold text-center mb-6">How to Use</h2>
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-3">For SVG Logo:</h3>
              <pre className="bg-gray-800 text-green-400 p-4 rounded text-sm overflow-x-auto">
{`<Logo 
  size="lg" 
  showImage={true} 
  logoType="svg" 
/>`}
              </pre>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-3">For PNG with Green Filter:</h3>
              <pre className="bg-gray-800 text-green-400 p-4 rounded text-sm overflow-x-auto">
{`<Logo 
  size="lg" 
  showImage={true} 
  logoType="png" 
/>`}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
