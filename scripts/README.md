# Next.js API Route Fixer

This utility script automatically fixes common issues in Next.js API routes, particularly focusing on:

1. **Authentication system compatibility**
   - Converts next-auth session objects to custom auth user objects
   - Fixes permission checks and user references

2. **TypeScript type errors**
   - Fixes parameter mismatches in API handlers (req/request)
   - Corrects dynamic route parameter types for App Router
   - Adds proper typing for external libraries (XLSX, etc.)

3. **Next.js App Router compatibility**
   - Updates route handler parameter types
   - Fixes response handling for binary data

## Usage

```bash
# Fix all API routes
node scripts/nextjs-api-route-fixer.js

# Fix only specific module routes
node scripts/nextjs-api-route-fixer.js app/api/users
```

## Common Issues Fixed

The script automatically detects and fixes the following issues:

### Authentication System Compatibility

- Replaces `getServerSession` imports with `getCurrentUser`
- Removes unnecessary `authOptions` imports
- Converts `session` objects to `user` objects
- Updates permission checks to use the correct user object

### TypeScript Type Errors

- Fixes parameter mismatches between function declarations and usage
- Corrects dynamic route parameter types for Next.js App Router
- Adds proper typing for external libraries like XLSX

### Next.js App Router Compatibility

- Updates route handler parameter types to use `Promise<{ params: { id: string } }>` for dynamic routes
- Fixes response handling for binary data using ReadableStream

## Safety Features

- Creates backups of all modified files in the `backups/api-route-fixes` directory
- Only modifies files that match specific patterns to avoid unintended changes
- Provides detailed logs of all changes made

## Integration with Build Process

You can integrate this script into your build process by adding it to your package.json scripts:

```json
"scripts": {
  "fix-api-routes": "node scripts/nextjs-api-route-fixer.js",
  "prebuild": "npm run fix-api-routes",
  "build": "next build"
}
```

This will automatically run the fixer before each build, ensuring your API routes are compatible with the latest Next.js requirements.

## Troubleshooting

If you encounter any issues:

1. Check the console output for specific error messages
2. Restore from backups if needed (located in `backups/api-route-fixes`)
3. Run the script with a more specific directory to narrow down problematic files

---

## 🔐 Super Admin Creation Script

### Overview
Creates a super admin user for the Kawandama Hills Plantation Management System with full administrative privileges.

### Files
- `create-super-admin.ts` - TypeScript version (recommended)
- `create-super-admin.js` - JavaScript version (fallback)

### Usage

#### Method 1: Using npm scripts (Recommended)
```bash
# TypeScript version (recommended)
npm run create-super-admin

# JavaScript version (fallback)
npm run create-super-admin:js
```

#### Method 2: Direct execution
```bash
# TypeScript version
npx tsx scripts/create-super-admin.ts

# JavaScript version
node scripts/create-super-admin.js
```

### Super Admin Credentials
The script creates a super admin user with the following credentials:

- **Email**: `<EMAIL>`
- **Password**: `@Admin2020`
- **Role**: `SUPER_ADMIN`
- **Status**: `ACTIVE`
- **Name**: Winston Mhango
- **Department**: Administration
- **Position**: System Administrator

### Features
- ✅ **Duplicate Check**: Checks if user already exists
- ✅ **Update Option**: Allows updating existing user if found
- ✅ **Password Hashing**: Automatically hashes password using bcrypt
- ✅ **Database Connection**: Handles MongoDB connection automatically
- ✅ **Error Handling**: Comprehensive error handling and logging
- ✅ **Interactive**: Prompts for confirmation before updates
- ✅ **Security Settings**: Configures appropriate security settings for admin

### Prerequisites
1. **Environment Variables**: Ensure `.env` file is configured with `MONGODB_URI` or `DATABASE_URL`
2. **Dependencies**: All required packages should be installed with `npm install`

### Security Notes
- 🔒 **Change Default Password**: After first login, change the default password
- 🔐 **Secure Storage**: Store credentials securely and don't commit them to version control
- 👥 **Limited Access**: Only create super admin accounts when necessary

## License

MIT
