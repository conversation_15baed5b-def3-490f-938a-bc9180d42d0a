#!/usr/bin/env node

/**
 * Debug script for API endpoints
 * 
 * This script helps debug API endpoint issues by testing the routes
 * and checking for common problems.
 * 
 * Usage: node scripts/debug-api-endpoints.js
 */

const { config } = require('dotenv');
const { join } = require('path');
const fs = require('fs');
const path = require('path');

// Load environment variables
config({ path: join(__dirname, '..', '.env') });

/**
 * Check API route file structure
 */
async function checkAPIRouteStructure() {
  console.log('🔍 Checking API Route Structure...\n');
  
  try {
    const apiRoutes = [
      {
        path: 'app/api/dashboard/organization/route.ts',
        name: 'Organization API',
        expectedMethods: ['GET', 'POST', 'PUT', 'PATCH']
      },
      {
        path: 'app/api/dashboard/leave/route.ts',
        name: 'Leave API',
        expectedMethods: ['GET', 'POST', 'PUT']
      },
      {
        path: 'app/api/dashboard/approvals/route.ts',
        name: 'Approvals API',
        expectedMethods: ['GET', 'POST', 'PUT']
      }
    ];

    let allRoutesValid = true;

    for (const route of apiRoutes) {
      const routePath = path.join(__dirname, '..', route.path);
      
      if (fs.existsSync(routePath)) {
        console.log(`✅ ${route.name} file exists`);
        
        const content = fs.readFileSync(routePath, 'utf8');
        
        // Check for export statements
        route.expectedMethods.forEach(method => {
          if (content.includes(`export async function ${method}`)) {
            console.log(`  ✅ ${method} method exported`);
          } else {
            console.log(`  ❌ ${method} method missing or not exported`);
            allRoutesValid = false;
          }
        });
        
        // Check for proper imports
        if (content.includes('NextRequest') && content.includes('NextResponse')) {
          console.log(`  ✅ Next.js imports present`);
        } else {
          console.log(`  ❌ Missing Next.js imports`);
          allRoutesValid = false;
        }
        
        // Check for runtime export
        if (content.includes("export const runtime = 'nodejs'")) {
          console.log(`  ✅ Runtime configuration present`);
        } else {
          console.log(`  ⚠️ Runtime configuration missing (may cause issues)`);
        }
        
        // Check for syntax errors (basic)
        const openBraces = (content.match(/{/g) || []).length;
        const closeBraces = (content.match(/}/g) || []).length;
        
        if (openBraces === closeBraces) {
          console.log(`  ✅ Balanced braces`);
        } else {
          console.log(`  ❌ Unbalanced braces (${openBraces} open, ${closeBraces} close)`);
          allRoutesValid = false;
        }
        
        console.log(''); // Add spacing
      } else {
        console.log(`❌ ${route.name} file does not exist at ${route.path}`);
        allRoutesValid = false;
      }
    }
    
    return allRoutesValid;
  } catch (error) {
    console.error('❌ API route structure check failed:', error.message);
    return false;
  }
}

/**
 * Check for common API issues
 */
async function checkCommonAPIIssues() {
  console.log('🐛 Checking for Common API Issues...\n');
  
  try {
    const issues = [];
    
    // Check if Next.js is properly configured
    const nextConfigPath = path.join(__dirname, '..', 'next.config.js');
    const nextConfigMjsPath = path.join(__dirname, '..', 'next.config.mjs');
    
    if (fs.existsSync(nextConfigPath) || fs.existsSync(nextConfigMjsPath)) {
      console.log('✅ Next.js config file exists');
    } else {
      console.log('⚠️ Next.js config file not found');
      issues.push('Missing Next.js configuration');
    }
    
    // Check package.json for required dependencies
    const packageJsonPath = path.join(__dirname, '..', 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      
      const requiredDeps = ['next', 'react'];
      const missingDeps = requiredDeps.filter(dep => 
        !packageJson.dependencies?.[dep] && !packageJson.devDependencies?.[dep]
      );
      
      if (missingDeps.length === 0) {
        console.log('✅ Required dependencies present');
      } else {
        console.log(`❌ Missing dependencies: ${missingDeps.join(', ')}`);
        issues.push(`Missing dependencies: ${missingDeps.join(', ')}`);
      }
    }
    
    // Check for TypeScript configuration
    const tsConfigPath = path.join(__dirname, '..', 'tsconfig.json');
    if (fs.existsSync(tsConfigPath)) {
      console.log('✅ TypeScript configuration exists');
    } else {
      console.log('⚠️ TypeScript configuration missing');
      issues.push('Missing TypeScript configuration');
    }
    
    // Check for environment variables
    const envPath = path.join(__dirname, '..', '.env');
    const envLocalPath = path.join(__dirname, '..', '.env.local');
    
    if (fs.existsSync(envPath) || fs.existsSync(envLocalPath)) {
      console.log('✅ Environment file exists');
    } else {
      console.log('⚠️ Environment file missing');
      issues.push('Missing environment configuration');
    }
    
    // Check for database connection
    const dbConfigPaths = [
      'lib/backend/database.ts',
      'lib/backend/database/index.ts',
      'lib/database.ts'
    ];
    
    let dbConfigFound = false;
    for (const dbPath of dbConfigPaths) {
      if (fs.existsSync(path.join(__dirname, '..', dbPath))) {
        console.log(`✅ Database configuration found at ${dbPath}`);
        dbConfigFound = true;
        break;
      }
    }
    
    if (!dbConfigFound) {
      console.log('⚠️ Database configuration not found');
      issues.push('Missing database configuration');
    }
    
    console.log(`\n📋 Issues found: ${issues.length}`);
    if (issues.length > 0) {
      issues.forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue}`);
      });
    }
    
    return issues.length === 0;
  } catch (error) {
    console.error('❌ Common issues check failed:', error.message);
    return false;
  }
}

/**
 * Generate API test commands
 */
async function generateAPITestCommands() {
  console.log('\n🧪 API Test Commands...\n');
  
  const baseUrl = process.env.NEXTAUTH_URL || process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
  
  console.log('You can test these API endpoints manually:');
  console.log('');
  
  console.log('1. Organization API:');
  console.log(`   curl -X GET "${baseUrl}/api/dashboard/organization?type=departments"`);
  console.log('');
  
  console.log('2. Leave API:');
  console.log(`   curl -X GET "${baseUrl}/api/dashboard/leave?limit=5"`);
  console.log('');
  
  console.log('3. Approvals API:');
  console.log(`   curl -X GET "${baseUrl}/api/dashboard/approvals?limit=5"`);
  console.log('');
  
  console.log('Note: These will fail without proper authentication cookies.');
  console.log('Test them in your browser\'s developer tools console instead:');
  console.log('');
  console.log('fetch("/api/dashboard/organization?type=departments")');
  console.log('  .then(r => r.json())');
  console.log('  .then(console.log)');
  console.log('  .catch(console.error)');
}

/**
 * Check for import/export issues
 */
async function checkImportExportIssues() {
  console.log('\n📦 Checking Import/Export Issues...\n');
  
  try {
    const apiFiles = [
      'app/api/dashboard/organization/route.ts',
      'app/api/dashboard/leave/route.ts',
      'app/api/dashboard/approvals/route.ts'
    ];

    let allImportsValid = true;

    for (const apiFile of apiFiles) {
      const apiPath = path.join(__dirname, '..', apiFile);
      
      if (fs.existsSync(apiPath)) {
        const content = fs.readFileSync(apiPath, 'utf8');
        const fileName = apiFile.split('/').pop();
        
        console.log(`📄 Checking ${fileName}:`);
        
        // Check for service imports
        const serviceImports = [
          '@/lib/services/dashboard/',
          '@/lib/backend/auth/auth',
          '@/lib/backend/auth/permissions'
        ];
        
        serviceImports.forEach(importPath => {
          if (content.includes(importPath)) {
            console.log(`  ✅ ${importPath} imported`);
          } else {
            console.log(`  ⚠️ ${importPath} not imported`);
          }
        });
        
        // Check for potential circular imports
        if (content.includes('import') && content.includes('export')) {
          console.log(`  ✅ Has imports and exports`);
        } else {
          console.log(`  ❌ Missing imports or exports`);
          allImportsValid = false;
        }
        
        console.log('');
      }
    }
    
    return allImportsValid;
  } catch (error) {
    console.error('❌ Import/export check failed:', error.message);
    return false;
  }
}

/**
 * Main debug function
 */
async function runDebug() {
  console.log('🚀 API Endpoint Debug Tool');
  console.log('=' .repeat(30));
  
  const results = {
    routeStructure: false,
    commonIssues: false,
    importExports: false
  };
  
  // Run all checks
  results.routeStructure = await checkAPIRouteStructure();
  results.commonIssues = await checkCommonAPIIssues();
  results.importExports = await checkImportExportIssues();
  
  // Generate test commands
  await generateAPITestCommands();
  
  // Summary
  console.log('\n📊 Debug Results Summary');
  console.log('=' .repeat(30));
  
  const passed = Object.values(results).filter(Boolean).length;
  const total = Object.keys(results).length;
  
  console.log(`✅ Route Structure: ${results.routeStructure ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Common Issues: ${results.commonIssues ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Import/Exports: ${results.importExports ? 'PASS' : 'FAIL'}`);
  
  console.log(`\n🎯 Overall: ${passed}/${total} checks passed`);
  
  if (passed === total) {
    console.log('🎉 All checks passed! API structure looks good.');
  } else {
    console.log('⚠️ Some issues found. Please review the details above.');
  }
  
  // Troubleshooting tips
  console.log('\n💡 Troubleshooting Tips:');
  console.log('1. Make sure your development server is running: npm run dev');
  console.log('2. Check browser console for detailed error messages');
  console.log('3. Verify you\'re logged in and have proper authentication');
  console.log('4. Check Network tab in browser dev tools for actual API responses');
  console.log('5. Try accessing API endpoints directly in browser');
  
  // Common solutions
  console.log('\n🔧 Common Solutions:');
  console.log('• Restart development server: npm run dev');
  console.log('• Clear browser cache and cookies');
  console.log('• Check if database is running and accessible');
  console.log('• Verify environment variables are set correctly');
  console.log('• Check for TypeScript compilation errors');
  
  process.exit(passed === total ? 0 : 1);
}

// Run the debug tool
if (require.main === module) {
  runDebug().catch((error) => {
    console.error('❌ Debug tool failed:', error);
    process.exit(1);
  });
}

module.exports = { runDebug };
