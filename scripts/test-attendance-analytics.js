#!/usr/bin/env node

/**
 * Test script for Attendance Analytics Service
 * 
 * This script tests the attendance analytics service to ensure it can
 * calculate real attendance rates from the database.
 * 
 * Usage: node scripts/test-attendance-analytics.js
 */

const { config } = require('dotenv');
const { join } = require('path');
const fs = require('fs');
const path = require('path');

// Load environment variables
config({ path: join(__dirname, '..', '.env') });

/**
 * Test attendance analytics service file
 */
async function testAttendanceAnalyticsService() {
  console.log('🔍 Testing Attendance Analytics Service...\n');
  
  try {
    const servicePath = path.join(__dirname, '..', 'lib', 'services', 'dashboard', 'attendance-analytics-service.ts');
    
    if (fs.existsSync(servicePath)) {
      console.log('✅ Attendance analytics service file exists');
      
      const content = fs.readFileSync(servicePath, 'utf8');
      
      // Check for main service class
      if (content.includes('export class AttendanceAnalyticsService')) {
        console.log('✅ AttendanceAnalyticsService class is exported');
      } else {
        console.log('❌ AttendanceAnalyticsService class is missing');
        return false;
      }
      
      // Check for required methods
      const requiredMethods = [
        'calculateAttendanceRate',
        'compareAttendancePeriods',
        'getDashboardAttendanceRate'
      ];
      
      requiredMethods.forEach(method => {
        if (content.includes(method)) {
          console.log(`✅ ${method} method is present`);
        } else {
          console.log(`❌ ${method} method is missing`);
        }
      });
      
      // Check for database integration
      if (content.includes('Attendance.aggregate') && content.includes('connectToDatabase')) {
        console.log('✅ Database integration is present');
      } else {
        console.log('❌ Database integration is missing');
      }
      
      // Check for proper calculations
      const calculations = [
        'attendanceRate',
        'totalWorkingDays',
        'totalPresentDays',
        'changePercentage'
      ];
      
      calculations.forEach(calc => {
        if (content.includes(calc)) {
          console.log(`✅ ${calc} calculation is present`);
        } else {
          console.log(`❌ ${calc} calculation is missing`);
        }
      });
      
      // Check for service instance export
      if (content.includes('export const attendanceAnalyticsService')) {
        console.log('✅ Service instance is exported');
      } else {
        console.log('❌ Service instance export is missing');
      }
      
      return true;
    } else {
      console.log('❌ Attendance analytics service file does not exist');
      return false;
    }
  } catch (error) {
    console.error('❌ Attendance analytics service test failed:', error.message);
    return false;
  }
}

/**
 * Test attendance model integration
 */
async function testAttendanceModelIntegration() {
  console.log('\n📊 Testing Attendance Model Integration...\n');
  
  try {
    const modelPath = path.join(__dirname, '..', 'models', 'attendance', 'Attendance.ts');
    
    if (fs.existsSync(modelPath)) {
      console.log('✅ Attendance model file exists');
      
      const content = fs.readFileSync(modelPath, 'utf8');
      
      // Check for required fields
      const requiredFields = [
        'employeeId',
        'date',
        'status',
        'checkIn',
        'checkOut',
        'workHours'
      ];
      
      requiredFields.forEach(field => {
        if (content.includes(field)) {
          console.log(`✅ ${field} field is present`);
        } else {
          console.log(`❌ ${field} field is missing`);
        }
      });
      
      // Check for status enum
      const statusValues = [
        'present',
        'absent',
        'late',
        'half-day',
        'leave',
        'holiday',
        'weekend'
      ];
      
      statusValues.forEach(status => {
        if (content.includes(`'${status}'`)) {
          console.log(`✅ ${status} status is defined`);
        } else {
          console.log(`❌ ${status} status is missing`);
        }
      });
      
      // Check for indexes
      if (content.includes('index') && content.includes('employeeId') && content.includes('date')) {
        console.log('✅ Database indexes are configured');
      } else {
        console.log('❌ Database indexes are missing');
      }
      
      return true;
    } else {
      console.log('❌ Attendance model file does not exist');
      return false;
    }
  } catch (error) {
    console.error('❌ Attendance model integration test failed:', error.message);
    return false;
  }
}

/**
 * Test API integration
 */
async function testAPIIntegration() {
  console.log('\n🔗 Testing API Integration...\n');
  
  try {
    const apiPath = path.join(__dirname, '..', 'app', 'api', 'dashboard', 'stats', 'route.ts');
    
    if (fs.existsSync(apiPath)) {
      console.log('✅ Dashboard stats API file exists');
      
      const content = fs.readFileSync(apiPath, 'utf8');
      
      // Check for attendance service import
      if (content.includes('attendanceAnalyticsService')) {
        console.log('✅ Attendance analytics service is imported');
      } else {
        console.log('❌ Attendance analytics service import is missing');
        return false;
      }
      
      // Check for real attendance calculation
      if (content.includes('getDashboardAttendanceRate')) {
        console.log('✅ Real attendance calculation is used');
      } else {
        console.log('❌ Real attendance calculation is missing');
        return false;
      }
      
      // Check for fallback handling
      if (content.includes('try') && content.includes('catch') && content.includes('attendanceError')) {
        console.log('✅ Fallback error handling is present');
      } else {
        console.log('❌ Fallback error handling is missing');
      }
      
      // Check if static data is removed
      if (!content.includes('const attendanceRate = 96.8')) {
        console.log('✅ Static attendance data has been replaced');
      } else {
        console.log('❌ Static attendance data is still present');
      }
      
      return true;
    } else {
      console.log('❌ Dashboard stats API file does not exist');
      return false;
    }
  } catch (error) {
    console.error('❌ API integration test failed:', error.message);
    return false;
  }
}

/**
 * Test calculation logic
 */
async function testCalculationLogic() {
  console.log('\n🧮 Testing Calculation Logic...\n');
  
  try {
    const servicePath = path.join(__dirname, '..', 'lib', 'services', 'dashboard', 'attendance-analytics-service.ts');
    
    if (fs.existsSync(servicePath)) {
      const content = fs.readFileSync(servicePath, 'utf8');
      
      // Check for proper attendance rate calculation
      if (content.includes('(totalPresentDays / totalWorkingDays) * 100')) {
        console.log('✅ Attendance rate calculation formula is correct');
      } else {
        console.log('❌ Attendance rate calculation formula is missing or incorrect');
      }
      
      // Check for weekend/holiday exclusion
      if (content.includes('weekend') && content.includes('holiday')) {
        console.log('✅ Weekend and holiday exclusion logic is present');
      } else {
        console.log('❌ Weekend and holiday exclusion logic is missing');
      }
      
      // Check for present status inclusion
      if (content.includes("['present', 'late', 'half-day']")) {
        console.log('✅ Present status inclusion logic is correct');
      } else {
        console.log('❌ Present status inclusion logic is missing or incorrect');
      }
      
      // Check for change percentage calculation
      if (content.includes('changePercentage') && content.includes('previousPeriod')) {
        console.log('✅ Change percentage calculation is present');
      } else {
        console.log('❌ Change percentage calculation is missing');
      }
      
      // Check for department breakdown
      if (content.includes('departmentBreakdown') && content.includes('department')) {
        console.log('✅ Department breakdown calculation is present');
      } else {
        console.log('❌ Department breakdown calculation is missing');
      }
      
      return true;
    } else {
      console.log('❌ Attendance analytics service file does not exist');
      return false;
    }
  } catch (error) {
    console.error('❌ Calculation logic test failed:', error.message);
    return false;
  }
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🚀 Attendance Analytics Service Tests');
  console.log('=' .repeat(40));
  
  const results = {
    attendanceAnalyticsService: false,
    attendanceModelIntegration: false,
    apiIntegration: false,
    calculationLogic: false
  };
  
  // Run all tests
  results.attendanceAnalyticsService = await testAttendanceAnalyticsService();
  results.attendanceModelIntegration = await testAttendanceModelIntegration();
  results.apiIntegration = await testAPIIntegration();
  results.calculationLogic = await testCalculationLogic();
  
  // Summary
  console.log('\n📊 Test Results Summary');
  console.log('=' .repeat(30));
  
  const passed = Object.values(results).filter(Boolean).length;
  const total = Object.keys(results).length;
  
  console.log(`✅ Attendance Analytics Service: ${results.attendanceAnalyticsService ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Attendance Model Integration: ${results.attendanceModelIntegration ? 'PASS' : 'FAIL'}`);
  console.log(`✅ API Integration: ${results.apiIntegration ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Calculation Logic: ${results.calculationLogic ? 'PASS' : 'FAIL'}`);
  
  console.log(`\n🎯 Overall: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! Attendance analytics service is ready.');
  } else {
    console.log('⚠️ Some tests failed. Please review the issues above.');
  }
  
  // Implementation details
  console.log('\n📋 Attendance Analytics Implementation:');
  console.log('• ✅ Real attendance rate calculation from database records');
  console.log('• ✅ Excludes weekends and holidays from working days');
  console.log('• ✅ Includes present, late, and half-day as attendance');
  console.log('• ✅ Month-over-month change percentage calculation');
  console.log('• ✅ Department-wise attendance breakdown');
  console.log('• ✅ Fallback handling for missing attendance data');
  console.log('• ✅ Comprehensive error handling and logging');
  
  // Data requirements
  console.log('\n📊 Data Requirements:');
  console.log('• Attendance records with employee ID, date, and status');
  console.log('• Employee records linked to departments');
  console.log('• Check-in/check-out times for work hours calculation');
  console.log('• Leave records integration for accurate attendance');
  
  // Usage instructions
  console.log('\n💡 Usage Instructions:');
  console.log('1. Ensure attendance records are populated in the database');
  console.log('2. The service automatically calculates rates for current vs previous month');
  console.log('3. Fallback to 0% attendance rate if no data is available');
  console.log('4. Dashboard will show real-time attendance metrics');
  
  process.exit(passed === total ? 0 : 1);
}

// Run the tests
if (require.main === module) {
  runTests().catch((error) => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = { runTests };
