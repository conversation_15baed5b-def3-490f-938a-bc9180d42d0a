#!/usr/bin/env node

/**
 * Test script for Empty State Component Integration
 * 
 * This script tests the reusable empty state component implementation by:
 * 1. Checking if the empty state component exists
 * 2. Verifying component integrations use the empty state
 * 3. Testing empty state variants and configurations
 * 
 * Usage: node scripts/test-empty-state.js
 */

const { config } = require('dotenv');
const { join } = require('path');
const fs = require('fs');
const path = require('path');

// Load environment variables
config({ path: join(__dirname, '..', '.env') });

/**
 * Test the empty state component
 */
async function testEmptyStateComponent() {
  console.log('🧪 Testing Empty State Component...\n');
  
  try {
    const componentPath = path.join(__dirname, '..', 'components', 'ui', 'empty-state.tsx');
    
    if (fs.existsSync(componentPath)) {
      console.log('✅ Empty State component file exists');
      
      const content = fs.readFileSync(componentPath, 'utf8');
      
      // Check for main component
      if (content.includes('export function EmptyState')) {
        console.log('✅ EmptyState component is exported');
      } else {
        console.log('❌ EmptyState component export is missing');
      }
      
      // Check for pre-configured variants
      if (content.includes('export const EmptyStates')) {
        console.log('✅ EmptyStates variants are exported');
      } else {
        console.log('❌ EmptyStates variants are missing');
      }
      
      // Check for hook
      if (content.includes('export function useEmptyState')) {
        console.log('✅ useEmptyState hook is exported');
      } else {
        console.log('❌ useEmptyState hook is missing');
      }
      
      // Check for required props
      const requiredFeatures = [
        'icon',
        'title',
        'description',
        'action',
        'size',
        'showBackground'
      ];
      
      requiredFeatures.forEach(feature => {
        if (content.includes(feature)) {
          console.log(`✅ ${feature} prop is supported`);
        } else {
          console.log(`⚠️ ${feature} prop might be missing`);
        }
      });
      
      // Check for pre-configured variants
      const variants = [
        'NoData',
        'LoadingFailed',
        'NoSearchResults',
        'ComingSoon',
        'AccessDenied'
      ];
      
      variants.forEach(variant => {
        if (content.includes(variant)) {
          console.log(`✅ ${variant} variant is available`);
        } else {
          console.log(`❌ ${variant} variant is missing`);
        }
      });
      
      return true;
    } else {
      console.log('❌ Empty State component file does not exist');
      return false;
    }
  } catch (error) {
    console.error('❌ Empty State component test failed:', error.message);
    return false;
  }
}

/**
 * Test component integrations
 */
async function testComponentIntegrations() {
  console.log('\n🎨 Testing Component Integrations...\n');
  
  try {
    const components = [
      {
        path: 'components/upcoming-reviews.tsx',
        name: 'Upcoming Reviews'
      },
      {
        path: 'components/leave-management.tsx',
        name: 'Leave Management'
      },
      {
        path: 'components/department-breakdown.tsx',
        name: 'Department Breakdown'
      },
      {
        path: 'components/recruitment-pipeline.tsx',
        name: 'Recruitment Pipeline'
      },
      {
        path: 'components/performance-metrics.tsx',
        name: 'Performance Metrics'
      }
    ];

    let allComponentsPass = true;

    for (const component of components) {
      const componentPath = path.join(__dirname, '..', component.path);
      
      if (fs.existsSync(componentPath)) {
        console.log(`✅ ${component.name} component exists`);
        
        const content = fs.readFileSync(componentPath, 'utf8');
        
        // Check for empty state import
        if (content.includes('import { EmptyState, useEmptyState }') || 
            content.includes('from "@/components/ui/empty-state"')) {
          console.log(`✅ ${component.name} imports EmptyState`);
        } else {
          console.log(`❌ ${component.name} does not import EmptyState`);
          allComponentsPass = false;
        }
        
        // Check for useEmptyState hook usage
        if (content.includes('useEmptyState()')) {
          console.log(`✅ ${component.name} uses useEmptyState hook`);
        } else {
          console.log(`❌ ${component.name} does not use useEmptyState hook`);
          allComponentsPass = false;
        }
        
        // Check for getEmptyState usage
        if (content.includes('getEmptyState(')) {
          console.log(`✅ ${component.name} uses getEmptyState method`);
        } else {
          console.log(`❌ ${component.name} does not use getEmptyState method`);
          allComponentsPass = false;
        }
        
        // Check if old empty state patterns are removed
        const oldPatterns = [
          '<div className="py-6 text-center">',
          '<AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />',
          'No data found',
          'Try Again'
        ];
        
        let hasOldPatterns = false;
        oldPatterns.forEach(pattern => {
          if (content.includes(pattern)) {
            hasOldPatterns = true;
          }
        });
        
        if (!hasOldPatterns) {
          console.log(`✅ ${component.name} has removed old empty state patterns`);
        } else {
          console.log(`⚠️ ${component.name} might still have old empty state patterns`);
        }
        
        console.log(''); // Add spacing
      } else {
        console.log(`❌ ${component.name} component does not exist`);
        allComponentsPass = false;
      }
    }
    
    return allComponentsPass;
  } catch (error) {
    console.error('❌ Component integrations test failed:', error.message);
    return false;
  }
}

/**
 * Test TypeScript interfaces
 */
async function testTypeScriptInterfaces() {
  console.log('📝 Testing TypeScript Interfaces...\n');
  
  try {
    const componentPath = path.join(__dirname, '..', 'components', 'ui', 'empty-state.tsx');
    
    if (fs.existsSync(componentPath)) {
      const content = fs.readFileSync(componentPath, 'utf8');
      
      // Check for interface definitions
      if (content.includes('interface EmptyStateProps')) {
        console.log('✅ EmptyStateProps interface is defined');
      } else {
        console.log('❌ EmptyStateProps interface is missing');
      }
      
      // Check for proper TypeScript usage
      if (content.includes('LucideIcon')) {
        console.log('✅ LucideIcon type is used for icons');
      } else {
        console.log('❌ LucideIcon type is missing');
      }
      
      // Check for proper prop types
      const propTypes = [
        'title: string',
        'description?: string',
        'action?:',
        'size?:',
        'className?: string'
      ];
      
      propTypes.forEach(propType => {
        if (content.includes(propType)) {
          console.log(`✅ ${propType} is properly typed`);
        } else {
          console.log(`⚠️ ${propType} typing might be missing`);
        }
      });
      
      return true;
    } else {
      console.log('❌ Empty State component file does not exist');
      return false;
    }
  } catch (error) {
    console.error('❌ TypeScript interfaces test failed:', error.message);
    return false;
  }
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🚀 Empty State Component Integration Tests');
  console.log('=' .repeat(45));
  
  const results = {
    emptyStateComponent: false,
    componentIntegrations: false,
    typeScriptInterfaces: false
  };
  
  // Run all tests
  results.emptyStateComponent = await testEmptyStateComponent();
  results.componentIntegrations = await testComponentIntegrations();
  results.typeScriptInterfaces = await testTypeScriptInterfaces();
  
  // Summary
  console.log('\n📊 Test Results Summary');
  console.log('=' .repeat(30));
  
  const passed = Object.values(results).filter(Boolean).length;
  const total = Object.keys(results).length;
  
  console.log(`✅ Empty State Component: ${results.emptyStateComponent ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Component Integrations: ${results.componentIntegrations ? 'PASS' : 'FAIL'}`);
  console.log(`✅ TypeScript Interfaces: ${results.typeScriptInterfaces ? 'PASS' : 'FAIL'}`);
  
  console.log(`\n🎯 Overall: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! Empty state component is ready and integrated.');
  } else {
    console.log('⚠️ Some tests failed. Please review the issues above.');
  }
  
  // Benefits summary
  console.log('\n📋 Empty State Component Benefits:');
  console.log('1. ✅ Consistent empty state design across all dashboard components');
  console.log('2. ✅ Reusable component with multiple size variants (sm, md, lg)');
  console.log('3. ✅ Pre-configured variants for common scenarios');
  console.log('4. ✅ Built-in error handling with retry functionality');
  console.log('5. ✅ TypeScript support with proper prop types');
  console.log('6. ✅ Customizable icons, titles, descriptions, and actions');
  console.log('7. ✅ useEmptyState hook for simplified state management');
  console.log('8. ✅ Responsive design that works on all screen sizes');
  
  // Usage examples
  console.log('\n💡 Usage Examples:');
  console.log('• Basic: <EmptyState icon={Users} title="No data" />');
  console.log('• With action: <EmptyState title="Error" action={{label: "Retry", onClick: retry}} />');
  console.log('• Using hook: getEmptyState(loading, error, hasData, options)');
  console.log('• Pre-configured: <EmptyStates.NoData title="Custom title" />');
  
  process.exit(passed === total ? 0 : 1);
}

// Run the tests
if (require.main === module) {
  runTests().catch((error) => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = { runTests };
