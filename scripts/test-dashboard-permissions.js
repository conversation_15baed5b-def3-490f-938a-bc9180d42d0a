#!/usr/bin/env node

/**
 * Test script for Dashboard API Permissions
 * 
 * This script tests the dashboard API permissions to ensure they are inclusive enough
 * for dashboard viewing while maintaining appropriate security.
 * 
 * Usage: node scripts/test-dashboard-permissions.js
 */

const { config } = require('dotenv');
const { join } = require('path');
const fs = require('fs');
const path = require('path');

// Load environment variables
config({ path: join(__dirname, '..', '.env') });

/**
 * Test API permissions configuration
 */
async function testAPIPermissions() {
  console.log('🔐 Testing Dashboard API Permissions...\n');
  
  try {
    const apiFiles = [
      {
        path: 'app/api/dashboard/organization/route.ts',
        name: 'Organization API',
        endpoints: ['GET', 'POST', 'PUT', 'PATCH']
      },
      {
        path: 'app/api/dashboard/leave/route.ts',
        name: 'Leave API',
        endpoints: ['GET', 'POST', 'PUT']
      },
      {
        path: 'app/api/dashboard/approvals/route.ts',
        name: 'Approvals API',
        endpoints: ['GET', 'POST', 'PUT']
      }
    ];

    let allPermissionsPass = true;

    for (const apiFile of apiFiles) {
      const apiPath = path.join(__dirname, '..', apiFile.path);
      
      if (fs.existsSync(apiPath)) {
        console.log(`✅ ${apiFile.name} file exists`);
        
        const content = fs.readFileSync(apiPath, 'utf8');
        
        // Check for inclusive permissions
        const inclusiveRoles = [
          'UserRole.EMPLOYEE',
          'UserRole.MANAGER',
          'UserRole.FINANCE_OFFICER'
        ];
        
        let hasInclusivePermissions = false;
        inclusiveRoles.forEach(role => {
          if (content.includes(role)) {
            hasInclusivePermissions = true;
          }
        });
        
        if (hasInclusivePermissions) {
          console.log(`✅ ${apiFile.name} has inclusive permissions for dashboard viewing`);
        } else {
          console.log(`⚠️ ${apiFile.name} might have restrictive permissions`);
        }
        
        // Check for authentication
        if (content.includes('getCurrentUser')) {
          console.log(`✅ ${apiFile.name} has authentication checks`);
        } else {
          console.log(`❌ ${apiFile.name} missing authentication checks`);
          allPermissionsPass = false;
        }
        
        // Check for permission validation
        if (content.includes('hasRequiredPermissions')) {
          console.log(`✅ ${apiFile.name} has permission validation`);
        } else {
          console.log(`❌ ${apiFile.name} missing permission validation`);
          allPermissionsPass = false;
        }
        
        // Check for proper error messages
        if (content.includes('Insufficient permissions')) {
          console.log(`✅ ${apiFile.name} has proper permission error messages`);
        } else {
          console.log(`❌ ${apiFile.name} missing permission error messages`);
          allPermissionsPass = false;
        }
        
        // Check for custom auth (not next-auth)
        if (content.includes('next-auth')) {
          console.log(`❌ ${apiFile.name} uses next-auth instead of custom auth`);
          allPermissionsPass = false;
        } else {
          console.log(`✅ ${apiFile.name} uses custom authentication`);
        }
        
        console.log(''); // Add spacing
      } else {
        console.log(`❌ ${apiFile.name} file does not exist`);
        allPermissionsPass = false;
      }
    }
    
    return allPermissionsPass;
  } catch (error) {
    console.error('❌ API permissions test failed:', error.message);
    return false;
  }
}

/**
 * Test permission role coverage
 */
async function testPermissionRoleCoverage() {
  console.log('👥 Testing Permission Role Coverage...\n');
  
  try {
    const apiFiles = [
      'app/api/dashboard/organization/route.ts',
      'app/api/dashboard/leave/route.ts',
      'app/api/dashboard/approvals/route.ts'
    ];

    const expectedRoles = [
      'SUPER_ADMIN',
      'SYSTEM_ADMIN',
      'HR_DIRECTOR',
      'HR_MANAGER',
      'HR_SPECIALIST',
      'DEPARTMENT_HEAD',
      'FINANCE_MANAGER',
      'FINANCE_OFFICER',
      'MANAGER',
      'EMPLOYEE'
    ];

    let allRolesCovered = true;

    for (const apiFile of apiFiles) {
      const apiPath = path.join(__dirname, '..', apiFile);
      
      if (fs.existsSync(apiPath)) {
        const content = fs.readFileSync(apiPath, 'utf8');
        const fileName = apiFile.split('/').pop();
        
        console.log(`📋 Checking role coverage in ${fileName}:`);
        
        expectedRoles.forEach(role => {
          if (content.includes(`UserRole.${role}`)) {
            console.log(`  ✅ ${role} is included`);
          } else {
            console.log(`  ⚠️ ${role} is not included (may be intentional)`);
          }
        });
        
        // Check for basic employee access to dashboard data
        if (content.includes('UserRole.EMPLOYEE') || content.includes('UserRole.MANAGER')) {
          console.log(`  ✅ Basic employee/manager access is provided`);
        } else {
          console.log(`  ⚠️ No basic employee access - dashboard may be too restrictive`);
        }
        
        console.log(''); // Add spacing
      }
    }
    
    return allRolesCovered;
  } catch (error) {
    console.error('❌ Permission role coverage test failed:', error.message);
    return false;
  }
}

/**
 * Test security best practices
 */
async function testSecurityBestPractices() {
  console.log('🛡️ Testing Security Best Practices...\n');
  
  try {
    const apiFiles = [
      'app/api/dashboard/organization/route.ts',
      'app/api/dashboard/leave/route.ts',
      'app/api/dashboard/approvals/route.ts'
    ];

    let allSecurityPass = true;

    for (const apiFile of apiFiles) {
      const apiPath = path.join(__dirname, '..', apiFile);
      
      if (fs.existsSync(apiPath)) {
        const content = fs.readFileSync(apiPath, 'utf8');
        const fileName = apiFile.split('/').pop();
        
        console.log(`🔒 Security check for ${fileName}:`);
        
        // Check for authentication before permission check
        const authIndex = content.indexOf('getCurrentUser');
        const permissionIndex = content.indexOf('hasRequiredPermissions');
        
        if (authIndex !== -1 && permissionIndex !== -1 && authIndex < permissionIndex) {
          console.log(`  ✅ Authentication checked before permissions`);
        } else {
          console.log(`  ❌ Authentication/permission order issue`);
          allSecurityPass = false;
        }
        
        // Check for proper error handling
        if (content.includes('try') && content.includes('catch')) {
          console.log(`  ✅ Proper error handling implemented`);
        } else {
          console.log(`  ❌ Missing error handling`);
          allSecurityPass = false;
        }
        
        // Check for logging
        if (content.includes('logger.info') && content.includes('logger.error')) {
          console.log(`  ✅ Proper logging implemented`);
        } else {
          console.log(`  ⚠️ Limited logging (may affect debugging)`);
        }
        
        // Check for input validation
        if (content.includes('parseInt') || content.includes('parseFloat') || content.includes('Math.min')) {
          console.log(`  ✅ Input validation present`);
        } else {
          console.log(`  ⚠️ Limited input validation`);
        }
        
        console.log(''); // Add spacing
      }
    }
    
    return allSecurityPass;
  } catch (error) {
    console.error('❌ Security best practices test failed:', error.message);
    return false;
  }
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🚀 Dashboard API Permissions Tests');
  console.log('=' .repeat(40));
  
  const results = {
    apiPermissions: false,
    roleCoverage: false,
    securityPractices: false
  };
  
  // Run all tests
  results.apiPermissions = await testAPIPermissions();
  results.roleCoverage = await testPermissionRoleCoverage();
  results.securityPractices = await testSecurityBestPractices();
  
  // Summary
  console.log('\n📊 Test Results Summary');
  console.log('=' .repeat(30));
  
  const passed = Object.values(results).filter(Boolean).length;
  const total = Object.keys(results).length;
  
  console.log(`✅ API Permissions: ${results.apiPermissions ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Role Coverage: ${results.roleCoverage ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Security Practices: ${results.securityPractices ? 'PASS' : 'FAIL'}`);
  
  console.log(`\n🎯 Overall: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! Dashboard API permissions are properly configured.');
  } else {
    console.log('⚠️ Some tests failed. Please review the issues above.');
  }
  
  // Permission summary
  console.log('\n📋 Permission Configuration Summary:');
  console.log('• Organization Data: Accessible to most authenticated users');
  console.log('• Leave Data: Accessible to employees, managers, and HR');
  console.log('• Approval Data: Accessible to managers and above');
  console.log('• Recruitment Data: Limited to HR and management');
  console.log('• Performance Data: Limited to management and HR');
  console.log('• All APIs use custom authentication (not next-auth)');
  console.log('• Proper error handling and logging implemented');
  
  // Recommendations
  console.log('\n💡 Recommendations:');
  console.log('• Dashboard data should be accessible to provide organizational transparency');
  console.log('• Sensitive data (recruitment, detailed performance) remains restricted');
  console.log('• Regular permission audits should be conducted');
  console.log('• Consider role-based data filtering for enhanced security');
  
  process.exit(passed === total ? 0 : 1);
}

// Run the tests
if (require.main === module) {
  runTests().catch((error) => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = { runTests };
