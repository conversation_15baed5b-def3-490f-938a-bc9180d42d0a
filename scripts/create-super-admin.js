#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create a Super Admin user for Kawandama Hills Plantation Management System
 * 
 * Usage: node scripts/create-super-admin.js
 * 
 * This script will create a super admin user with the following credentials:
 * Email: <EMAIL>
 * Password: @Admin2020
 * Role: SUPER_ADMIN
 * Status: ACTIVE
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

// Import the User model and enums
const { UserRole, UserStatus } = require('../types/user-roles');

// User schema definition (since we can't import the model directly in a script)
const userSchema = new mongoose.Schema(
  {
    email: {
      type: String,
      required: [true, 'Email is required'],
      unique: true,
      lowercase: true,
      trim: true,
      validate: {
        validator: function(v) {
          return /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/.test(v);
        },
        message: (props) => `${props.value} is not a valid email address!`
      }
    },
    password: {
      type: String,
      required: [true, 'Password is required'],
      minlength: [8, 'Password must be at least 8 characters long'],
      select: false
    },
    firstName: {
      type: String,
      required: [true, 'First name is required'],
      trim: true
    },
    lastName: {
      type: String,
      required: [true, 'Last name is required'],
      trim: true
    },
    role: {
      type: String,
      enum: Object.values(UserRole),
      default: UserRole.EMPLOYEE
    },
    status: {
      type: String,
      enum: Object.values(UserStatus),
      default: UserStatus.ACTIVE
    },
    department: {
      type: String,
      ref: 'Department'
    },
    position: {
      type: String
    },
    phoneNumber: {
      type: String
    },
    address: {
      type: String
    },
    avatar: {
      type: String
    },
    dateOfBirth: {
      type: Date
    },
    dateOfJoining: {
      type: Date
    },
    emergencyContact: {
      name: String,
      relationship: String,
      phoneNumber: String
    },
    passwordResetToken: String,
    passwordResetExpires: Date,
    lastLogin: Date,
    failedLoginAttempts: {
      type: Number,
      default: 0
    },
    lastFailedLogin: Date,
    accountLockTime: Date,
    statusReason: String,
    statusChangedAt: Date,
    statusChangedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    securityNotes: String,
    allowMultipleDevices: {
      type: Boolean,
      default: false
    },
    trustedDevicesOnly: {
      type: Boolean,
      default: false
    },
    singleDeviceLogin: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true
  }
);

// Pre-save hook to hash password
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare password
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Method to get full name
userSchema.methods.fullName = function() {
  return `${this.firstName} ${this.lastName}`;
};

// Static method to find by email
userSchema.statics.findByEmail = function(email) {
  return this.findOne({ email: email.toLowerCase() });
};

// Create the User model
const User = mongoose.model('User', userSchema);

// Super Admin user details
const SUPER_ADMIN_DATA = {
  email: '<EMAIL>',
  password: '@Admin2020',
  firstName: 'Winston',
  lastName: 'Mhango',
  role: UserRole.SUPER_ADMIN,
  status: UserStatus.ACTIVE,
  department: 'Administration',
  position: 'System Administrator',
  dateOfJoining: new Date(),
  allowMultipleDevices: true,
  trustedDevicesOnly: false,
  singleDeviceLogin: false
};

/**
 * Connect to MongoDB database
 */
async function connectToDatabase() {
  try {
    const mongoUri = process.env.MONGODB_URI || process.env.DATABASE_URL;
    
    if (!mongoUri) {
      throw new Error('MONGODB_URI or DATABASE_URL environment variable is not set');
    }

    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB successfully');
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error.message);
    process.exit(1);
  }
}

/**
 * Create super admin user
 */
async function createSuperAdmin() {
  try {
    console.log('\n🔍 Checking if super admin already exists...');
    
    // Check if user already exists
    const existingUser = await User.findByEmail(SUPER_ADMIN_DATA.email);
    
    if (existingUser) {
      console.log('⚠️  Super admin user already exists!');
      console.log(`📧 Email: ${existingUser.email}`);
      console.log(`👤 Name: ${existingUser.fullName()}`);
      console.log(`🔑 Role: ${existingUser.role}`);
      console.log(`📊 Status: ${existingUser.status}`);
      console.log(`📅 Created: ${existingUser.createdAt}`);
      
      // Ask if user wants to update the existing user
      const readline = require('readline');
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });
      
      return new Promise((resolve) => {
        rl.question('\n❓ Do you want to update the existing user? (y/N): ', async (answer) => {
          rl.close();
          
          if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
            console.log('\n🔄 Updating existing super admin user...');
            
            // Update the existing user
            existingUser.password = SUPER_ADMIN_DATA.password;
            existingUser.firstName = SUPER_ADMIN_DATA.firstName;
            existingUser.lastName = SUPER_ADMIN_DATA.lastName;
            existingUser.role = SUPER_ADMIN_DATA.role;
            existingUser.status = SUPER_ADMIN_DATA.status;
            existingUser.department = SUPER_ADMIN_DATA.department;
            existingUser.position = SUPER_ADMIN_DATA.position;
            existingUser.allowMultipleDevices = SUPER_ADMIN_DATA.allowMultipleDevices;
            existingUser.trustedDevicesOnly = SUPER_ADMIN_DATA.trustedDevicesOnly;
            existingUser.singleDeviceLogin = SUPER_ADMIN_DATA.singleDeviceLogin;
            
            await existingUser.save();
            
            console.log('✅ Super admin user updated successfully!');
            console.log(`📧 Email: ${existingUser.email}`);
            console.log(`👤 Name: ${existingUser.fullName()}`);
            console.log(`🔑 Role: ${existingUser.role}`);
            console.log(`📊 Status: ${existingUser.status}`);
          } else {
            console.log('❌ Operation cancelled. Existing user was not modified.');
          }
          
          resolve();
        });
      });
    }
    
    console.log('👤 Creating new super admin user...');
    
    // Create new super admin user
    const superAdmin = new User(SUPER_ADMIN_DATA);
    await superAdmin.save();
    
    console.log('✅ Super admin user created successfully!');
    console.log('\n📋 User Details:');
    console.log(`📧 Email: ${superAdmin.email}`);
    console.log(`👤 Name: ${superAdmin.fullName()}`);
    console.log(`🔑 Role: ${superAdmin.role}`);
    console.log(`📊 Status: ${superAdmin.status}`);
    console.log(`🏢 Department: ${superAdmin.department}`);
    console.log(`💼 Position: ${superAdmin.position}`);
    console.log(`📅 Created: ${superAdmin.createdAt}`);
    console.log(`🔐 Password: ${SUPER_ADMIN_DATA.password}`);
    
    console.log('\n🎉 Super admin setup complete!');
    console.log('🔗 You can now login to the system with these credentials.');
    
  } catch (error) {
    console.error('❌ Failed to create super admin user:', error.message);
    
    if (error.code === 11000) {
      console.error('💡 This error usually means the email address is already in use.');
    }
    
    process.exit(1);
  }
}

/**
 * Main function
 */
async function main() {
  console.log('🌱 Kawandama Hills Plantation Management System');
  console.log('🔧 Super Admin User Creation Script');
  console.log('=' .repeat(50));
  
  try {
    // Connect to database
    await connectToDatabase();
    
    // Create super admin
    await createSuperAdmin();
    
  } catch (error) {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  } finally {
    // Close database connection
    console.log('\n🔌 Closing database connection...');
    await mongoose.connection.close();
    console.log('✅ Database connection closed');
    process.exit(0);
  }
}

// Run the script
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
  });
}

module.exports = { createSuperAdmin, connectToDatabase, User, SUPER_ADMIN_DATA };
