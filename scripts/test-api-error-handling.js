#!/usr/bin/env node

/**
 * Test script for API Error Handling Integration
 * 
 * This script tests the enhanced API error handling across all dashboard components
 * to ensure consistent error handling and user experience.
 * 
 * Usage: node scripts/test-api-error-handling.js
 */

const { config } = require('dotenv');
const { join } = require('path');
const fs = require('fs');
const path = require('path');

// Load environment variables
config({ path: join(__dirname, '..', '.env') });

/**
 * Test API error handler utility
 */
async function testAPIErrorHandler() {
  console.log('🔧 Testing API Error Handler Utility...\n');
  
  try {
    const handlerPath = path.join(__dirname, '..', 'lib', 'utils', 'api-error-handler.ts');
    
    if (fs.existsSync(handlerPath)) {
      console.log('✅ API error handler file exists');
      
      const content = fs.readFileSync(handlerPath, 'utf8');
      
      // Check for enhanced error handling features
      const features = [
        'handleApiResponse',
        'apiGet',
        'apiRequest',
        'ApiError',
        'text/html',
        'Authentication required',
        'Server error occurred'
      ];
      
      features.forEach(feature => {
        if (content.includes(feature)) {
          console.log(`✅ ${feature} feature is present`);
        } else {
          console.log(`❌ ${feature} feature is missing`);
        }
      });
      
      // Check for HTML response detection
      if (content.includes('text/html') && content.includes('Authentication required')) {
        console.log('✅ HTML response detection implemented');
      } else {
        console.log('❌ HTML response detection missing');
      }
      
      return true;
    } else {
      console.log('❌ API error handler file does not exist');
      return false;
    }
  } catch (error) {
    console.error('❌ API error handler test failed:', error.message);
    return false;
  }
}

/**
 * Test component integrations
 */
async function testComponentIntegrations() {
  console.log('\n🎨 Testing Component API Error Handling...\n');
  
  try {
    const components = [
      {
        path: 'components/leave-management.tsx',
        name: 'Leave Management',
        expectedImports: ['apiGet', 'apiRequest', 'ApiError'],
        expectedUsage: ['apiGet(', 'apiRequest(']
      },
      {
        path: 'components/upcoming-reviews.tsx',
        name: 'Upcoming Reviews',
        expectedImports: ['apiGet', 'ApiError'],
        expectedUsage: ['apiGet(']
      },
      {
        path: 'components/department-breakdown.tsx',
        name: 'Department Breakdown',
        expectedImports: ['apiGet', 'ApiError'],
        expectedUsage: ['apiGet(']
      },
      {
        path: 'components/recruitment-pipeline.tsx',
        name: 'Recruitment Pipeline',
        expectedImports: ['apiRequest', 'ApiError'],
        expectedUsage: ['apiRequest(']
      },
      {
        path: 'components/performance-metrics.tsx',
        name: 'Performance Metrics',
        expectedImports: ['apiRequest', 'ApiError'],
        expectedUsage: ['apiRequest(']
      }
    ];

    let allComponentsPass = true;

    for (const component of components) {
      const componentPath = path.join(__dirname, '..', component.path);
      
      if (fs.existsSync(componentPath)) {
        console.log(`✅ ${component.name} component exists`);
        
        const content = fs.readFileSync(componentPath, 'utf8');
        
        // Check for proper imports
        let hasRequiredImports = true;
        component.expectedImports.forEach(importItem => {
          if (content.includes(importItem)) {
            console.log(`  ✅ ${importItem} imported`);
          } else {
            console.log(`  ❌ ${importItem} import missing`);
            hasRequiredImports = false;
          }
        });
        
        // Check for usage
        let hasProperUsage = true;
        component.expectedUsage.forEach(usage => {
          if (content.includes(usage)) {
            console.log(`  ✅ ${usage} used correctly`);
          } else {
            console.log(`  ❌ ${usage} usage missing`);
            hasProperUsage = false;
          }
        });
        
        // Check for ApiError handling
        if (content.includes('instanceof ApiError')) {
          console.log(`  ✅ ApiError handling implemented`);
        } else {
          console.log(`  ❌ ApiError handling missing`);
          hasProperUsage = false;
        }
        
        // Check if old fetch patterns are removed
        const oldPatterns = [
          'await fetch(',
          'response.ok',
          'response.json()'
        ];
        
        let hasOldPatterns = false;
        oldPatterns.forEach(pattern => {
          if (content.includes(pattern)) {
            hasOldPatterns = true;
          }
        });
        
        if (!hasOldPatterns) {
          console.log(`  ✅ Old fetch patterns removed`);
        } else {
          console.log(`  ⚠️ Some old fetch patterns might still exist`);
        }
        
        if (!hasRequiredImports || !hasProperUsage) {
          allComponentsPass = false;
        }
        
        console.log(''); // Add spacing
      } else {
        console.log(`❌ ${component.name} component does not exist`);
        allComponentsPass = false;
      }
    }
    
    return allComponentsPass;
  } catch (error) {
    console.error('❌ Component integrations test failed:', error.message);
    return false;
  }
}

/**
 * Test error message consistency
 */
async function testErrorMessageConsistency() {
  console.log('📝 Testing Error Message Consistency...\n');
  
  try {
    const components = [
      'components/leave-management.tsx',
      'components/upcoming-reviews.tsx',
      'components/department-breakdown.tsx',
      'components/recruitment-pipeline.tsx',
      'components/performance-metrics.tsx'
    ];

    let allConsistent = true;

    for (const componentPath of components) {
      const fullPath = path.join(__dirname, '..', componentPath);
      
      if (fs.existsSync(fullPath)) {
        const content = fs.readFileSync(fullPath, 'utf8');
        const fileName = componentPath.split('/').pop();
        
        console.log(`📄 Checking ${fileName}:`);
        
        // Check for consistent error handling patterns
        const patterns = [
          'catch (err)',
          'instanceof ApiError',
          'err.message',
          'setError(errorMessage)',
          'console.error('
        ];
        
        patterns.forEach(pattern => {
          if (content.includes(pattern)) {
            console.log(`  ✅ ${pattern} pattern present`);
          } else {
            console.log(`  ❌ ${pattern} pattern missing`);
            allConsistent = false;
          }
        });
        
        // Check for user-friendly error messages
        if (content.includes('Failed to load') || content.includes('Failed to fetch')) {
          console.log(`  ✅ User-friendly error messages present`);
        } else {
          console.log(`  ❌ User-friendly error messages missing`);
          allConsistent = false;
        }
        
        console.log('');
      }
    }
    
    return allConsistent;
  } catch (error) {
    console.error('❌ Error message consistency test failed:', error.message);
    return false;
  }
}

/**
 * Test loading and error states
 */
async function testLoadingAndErrorStates() {
  console.log('⏳ Testing Loading and Error States...\n');
  
  try {
    const components = [
      'components/leave-management.tsx',
      'components/upcoming-reviews.tsx',
      'components/department-breakdown.tsx',
      'components/recruitment-pipeline.tsx',
      'components/performance-metrics.tsx'
    ];

    let allStatesImplemented = true;

    for (const componentPath of components) {
      const fullPath = path.join(__dirname, '..', componentPath);
      
      if (fs.existsSync(fullPath)) {
        const content = fs.readFileSync(fullPath, 'utf8');
        const fileName = componentPath.split('/').pop();
        
        console.log(`📄 Checking ${fileName}:`);
        
        // Check for loading states
        if (content.includes('isLoading') && content.includes('setIsLoading')) {
          console.log(`  ✅ Loading state management present`);
        } else {
          console.log(`  ❌ Loading state management missing`);
          allStatesImplemented = false;
        }
        
        // Check for error states
        if (content.includes('error') && content.includes('setError')) {
          console.log(`  ✅ Error state management present`);
        } else {
          console.log(`  ❌ Error state management missing`);
          allStatesImplemented = false;
        }
        
        // Check for empty state integration
        if (content.includes('getEmptyState') && content.includes('useEmptyState')) {
          console.log(`  ✅ Empty state integration present`);
        } else {
          console.log(`  ❌ Empty state integration missing`);
          allStatesImplemented = false;
        }
        
        // Check for skeleton loading
        if (content.includes('Skeleton')) {
          console.log(`  ✅ Skeleton loading components present`);
        } else {
          console.log(`  ❌ Skeleton loading components missing`);
          allStatesImplemented = false;
        }
        
        console.log('');
      }
    }
    
    return allStatesImplemented;
  } catch (error) {
    console.error('❌ Loading and error states test failed:', error.message);
    return false;
  }
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🚀 API Error Handling Integration Tests');
  console.log('=' .repeat(45));
  
  const results = {
    apiErrorHandler: false,
    componentIntegrations: false,
    errorMessageConsistency: false,
    loadingAndErrorStates: false
  };
  
  // Run all tests
  results.apiErrorHandler = await testAPIErrorHandler();
  results.componentIntegrations = await testComponentIntegrations();
  results.errorMessageConsistency = await testErrorMessageConsistency();
  results.loadingAndErrorStates = await testLoadingAndErrorStates();
  
  // Summary
  console.log('\n📊 Test Results Summary');
  console.log('=' .repeat(30));
  
  const passed = Object.values(results).filter(Boolean).length;
  const total = Object.keys(results).length;
  
  console.log(`✅ API Error Handler: ${results.apiErrorHandler ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Component Integrations: ${results.componentIntegrations ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Error Message Consistency: ${results.errorMessageConsistency ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Loading and Error States: ${results.loadingAndErrorStates ? 'PASS' : 'FAIL'}`);
  
  console.log(`\n🎯 Overall: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! API error handling is consistent across all components.');
  } else {
    console.log('⚠️ Some tests failed. Please review the issues above.');
  }
  
  // Benefits summary
  console.log('\n📋 API Error Handling Benefits:');
  console.log('• ✅ Consistent error handling across all dashboard components');
  console.log('• ✅ Enhanced HTML response detection and handling');
  console.log('• ✅ User-friendly error messages for common scenarios');
  console.log('• ✅ Proper loading states with skeleton components');
  console.log('• ✅ Graceful error recovery with retry functionality');
  console.log('• ✅ TypeScript support with proper error types');
  console.log('• ✅ Centralized error handling logic for maintainability');
  
  // Error scenarios handled
  console.log('\n🛡️ Error Scenarios Handled:');
  console.log('• HTML error pages (authentication issues)');
  console.log('• Network connectivity problems');
  console.log('• Server errors (5xx status codes)');
  console.log('• Invalid JSON responses');
  console.log('• Permission/authorization errors');
  console.log('• API endpoint not found errors');
  console.log('• Timeout and connection errors');
  
  process.exit(passed === total ? 0 : 1);
}

// Run the tests
if (require.main === module) {
  runTests().catch((error) => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = { runTests };
