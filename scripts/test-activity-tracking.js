#!/usr/bin/env node

/**
 * Test script for Activity Tracking System
 * 
 * This script tests the dashboard activity tracking implementation by:
 * 1. Checking if the activity service works
 * 2. Verifying API endpoints respond correctly
 * 3. Testing activity data transformation
 * 
 * Usage: node scripts/test-activity-tracking.js
 */

const { config } = require('dotenv');
const { join } = require('path');

// Load environment variables
config({ path: join(__dirname, '..', '.env') });

// Test the activity service
async function testActivityService() {
  console.log('🧪 Testing Dashboard Activity Service...\n');
  
  try {
    // Import the service (using dynamic import for ES modules)
    const { dashboardActivityService } = await import('../lib/services/dashboard/activity-service.ts');
    
    console.log('✅ Activity service imported successfully');
    
    // Test getting recent activities
    console.log('📊 Testing getRecentActivities...');
    const activities = await dashboardActivityService.getRecentActivities(5);
    
    console.log(`✅ Retrieved ${activities.length} activities`);
    
    if (activities.length > 0) {
      console.log('\n📋 Sample Activity:');
      const sample = activities[0];
      console.log(`   ID: ${sample.id}`);
      console.log(`   User: ${sample.user.name}`);
      console.log(`   Action: ${sample.action}`);
      console.log(`   Department: ${sample.department}`);
      console.log(`   Time: ${sample.time}`);
      console.log(`   Module: ${sample.module}`);
      console.log(`   Type: ${sample.type}`);
    }
    
    // Test getting activity stats
    console.log('\n📈 Testing getActivityStats...');
    const stats = await dashboardActivityService.getActivityStats('week');
    
    console.log(`✅ Activity Statistics:`);
    console.log(`   Total Activities: ${stats.totalActivities}`);
    console.log(`   Today's Activities: ${stats.todayActivities}`);
    console.log(`   Week's Activities: ${stats.weekActivities}`);
    console.log(`   Module Breakdown: ${stats.moduleBreakdown.length} modules`);
    console.log(`   Action Breakdown: ${stats.actionBreakdown.length} actions`);
    
    return true;
  } catch (error) {
    console.error('❌ Activity service test failed:', error.message);
    return false;
  }
}

// Test the API endpoint
async function testAPIEndpoint() {
  console.log('\n🌐 Testing API Endpoint...\n');
  
  try {
    // We can't directly test the API endpoint in this script since it requires authentication
    // But we can check if the file exists and is properly structured
    const fs = require('fs');
    const path = require('path');
    
    const apiPath = path.join(__dirname, '..', 'app', 'api', 'dashboard', 'activities', 'route.ts');
    
    if (fs.existsSync(apiPath)) {
      console.log('✅ API endpoint file exists');
      
      const content = fs.readFileSync(apiPath, 'utf8');
      
      // Check for required exports
      if (content.includes('export async function GET')) {
        console.log('✅ GET endpoint is defined');
      } else {
        console.log('❌ GET endpoint is missing');
      }
      
      if (content.includes('export async function POST')) {
        console.log('✅ POST endpoint is defined');
      } else {
        console.log('❌ POST endpoint is missing');
      }
      
      if (content.includes('getCurrentUser')) {
        console.log('✅ Authentication check is implemented');
      } else {
        console.log('❌ Authentication check is missing');
      }
      
      if (content.includes('dashboardActivityService')) {
        console.log('✅ Activity service is imported and used');
      } else {
        console.log('❌ Activity service integration is missing');
      }
      
      return true;
    } else {
      console.log('❌ API endpoint file does not exist');
      return false;
    }
  } catch (error) {
    console.error('❌ API endpoint test failed:', error.message);
    return false;
  }
}

// Test the component integration
async function testComponentIntegration() {
  console.log('\n🎨 Testing Component Integration...\n');
  
  try {
    const fs = require('fs');
    const path = require('path');
    
    const componentPath = path.join(__dirname, '..', 'components', 'recent-activities.tsx');
    
    if (fs.existsSync(componentPath)) {
      console.log('✅ Recent Activities component exists');
      
      const content = fs.readFileSync(componentPath, 'utf8');
      
      // Check for API integration
      if (content.includes('/api/dashboard/activities')) {
        console.log('✅ Component uses real API endpoint');
      } else {
        console.log('❌ Component still uses mock data');
      }
      
      // Check for real-time updates
      if (content.includes('setInterval') || content.includes('useCallback')) {
        console.log('✅ Real-time updates are implemented');
      } else {
        console.log('❌ Real-time updates are missing');
      }
      
      // Check for error handling
      if (content.includes('try') && content.includes('catch')) {
        console.log('✅ Error handling is implemented');
      } else {
        console.log('❌ Error handling is missing');
      }
      
      // Check for loading states
      if (content.includes('isLoading') && content.includes('Skeleton')) {
        console.log('✅ Loading states are implemented');
      } else {
        console.log('❌ Loading states are missing');
      }
      
      return true;
    } else {
      console.log('❌ Recent Activities component does not exist');
      return false;
    }
  } catch (error) {
    console.error('❌ Component integration test failed:', error.message);
    return false;
  }
}

// Test database connectivity
async function testDatabaseConnectivity() {
  console.log('\n🗄️ Testing Database Connectivity...\n');
  
  try {
    // Import database connection
    const { connectToDatabase } = await import('../lib/backend/database.ts');
    
    console.log('🔌 Connecting to database...');
    await connectToDatabase();
    console.log('✅ Database connection successful');
    
    // Test if AuditLog model is available
    const AuditLog = (await import('../models/payroll/AuditLog.ts')).default;
    
    console.log('📊 Testing AuditLog model...');
    const count = await AuditLog.countDocuments();
    console.log(`✅ Found ${count} audit log entries`);
    
    // Test a simple query
    const recentLogs = await AuditLog.find()
      .sort({ timestamp: -1 })
      .limit(1)
      .lean();
    
    if (recentLogs.length > 0) {
      console.log('✅ Can query audit logs successfully');
      console.log(`   Most recent log: ${recentLogs[0].action} on ${recentLogs[0].entityType}`);
    } else {
      console.log('⚠️ No audit logs found (this is normal for a new system)');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Database connectivity test failed:', error.message);
    return false;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Dashboard Activity Tracking System Tests');
  console.log('=' .repeat(50));
  
  const results = {
    activityService: false,
    apiEndpoint: false,
    componentIntegration: false,
    databaseConnectivity: false
  };
  
  // Run all tests
  results.activityService = await testActivityService();
  results.apiEndpoint = await testAPIEndpoint();
  results.componentIntegration = await testComponentIntegration();
  results.databaseConnectivity = await testDatabaseConnectivity();
  
  // Summary
  console.log('\n📊 Test Results Summary');
  console.log('=' .repeat(30));
  
  const passed = Object.values(results).filter(Boolean).length;
  const total = Object.keys(results).length;
  
  console.log(`✅ Activity Service: ${results.activityService ? 'PASS' : 'FAIL'}`);
  console.log(`✅ API Endpoint: ${results.apiEndpoint ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Component Integration: ${results.componentIntegration ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Database Connectivity: ${results.databaseConnectivity ? 'PASS' : 'FAIL'}`);
  
  console.log(`\n🎯 Overall: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! Activity tracking system is ready.');
  } else {
    console.log('⚠️ Some tests failed. Please review the issues above.');
  }
  
  // Next steps
  console.log('\n📋 Next Steps:');
  console.log('1. Start your development server: npm run dev');
  console.log('2. Navigate to /dashboard to see the updated Recent Activities');
  console.log('3. Perform some actions (create/update employees, etc.) to generate activities');
  console.log('4. Verify that activities appear in real-time');
  
  process.exit(passed === total ? 0 : 1);
}

// Run the tests
if (require.main === module) {
  runTests().catch((error) => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = { runTests };
