"use client"

import { useState, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';

interface WorkflowActionResult {
  success: boolean;
  message: string;
  nextStep?: number;
  nextApprovers?: string[];
  workflowCompleted?: boolean;
  finalStatus?: 'approved' | 'rejected';
}

interface UseApprovalWorkflowsReturn {
  loading: boolean;
  error: string | null;
  approveStep: (workflowId: string, stepNumber: number, comments?: string) => Promise<WorkflowActionResult | null>;
  rejectStep: (workflowId: string, stepNumber: number, rejectionReason: string) => Promise<WorkflowActionResult | null>;
  delegateStep: (workflowId: string, stepNumber: number, delegateToId: string, delegationReason: string) => Promise<WorkflowActionResult | null>;
}

export function useApprovalWorkflows(): UseApprovalWorkflowsReturn {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const approveStep = useCallback(async (
    workflowId: string,
    stepNumber: number,
    comments?: string
  ): Promise<WorkflowActionResult | null> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/leave/workflows/${workflowId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          stepNumber,
          comments
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to approve workflow step');
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: result.message || "Workflow step approved successfully",
      });

      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while approving workflow step';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const rejectStep = useCallback(async (
    workflowId: string,
    stepNumber: number,
    rejectionReason: string
  ): Promise<WorkflowActionResult | null> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/leave/workflows/${workflowId}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          stepNumber,
          rejectionReason
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to reject workflow step');
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: result.message || "Workflow step rejected successfully",
      });

      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while rejecting workflow step';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const delegateStep = useCallback(async (
    workflowId: string,
    stepNumber: number,
    delegateToId: string,
    delegationReason: string
  ): Promise<WorkflowActionResult | null> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/leave/workflows/${workflowId}/delegate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          stepNumber,
          delegateToId,
          delegationReason
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delegate workflow step');
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: result.message || "Workflow step delegated successfully",
      });

      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while delegating workflow step';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  return {
    loading,
    error,
    approveStep,
    rejectStep,
    delegateStep,
  };
}
