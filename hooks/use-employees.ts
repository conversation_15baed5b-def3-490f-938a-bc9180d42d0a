"use client"

import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';

export interface Employee {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  employeeId?: string;
  position?: string;
  departmentId?: {
    _id: string;
    name: string;
  };
  profilePicture?: string;
  employmentStatus: 'active' | 'inactive' | 'terminated';
  hireDate: string;
  basicSalary?: number;
}

interface UseEmployeesOptions {
  autoFetch?: boolean;
  status?: 'active' | 'inactive' | 'terminated' | 'all';
  departmentId?: string;
  searchTerm?: string;
  limit?: number;
  page?: number;
}

interface UseEmployeesReturn {
  employees: Employee[];
  loading: boolean;
  error: string | null;
  totalPages: number;
  currentPage: number;
  totalEmployees: number;
  searchEmployees: (searchTerm: string) => Promise<void>;
  fetchEmployees: (options?: UseEmployeesOptions) => Promise<void>;
  refreshEmployees: () => Promise<void>;
  getEmployeeById: (id: string) => Employee | undefined;
  getEmployeeStats: () => {
    active: number;
    inactive: number;
    terminated: number;
    total: number;
  };
}

export function useEmployees(options: UseEmployeesOptions = {}): UseEmployeesReturn {
  const { toast } = useToast();
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalEmployees, setTotalEmployees] = useState(0);
  const [currentOptions, setCurrentOptions] = useState<UseEmployeesOptions>(options);

  const fetchEmployees = useCallback(async (fetchOptions: UseEmployeesOptions = {}) => {
    try {
      setLoading(true);
      setError(null);

      const mergedOptions = { ...currentOptions, ...fetchOptions };
      setCurrentOptions(mergedOptions);

      // Build query parameters
      const params = new URLSearchParams();
      
      if (mergedOptions.status && mergedOptions.status !== 'all') {
        params.append('status', mergedOptions.status);
      }
      
      if (mergedOptions.departmentId) {
        params.append('departmentId', mergedOptions.departmentId);
      }
      
      if (mergedOptions.searchTerm) {
        params.append('search', mergedOptions.searchTerm);
      }
      
      if (mergedOptions.limit) {
        params.append('limit', mergedOptions.limit.toString());
      }
      
      if (mergedOptions.page) {
        params.append('page', mergedOptions.page.toString());
      }

      const response = await fetch(`/api/hr/employees?${params.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch employees');
      }

      const data = await response.json();
      
      setEmployees(data.docs || []);
      setTotalPages(data.totalPages || 1);
      setCurrentPage(data.page || 1);
      setTotalEmployees(data.totalDocs || 0);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while fetching employees';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [currentOptions, toast]);

  const searchEmployees = useCallback(async (searchTerm: string) => {
    await fetchEmployees({ ...currentOptions, searchTerm, page: 1 });
  }, [fetchEmployees, currentOptions]);

  const refreshEmployees = useCallback(async () => {
    await fetchEmployees(currentOptions);
  }, [fetchEmployees, currentOptions]);

  const getEmployeeById = useCallback((id: string): Employee | undefined => {
    return employees.find(emp => emp._id === id);
  }, [employees]);

  const getEmployeeStats = useCallback(() => {
    const stats = employees.reduce(
      (acc, emp) => {
        acc[emp.employmentStatus]++;
        acc.total++;
        return acc;
      },
      { active: 0, inactive: 0, terminated: 0, total: 0 }
    );
    return stats;
  }, [employees]);

  // Auto-fetch on mount if enabled
  useEffect(() => {
    if (options.autoFetch !== false) {
      fetchEmployees(options);
    }
  }, []); // Only run once on mount

  return {
    employees,
    loading,
    error,
    totalPages,
    currentPage,
    totalEmployees,
    searchEmployees,
    fetchEmployees,
    refreshEmployees,
    getEmployeeById,
    getEmployeeStats,
  };
}
