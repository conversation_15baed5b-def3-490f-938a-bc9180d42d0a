"use client"

import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';

interface LeaveBalance {
  _id?: string;
  leaveType?: {
    _id: string;
    name: string;
    code: string;
    color: string;
  };
  leaveTypeId?: {
    _id: string;
    name: string;
    color: string;
    isPaid: boolean;
  } | string;
  totalDays: number;
  usedDays: number;
  remainingDays: number;
  carryOverDays?: number;
  pendingDays?: number;
  year?: number;
}

interface UseEmployeeLeaveBalancesReturn {
  balances: LeaveBalance[];
  loading: boolean;
  error: string | null;
  fetchBalances: (employeeId: string, year?: number) => Promise<void>;
  refreshBalances: () => Promise<void>;
  getTotalAvailable: () => number;
  getTotalUsed: () => number;
  getBalanceByLeaveType: (leaveTypeId: string) => LeaveBalance | undefined;
}

export function useEmployeeLeaveBalances(): UseEmployeeLeaveBalancesReturn {
  const { toast } = useToast();
  const [balances, setBalances] = useState<LeaveBalance[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentEmployeeId, setCurrentEmployeeId] = useState<string | null>(null);
  const [currentYear, setCurrentYear] = useState<number>(new Date().getFullYear());

  const fetchBalances = useCallback(async (employeeId: string, year: number = new Date().getFullYear()) => {
    try {
      setLoading(true);
      setError(null);
      setCurrentEmployeeId(employeeId);
      setCurrentYear(year);

      const params = new URLSearchParams({
        employeeId,
        year: year.toString()
      });

      const response = await fetch(`/api/leave/balances?${params.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch leave balances');
      }

      const data = await response.json();

      // Transform the data structure to match component expectations
      const transformedBalances = (data.data || []).map((balance: any) => {
        // Handle both old and new data structures
        if (balance.leaveType) {
          // New structure from LeaveService
          return {
            _id: balance.leaveType._id,
            leaveTypeId: {
              _id: balance.leaveType._id,
              name: balance.leaveType.name,
              color: balance.leaveType.color || '#6B7280',
              isPaid: true // Default to paid, this should come from the leave type
            },
            totalDays: balance.totalDays,
            usedDays: balance.usedDays,
            remainingDays: balance.remainingDays,
            carryOverDays: balance.carryOverDays || 0,
            pendingDays: balance.pendingDays || 0,
            year: data.year || new Date().getFullYear()
          };
        } else {
          // Old structure - return as is
          return balance;
        }
      });

      setBalances(transformedBalances);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while fetching leave balances';
      setError(errorMessage);
      setBalances([]);
      
      // Only show toast for actual errors, not when employee is not found
      if (!errorMessage.includes('not found')) {
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const refreshBalances = useCallback(async () => {
    if (currentEmployeeId) {
      await fetchBalances(currentEmployeeId, currentYear);
    }
  }, [fetchBalances, currentEmployeeId, currentYear]);

  const getTotalAvailable = useCallback((): number => {
    return balances.reduce((total, balance) => total + balance.remainingDays, 0);
  }, [balances]);

  const getTotalUsed = useCallback((): number => {
    return balances.reduce((total, balance) => total + balance.usedDays, 0);
  }, [balances]);

  const getBalanceByLeaveType = useCallback((leaveTypeId: string): LeaveBalance | undefined => {
    return balances.find(balance => {
      if (typeof balance.leaveTypeId === 'object' && balance.leaveTypeId) {
        return balance.leaveTypeId._id === leaveTypeId;
      }
      return balance.leaveTypeId === leaveTypeId;
    });
  }, [balances]);

  return {
    balances,
    loading,
    error,
    fetchBalances,
    refreshBalances,
    getTotalAvailable,
    getTotalUsed,
    getBalanceByLeaveType,
  };
}
