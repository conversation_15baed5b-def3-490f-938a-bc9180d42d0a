"use client"

import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';

interface LeaveBalance {
  _id: string;
  leaveTypeId: {
    _id: string;
    name: string;
    color: string;
    isPaid: boolean;
  };
  totalDays: number;
  usedDays: number;
  remainingDays: number;
  carryOverDays: number;
  year: number;
}

interface UseEmployeeLeaveBalancesReturn {
  balances: LeaveBalance[];
  loading: boolean;
  error: string | null;
  fetchBalances: (employeeId: string, year?: number) => Promise<void>;
  refreshBalances: () => Promise<void>;
  getTotalAvailable: () => number;
  getTotalUsed: () => number;
  getBalanceByLeaveType: (leaveTypeId: string) => LeaveBalance | undefined;
}

export function useEmployeeLeaveBalances(): UseEmployeeLeaveBalancesReturn {
  const { toast } = useToast();
  const [balances, setBalances] = useState<LeaveBalance[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentEmployeeId, setCurrentEmployeeId] = useState<string | null>(null);
  const [currentYear, setCurrentYear] = useState<number>(new Date().getFullYear());

  const fetchBalances = useCallback(async (employeeId: string, year: number = new Date().getFullYear()) => {
    try {
      setLoading(true);
      setError(null);
      setCurrentEmployeeId(employeeId);
      setCurrentYear(year);

      const params = new URLSearchParams({
        employeeId,
        year: year.toString()
      });

      const response = await fetch(`/api/leave/balances?${params.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch leave balances');
      }

      const data = await response.json();
      setBalances(data.data || []);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while fetching leave balances';
      setError(errorMessage);
      setBalances([]);
      
      // Only show toast for actual errors, not when employee is not found
      if (!errorMessage.includes('not found')) {
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const refreshBalances = useCallback(async () => {
    if (currentEmployeeId) {
      await fetchBalances(currentEmployeeId, currentYear);
    }
  }, [fetchBalances, currentEmployeeId, currentYear]);

  const getTotalAvailable = useCallback((): number => {
    return balances.reduce((total, balance) => total + balance.remainingDays, 0);
  }, [balances]);

  const getTotalUsed = useCallback((): number => {
    return balances.reduce((total, balance) => total + balance.usedDays, 0);
  }, [balances]);

  const getBalanceByLeaveType = useCallback((leaveTypeId: string): LeaveBalance | undefined => {
    return balances.find(balance => balance.leaveTypeId._id === leaveTypeId);
  }, [balances]);

  return {
    balances,
    loading,
    error,
    fetchBalances,
    refreshBalances,
    getTotalAvailable,
    getTotalUsed,
    getBalanceByLeaveType,
  };
}
