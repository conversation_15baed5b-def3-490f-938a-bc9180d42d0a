"use client"

import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';

interface EncashmentEligibility {
  eligible: boolean;
  maxEncashableDays: number;
  minRetentionDays: number;
  availableForEncashment: number;
  ratePerDay: number;
  estimatedAmount: number;
  rule?: any;
  errors?: string[];
}

interface EncashmentRequest {
  employeeId: string;
  leaveTypeId: string;
  year: number;
  daysToEncash: number;
  reason?: string;
  notes?: string;
}

interface UseLeaveEncashmentsReturn {
  encashments: any[];
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  } | null;
  fetchEncashments: (params?: any) => Promise<void>;
  checkEligibility: (employeeId: string, leaveTypeId: string, year: number) => Promise<EncashmentEligibility | null>;
  createEncashmentRequest: (request: EncashmentRequest) => Promise<any>;
  approveEncashment: (id: string) => Promise<boolean>;
  rejectEncashment: (id: string, reason: string) => Promise<boolean>;
  refreshEncashments: () => Promise<void>;
}

export function useLeaveEncashments(): UseLeaveEncashmentsReturn {
  const { toast } = useToast();
  const [encashments, setEncashments] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<{
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  } | null>(null);
  const [currentParams, setCurrentParams] = useState<any>({});

  const fetchEncashments = useCallback(async (params: any = {}) => {
    try {
      setLoading(true);
      setError(null);

      const mergedParams = { ...currentParams, ...params };
      setCurrentParams(mergedParams);

      // Build query string
      const searchParams = new URLSearchParams();
      Object.entries(mergedParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/leave/encashments?${searchParams.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch encashments');
      }

      const data = await response.json();
      setEncashments(data.data);
      setPagination(data.pagination);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while fetching encashments';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [currentParams, toast]);

  const refreshEncashments = useCallback(async () => {
    await fetchEncashments(currentParams);
  }, [fetchEncashments, currentParams]);

  const checkEligibility = useCallback(async (
    employeeId: string,
    leaveTypeId: string,
    year: number
  ): Promise<EncashmentEligibility | null> => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        employeeId,
        leaveTypeId,
        year: year.toString()
      });

      const response = await fetch(`/api/leave/encashments/eligibility?${params.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to check eligibility');
      }

      const result = await response.json();
      return result.data;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while checking eligibility';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const createEncashmentRequest = useCallback(async (request: EncashmentRequest): Promise<any> => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/leave/encashments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create encashment request');
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: result.message || "Encashment request created successfully",
      });

      // Refresh the list
      await refreshEncashments();
      
      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while creating encashment request';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [refreshEncashments, toast]);

  const approveEncashment = useCallback(async (id: string): Promise<boolean> => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/leave/encashments/${id}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to approve encashment');
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: result.message || "Encashment approved successfully",
      });

      // Update the local state
      setEncashments(prev => prev.map(enc => 
        enc._id === id ? { ...enc, status: 'approved' } : enc
      ));
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while approving encashment';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const rejectEncashment = useCallback(async (id: string, reason: string): Promise<boolean> => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/leave/encashments/${id}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ rejectionReason: reason }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to reject encashment');
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: result.message || "Encashment rejected successfully",
      });

      // Update the local state
      setEncashments(prev => prev.map(enc => 
        enc._id === id ? { ...enc, status: 'rejected' } : enc
      ));
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while rejecting encashment';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  // Auto-fetch on mount
  useEffect(() => {
    fetchEncashments();
  }, []); // Only run once on mount

  return {
    encashments,
    loading,
    error,
    pagination,
    fetchEncashments,
    checkEligibility,
    createEncashmentRequest,
    approveEncashment,
    rejectEncashment,
    refreshEncashments,
  };
}
