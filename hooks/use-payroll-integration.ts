"use client"

import { useState, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';

interface PayrollData {
  periodStart: string;
  periodEnd: string;
  totalEmployees: number;
  leaveDeductions: any[];
  encashmentPayments: any[];
  summary: {
    totalLeaveDeductions: number;
    totalEncashmentPayments: number;
    netPayrollImpact: number;
  };
}

interface CostCenterAllocation {
  allocations: any[];
  summary: {
    totalCostCenters: number;
    totalDeductions: number;
    totalEncashments: number;
    netAmount: number;
    totalEmployees: number;
  };
}

interface UsePayrollIntegrationReturn {
  loading: boolean;
  error: string | null;
  generatePayrollData: (periodStart: Date, periodEnd: Date, format?: 'json' | 'csv') => Promise<PayrollData | null>;
  generateCostCenterAllocation: (periodStart: Date, periodEnd: Date, format?: 'json' | 'csv') => Promise<CostCenterAllocation | null>;
  processEncashments: (encashmentIds: string[], payrollRunId: string) => Promise<boolean>;
}

export function usePayrollIntegration(): UsePayrollIntegrationReturn {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generatePayrollData = useCallback(async (
    periodStart: Date,
    periodEnd: Date,
    format: 'json' | 'csv' = 'json'
  ): Promise<PayrollData | null> => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        periodStart: periodStart.toISOString().split('T')[0],
        periodEnd: periodEnd.toISOString().split('T')[0],
        format
      });

      const response = await fetch(`/api/payroll/leave-data?${params.toString()}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate payroll data');
      }

      if (format === 'csv') {
        // Handle CSV download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `payroll-leave-data-${periodStart.toISOString().split('T')[0]}-to-${periodEnd.toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        toast({
          title: "Success",
          description: "Payroll data downloaded successfully",
        });

        return null;
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: "Payroll data generated successfully",
      });

      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while generating payroll data';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const generateCostCenterAllocation = useCallback(async (
    periodStart: Date,
    periodEnd: Date,
    format: 'json' | 'csv' = 'json'
  ): Promise<CostCenterAllocation | null> => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        periodStart: periodStart.toISOString().split('T')[0],
        periodEnd: periodEnd.toISOString().split('T')[0],
        format
      });

      const response = await fetch(`/api/payroll/cost-center-allocation?${params.toString()}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate cost center allocation');
      }

      if (format === 'csv') {
        // Handle CSV download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `cost-center-allocation-${periodStart.toISOString().split('T')[0]}-to-${periodEnd.toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        toast({
          title: "Success",
          description: "Cost center allocation downloaded successfully",
        });

        return null;
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: "Cost center allocation generated successfully",
      });

      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while generating cost center allocation';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const processEncashments = useCallback(async (
    encashmentIds: string[],
    payrollRunId: string
  ): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/payroll/process-encashments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          encashmentIds,
          payrollRunId
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process encashments');
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: result.message || "Encashments processed successfully",
      });

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while processing encashments';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  return {
    loading,
    error,
    generatePayrollData,
    generateCostCenterAllocation,
    processEncashments,
  };
}
