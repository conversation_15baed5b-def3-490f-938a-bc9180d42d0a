"use client"

import { useState, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';

interface AccrualProcessResult {
  totalProcessed: number;
  totalEligible: number;
  totalErrors: number;
  results: any[];
  errors: string[];
}

interface UseLeaveAccrualsReturn {
  loading: boolean;
  error: string | null;
  processMonthlyAccruals: (processingDate: Date) => Promise<AccrualProcessResult | null>;
  processQuarterlyAccruals: (processingDate: Date) => Promise<AccrualProcessResult | null>;
  processAnnualAccruals: (processingDate: Date) => Promise<AccrualProcessResult | null>;
  processProRataAccrual: (employeeId: string, hireDate: Date) => Promise<AccrualProcessResult | null>;
  applyAccruals: (accrualIds: string[]) => Promise<boolean>;
}

export function useLeaveAccruals(): UseLeaveAccrualsReturn {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const processAccruals = useCallback(async (
    type: 'monthly' | 'quarterly' | 'annually' | 'pro-rata',
    processingDate: Date,
    employeeId?: string,
    hireDate?: Date
  ): Promise<AccrualProcessResult | null> => {
    try {
      setLoading(true);
      setError(null);

      const payload: any = {
        type,
        processingDate: processingDate.toISOString().split('T')[0]
      };

      if (type === 'pro-rata' && employeeId && hireDate) {
        payload.employeeId = employeeId;
        payload.hireDate = hireDate.toISOString().split('T')[0];
      }

      const response = await fetch('/api/leave/accruals/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process accruals');
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: result.message || "Accruals processed successfully",
      });

      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while processing accruals';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const processMonthlyAccruals = useCallback(async (processingDate: Date): Promise<AccrualProcessResult | null> => {
    return processAccruals('monthly', processingDate);
  }, [processAccruals]);

  const processQuarterlyAccruals = useCallback(async (processingDate: Date): Promise<AccrualProcessResult | null> => {
    return processAccruals('quarterly', processingDate);
  }, [processAccruals]);

  const processAnnualAccruals = useCallback(async (processingDate: Date): Promise<AccrualProcessResult | null> => {
    return processAccruals('annually', processingDate);
  }, [processAccruals]);

  const processProRataAccrual = useCallback(async (employeeId: string, hireDate: Date): Promise<AccrualProcessResult | null> => {
    return processAccruals('pro-rata', new Date(), employeeId, hireDate);
  }, [processAccruals]);

  const applyAccruals = useCallback(async (accrualIds: string[]): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/leave/accruals/apply', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ accrualIds }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to apply accruals');
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: result.message || "Accruals applied successfully",
      });

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while applying accruals';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  return {
    loading,
    error,
    processMonthlyAccruals,
    processQuarterlyAccruals,
    processAnnualAccruals,
    processProRataAccrual,
    applyAccruals,
  };
}
