"use client"

import { useState, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';

interface UseLeaveReportsReturn {
  loading: boolean;
  error: string | null;
  generateUtilizationReport: (startDate: Date, endDate: Date, format?: 'json' | 'csv') => Promise<any>;
  generateDepartmentReport: (year: number, format?: 'json' | 'csv') => Promise<any>;
  generateTrendReport: (period: 'monthly' | 'quarterly' | 'yearly', periodsBack?: number, format?: 'json' | 'csv') => Promise<any>;
  generateEmployeeReport: (employeeId: string, year: number, format?: 'json' | 'csv') => Promise<any>;
}

export function useLeaveReports(): UseLeaveReportsReturn {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generateUtilizationReport = useCallback(async (
    startDate: Date,
    endDate: Date,
    format: 'json' | 'csv' = 'json'
  ): Promise<any> => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
        format
      });

      const response = await fetch(`/api/leave/reports/utilization?${params.toString()}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate utilization report');
      }

      if (format === 'csv') {
        // Handle CSV download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `leave-utilization-report-${startDate.toISOString().split('T')[0]}-to-${endDate.toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        toast({
          title: "Success",
          description: "Utilization report downloaded successfully",
        });

        return null;
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: "Utilization report generated successfully",
      });

      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while generating utilization report';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const generateDepartmentReport = useCallback(async (
    year: number,
    format: 'json' | 'csv' = 'json'
  ): Promise<any> => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        year: year.toString(),
        format
      });

      const response = await fetch(`/api/leave/reports/department?${params.toString()}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate department report');
      }

      if (format === 'csv') {
        // Handle CSV download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `department-analytics-report-${year}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        toast({
          title: "Success",
          description: "Department report downloaded successfully",
        });

        return null;
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: "Department report generated successfully",
      });

      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while generating department report';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const generateTrendReport = useCallback(async (
    period: 'monthly' | 'quarterly' | 'yearly',
    periodsBack: number = 12,
    format: 'json' | 'csv' = 'json'
  ): Promise<any> => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        period,
        periodsBack: periodsBack.toString(),
        format
      });

      const response = await fetch(`/api/leave/reports/trends?${params.toString()}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate trend report');
      }

      if (format === 'csv') {
        // Handle CSV download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `trend-analysis-report-${period}-${periodsBack}periods.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        toast({
          title: "Success",
          description: "Trend report downloaded successfully",
        });

        return null;
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: "Trend report generated successfully",
      });

      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while generating trend report';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const generateEmployeeReport = useCallback(async (
    employeeId: string,
    year: number,
    format: 'json' | 'csv' = 'json'
  ): Promise<any> => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        year: year.toString(),
        format
      });

      const response = await fetch(`/api/leave/reports/employee/${employeeId}?${params.toString()}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate employee report');
      }

      if (format === 'csv') {
        // Handle CSV download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `employee-leave-report-${employeeId}-${year}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        toast({
          title: "Success",
          description: "Employee report downloaded successfully",
        });

        return null;
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: "Employee report generated successfully",
      });

      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while generating employee report';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  return {
    loading,
    error,
    generateUtilizationReport,
    generateDepartmentReport,
    generateTrendReport,
    generateEmployeeReport,
  };
}
