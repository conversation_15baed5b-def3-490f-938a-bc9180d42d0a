"use client"

import { useState, useEffect, useCallback } from 'react';
import { pwaService } from '@/lib/pwa/PWAService';
import { useToast } from '@/components/ui/use-toast';

interface UsePWAReturn {
  isOnline: boolean;
  isPWA: boolean;
  isInstallable: boolean;
  isUpdateAvailable: boolean;
  offlineRequestsCount: number;
  installApp: () => Promise<boolean>;
  updateApp: () => Promise<void>;
  storeOfflineRequest: (data: any) => Promise<string>;
  syncOfflineData: () => Promise<void>;
  showNotification: (title: string, options?: NotificationOptions) => Promise<void>;
  requestNotificationPermission: () => Promise<NotificationPermission>;
}

export function usePWA(): UsePWAReturn {
  const { toast } = useToast();
  const [isOnline, setIsOnline] = useState(true);
  const [isPWA, setIsPWA] = useState(false);
  const [isInstallable, setIsInstallable] = useState(false);
  const [isUpdateAvailable, setIsUpdateAvailable] = useState(false);
  const [offlineRequestsCount, setOfflineRequestsCount] = useState(0);

  // Initialize PWA service
  useEffect(() => {
    const initializePWA = async () => {
      try {
        await pwaService.initialize();
        setIsOnline(pwaService.isOnline());
        setIsPWA(pwaService.isPWA());
        setIsInstallable(pwaService.isInstallable());
        
        // Get offline requests count
        const offlineRequests = await pwaService.getOfflineLeaveRequests();
        setOfflineRequestsCount(offlineRequests.length);
      } catch (error) {
        console.error('Error initializing PWA:', error);
      }
    };

    initializePWA();
  }, []);

  // Set up event listeners
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      toast({
        title: "Back Online",
        description: "Your connection has been restored. Syncing offline data...",
      });
      syncOfflineData();
    };

    const handleOffline = () => {
      setIsOnline(false);
      toast({
        title: "You're Offline",
        description: "You can continue working. Changes will sync when you're back online.",
        variant: "destructive",
      });
    };

    const handleInstallAvailable = () => {
      setIsInstallable(true);
      toast({
        title: "Install App",
        description: "Install Kawandama HR for a better experience!",
        action: {
          altText: "Install",
          onClick: installApp
        }
      });
    };

    const handleUpdateAvailable = () => {
      setIsUpdateAvailable(true);
      toast({
        title: "Update Available",
        description: "A new version of the app is available.",
        action: {
          altText: "Update",
          onClick: updateApp
        }
      });
    };

    // Add event listeners
    window.addEventListener('pwa-online', handleOnline);
    window.addEventListener('pwa-offline', handleOffline);
    window.addEventListener('pwa-install-available', handleInstallAvailable);
    window.addEventListener('pwa-update-available', handleUpdateAvailable);

    // Cleanup
    return () => {
      window.removeEventListener('pwa-online', handleOnline);
      window.removeEventListener('pwa-offline', handleOffline);
      window.removeEventListener('pwa-install-available', handleInstallAvailable);
      window.removeEventListener('pwa-update-available', handleUpdateAvailable);
    };
  }, [toast]);

  const installApp = useCallback(async (): Promise<boolean> => {
    try {
      const installed = await pwaService.installApp();
      if (installed) {
        setIsInstallable(false);
        setIsPWA(true);
        toast({
          title: "App Installed",
          description: "Kawandama HR has been installed successfully!",
        });
      }
      return installed;
    } catch (error) {
      console.error('Error installing app:', error);
      toast({
        title: "Installation Failed",
        description: "Failed to install the app. Please try again.",
        variant: "destructive",
      });
      return false;
    }
  }, [toast]);

  const updateApp = useCallback(async (): Promise<void> => {
    try {
      await pwaService.updateServiceWorker();
      setIsUpdateAvailable(false);
      toast({
        title: "App Updated",
        description: "The app has been updated to the latest version.",
      });
    } catch (error) {
      console.error('Error updating app:', error);
      toast({
        title: "Update Failed",
        description: "Failed to update the app. Please try again.",
        variant: "destructive",
      });
    }
  }, [toast]);

  const storeOfflineRequest = useCallback(async (data: any): Promise<string> => {
    try {
      const id = await pwaService.storeOfflineLeaveRequest(data);
      setOfflineRequestsCount(prev => prev + 1);
      
      toast({
        title: "Request Saved Offline",
        description: "Your request will be submitted when you're back online.",
      });
      
      return id;
    } catch (error) {
      console.error('Error storing offline request:', error);
      toast({
        title: "Error",
        description: "Failed to save request offline.",
        variant: "destructive",
      });
      throw error;
    }
  }, [toast]);

  const syncOfflineData = useCallback(async (): Promise<void> => {
    try {
      await pwaService.syncOfflineData();
      
      // Update offline requests count
      const offlineRequests = await pwaService.getOfflineLeaveRequests();
      setOfflineRequestsCount(offlineRequests.length);
      
      if (offlineRequests.length === 0) {
        toast({
          title: "Sync Complete",
          description: "All offline data has been synchronized.",
        });
      }
    } catch (error) {
      console.error('Error syncing offline data:', error);
      toast({
        title: "Sync Failed",
        description: "Some offline data could not be synchronized.",
        variant: "destructive",
      });
    }
  }, [toast]);

  const showNotification = useCallback(async (
    title: string, 
    options: NotificationOptions = {}
  ): Promise<void> => {
    try {
      await pwaService.showNotification(title, options);
    } catch (error) {
      console.error('Error showing notification:', error);
    }
  }, []);

  const requestNotificationPermission = useCallback(async (): Promise<NotificationPermission> => {
    try {
      const permission = await pwaService.requestNotificationPermission();
      
      if (permission === 'granted') {
        toast({
          title: "Notifications Enabled",
          description: "You'll receive notifications for important updates.",
        });
      } else if (permission === 'denied') {
        toast({
          title: "Notifications Disabled",
          description: "You can enable notifications in your browser settings.",
          variant: "destructive",
        });
      }
      
      return permission;
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return 'denied';
    }
  }, [toast]);

  return {
    isOnline,
    isPWA,
    isInstallable,
    isUpdateAvailable,
    offlineRequestsCount,
    installApp,
    updateApp,
    storeOfflineRequest,
    syncOfflineData,
    showNotification,
    requestNotificationPermission,
  };
}
