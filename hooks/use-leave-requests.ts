"use client"

import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { 
  ILeaveRequestDB, 
  LeaveRequestListResponse, 
  LeaveRequestQueryParams,
  LeaveRequestResponse 
} from '@/types/leave-request';

interface UseLeaveRequestsOptions {
  initialParams?: LeaveRequestQueryParams;
  autoFetch?: boolean;
}

interface UseLeaveRequestsReturn {
  requests: ILeaveRequestDB[];
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  } | null;
  fetchRequests: (params?: LeaveRequestQueryParams) => Promise<void>;
  refreshRequests: () => Promise<void>;
  createRequest: (data: any) => Promise<ILeaveRequestDB | null>;
  updateRequest: (id: string, data: any) => Promise<ILeaveRequestDB | null>;
  deleteRequest: (id: string) => Promise<boolean>;
  approveRequest: (id: string) => Promise<ILeaveRequestDB | null>;
  rejectRequest: (id: string, reason: string) => Promise<ILeaveRequestDB | null>;
}

export function useLeaveRequests(options: UseLeaveRequestsOptions = {}): UseLeaveRequestsReturn {
  const { initialParams = {}, autoFetch = true } = options;
  const { toast } = useToast();

  const [requests, setRequests] = useState<ILeaveRequestDB[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<{
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  } | null>(null);
  const [currentParams, setCurrentParams] = useState<LeaveRequestQueryParams>(initialParams);

  const fetchRequests = useCallback(async (params: LeaveRequestQueryParams = {}) => {
    try {
      setLoading(true);
      setError(null);

      const mergedParams = { ...currentParams, ...params };
      setCurrentParams(mergedParams);

      // Build query string
      const searchParams = new URLSearchParams();
      Object.entries(mergedParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/leave/requests?${searchParams.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch leave requests');
      }

      const data: LeaveRequestListResponse = await response.json();
      setRequests(data.data);
      setPagination(data.pagination);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while fetching leave requests';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [currentParams, toast]);

  const refreshRequests = useCallback(async () => {
    await fetchRequests(currentParams);
  }, [fetchRequests, currentParams]);

  const createRequest = useCallback(async (data: any): Promise<ILeaveRequestDB | null> => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/leave/requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create leave request');
      }

      const result: LeaveRequestResponse = await response.json();
      
      toast({
        title: "Success",
        description: result.message || "Leave request created successfully",
      });

      // Refresh the list
      await refreshRequests();
      
      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while creating leave request';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [refreshRequests, toast]);

  const updateRequest = useCallback(async (id: string, data: any): Promise<ILeaveRequestDB | null> => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/leave/requests/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update leave request');
      }

      const result: LeaveRequestResponse = await response.json();
      
      toast({
        title: "Success",
        description: result.message || "Leave request updated successfully",
      });

      // Update the local state
      setRequests(prev => prev.map(req => 
        req._id.toString() === id ? result.data : req
      ));
      
      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while updating leave request';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const deleteRequest = useCallback(async (id: string): Promise<boolean> => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/leave/requests/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to cancel leave request');
      }

      const result: LeaveRequestResponse = await response.json();
      
      toast({
        title: "Success",
        description: result.message || "Leave request cancelled successfully",
      });

      // Update the local state
      setRequests(prev => prev.map(req => 
        req._id.toString() === id ? result.data : req
      ));
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while cancelling leave request';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const approveRequest = useCallback(async (id: string): Promise<ILeaveRequestDB | null> => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/leave/requests/${id}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to approve leave request');
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: result.message || "Leave request approved successfully",
      });

      // Update the local state
      setRequests(prev => prev.map(req => 
        req._id.toString() === id ? result.data : req
      ));
      
      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while approving leave request';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const rejectRequest = useCallback(async (id: string, reason: string): Promise<ILeaveRequestDB | null> => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/leave/requests/${id}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to reject leave request');
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: result.message || "Leave request rejected successfully",
      });

      // Update the local state
      setRequests(prev => prev.map(req => 
        req._id.toString() === id ? result.data : req
      ));
      
      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while rejecting leave request';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  // Auto-fetch on mount if enabled
  useEffect(() => {
    if (autoFetch) {
      fetchRequests(initialParams);
    }
  }, [autoFetch]); // Only depend on autoFetch to avoid infinite loops

  return {
    requests,
    loading,
    error,
    pagination,
    fetchRequests,
    refreshRequests,
    createRequest,
    updateRequest,
    deleteRequest,
    approveRequest,
    rejectRequest,
  };
}
