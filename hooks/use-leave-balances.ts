"use client"

import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { LeaveBalanceResponse } from '@/types/leave-request';

interface UseLeaveBalancesOptions {
  year?: number;
  autoFetch?: boolean;
}

interface UseLeaveBalancesReturn {
  balances: LeaveBalanceResponse[];
  loading: boolean;
  error: string | null;
  fetchBalances: (year?: number) => Promise<void>;
  refreshBalances: () => Promise<void>;
  adjustBalance: (data: {
    employeeId: string;
    leaveTypeId: string;
    year: number;
    adjustmentType: 'add' | 'subtract' | 'set';
    days: number;
    reason: string;
  }) => Promise<boolean>;
}

export function useLeaveBalances(options: UseLeaveBalancesOptions = {}): UseLeaveBalancesReturn {
  const { year = new Date().getFullYear(), autoFetch = true } = options;
  const { toast } = useToast();

  const [balances, setBalances] = useState<LeaveBalanceResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentYear, setCurrentYear] = useState(year);

  const fetchBalances = useCallback(async (fetchYear: number = currentYear) => {
    try {
      setLoading(true);
      setError(null);
      setCurrentYear(fetchYear);

      const response = await fetch(`/api/leave/balances?year=${fetchYear}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch leave balances');
      }

      const data: LeaveBalanceResponse[] = await response.json();
      setBalances(data);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while fetching leave balances';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [currentYear, toast]);

  const refreshBalances = useCallback(async () => {
    await fetchBalances(currentYear);
  }, [fetchBalances, currentYear]);

  const adjustBalance = useCallback(async (data: {
    employeeId: string;
    leaveTypeId: string;
    year: number;
    adjustmentType: 'add' | 'subtract' | 'set';
    days: number;
    reason: string;
  }): Promise<boolean> => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/leave/balances/adjust', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to adjust leave balance');
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: result.message || "Leave balance adjusted successfully",
      });

      // Refresh balances if the adjustment was for the current year
      if (data.year === currentYear) {
        await refreshBalances();
      }
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while adjusting leave balance';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  }, [currentYear, refreshBalances, toast]);

  // Auto-fetch on mount if enabled
  useEffect(() => {
    if (autoFetch) {
      fetchBalances(year);
    }
  }, [autoFetch]); // Only depend on autoFetch to avoid infinite loops

  return {
    balances,
    loading,
    error,
    fetchBalances,
    refreshBalances,
    adjustBalance,
  };
}
