"use client"

import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';

interface Notification {
  _id: string;
  notificationId: string;
  type: string;
  category: string;
  title: string;
  message: string;
  channels: {
    inApp: {
      read: boolean;
      readAt?: string;
    };
  };
  priority: string;
  actions?: {
    label: string;
    action: string;
    url?: string;
    style: string;
  }[];
  createdAt: string;
  senderId?: {
    firstName: string;
    lastName: string;
  };
}

interface UseNotificationsReturn {
  notifications: Notification[];
  loading: boolean;
  error: string | null;
  unreadCount: number;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  } | null;
  fetchNotifications: (params?: any) => Promise<void>;
  markAsRead: (notificationId: string) => Promise<boolean>;
  markAllAsRead: () => Promise<boolean>;
  refreshNotifications: () => Promise<void>;
}

export function useNotifications(): UseNotificationsReturn {
  const { toast } = useToast();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [unreadCount, setUnreadCount] = useState(0);
  const [pagination, setPagination] = useState<{
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  } | null>(null);
  const [currentParams, setCurrentParams] = useState<any>({});

  const fetchNotifications = useCallback(async (params: any = {}) => {
    try {
      setLoading(true);
      setError(null);

      const mergedParams = { ...currentParams, ...params };
      setCurrentParams(mergedParams);

      // Build query string
      const searchParams = new URLSearchParams();
      Object.entries(mergedParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/notifications?${searchParams.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch notifications');
      }

      const data = await response.json();
      setNotifications(data.data);
      setPagination(data.pagination);
      setUnreadCount(data.pagination.unreadCount);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while fetching notifications';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [currentParams, toast]);

  const refreshNotifications = useCallback(async () => {
    await fetchNotifications(currentParams);
  }, [fetchNotifications, currentParams]);

  const markAsRead = useCallback(async (notificationId: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}/read`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to mark notification as read');
      }

      // Update local state
      setNotifications(prev => prev.map(notif => 
        notif._id === notificationId 
          ? { 
              ...notif, 
              channels: { 
                ...notif.channels, 
                inApp: { 
                  ...notif.channels.inApp, 
                  read: true, 
                  readAt: new Date().toISOString() 
                } 
              } 
            }
          : notif
      ));

      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1));

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while marking notification as read';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return false;
    }
  }, [toast]);

  const markAllAsRead = useCallback(async (): Promise<boolean> => {
    try {
      // Mark all unread notifications as read
      const unreadNotifications = notifications.filter(n => !n.channels.inApp.read);
      
      const promises = unreadNotifications.map(notif => 
        fetch(`/api/notifications/${notif._id}/read`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        })
      );

      const results = await Promise.allSettled(promises);
      
      // Check if all requests succeeded
      const allSucceeded = results.every(result => 
        result.status === 'fulfilled' && result.value.ok
      );

      if (allSucceeded) {
        // Update local state
        setNotifications(prev => prev.map(notif => ({
          ...notif,
          channels: {
            ...notif.channels,
            inApp: {
              ...notif.channels.inApp,
              read: true,
              readAt: new Date().toISOString()
            }
          }
        })));

        setUnreadCount(0);

        toast({
          title: "Success",
          description: "All notifications marked as read",
        });

        return true;
      } else {
        throw new Error('Some notifications could not be marked as read');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while marking all notifications as read';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return false;
    }
  }, [notifications, toast]);

  // Auto-fetch on mount
  useEffect(() => {
    fetchNotifications();
  }, []); // Only run once on mount

  // Set up polling for real-time updates (optional)
  useEffect(() => {
    const interval = setInterval(() => {
      // Only refresh if we're on the first page to avoid disrupting pagination
      if (!currentParams.page || currentParams.page === 1) {
        refreshNotifications();
      }
    }, 30000); // Poll every 30 seconds

    return () => clearInterval(interval);
  }, [refreshNotifications, currentParams.page]);

  return {
    notifications,
    loading,
    error,
    unreadCount,
    pagination,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    refreshNotifications,
  };
}
