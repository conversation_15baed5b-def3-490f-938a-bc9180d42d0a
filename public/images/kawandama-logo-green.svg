<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with green gradient -->
  <circle cx="100" cy="100" r="95" fill="url(#greenGradient)" stroke="url(#greenBorder)" stroke-width="2"/>
  
  <!-- Plantation/Agricultural symbol -->
  <g transform="translate(100, 100)">
    <!-- Hills/Mountains in background -->
    <path d="M-60 20 L-40 -10 L-20 5 L0 -15 L20 0 L40 -20 L60 10 L60 40 L-60 40 Z" 
          fill="url(#hillGradient)" opacity="0.7"/>
    
    <!-- Trees/Plantation -->
    <g>
      <!-- Tree 1 -->
      <circle cx="-30" cy="0" r="12" fill="#2D5016"/>
      <rect x="-32" y="0" width="4" height="20" fill="#8B4513"/>
      
      <!-- Tree 2 -->
      <circle cx="0" cy="-5" r="15" fill="#2D5016"/>
      <rect x="-2" y="-5" width="4" height="25" fill="#8B4513"/>
      
      <!-- Tree 3 -->
      <circle cx="30" cy="2" r="10" fill="#2D5016"/>
      <rect x="28" y="2" width="4" height="18" fill="#8B4513"/>
    </g>
    
    <!-- Plantation rows/fields -->
    <g opacity="0.6">
      <path d="M-50 25 Q-25 20 0 25 Q25 30 50 25" stroke="#4ADE80" stroke-width="2" fill="none"/>
      <path d="M-50 30 Q-25 25 0 30 Q25 35 50 30" stroke="#4ADE80" stroke-width="2" fill="none"/>
      <path d="M-50 35 Q-25 30 0 35 Q25 40 50 35" stroke="#4ADE80" stroke-width="2" fill="none"/>
    </g>
    
    <!-- Central emblem/crest -->
    <circle cx="0" cy="-35" r="20" fill="url(#crestGradient)" stroke="#1F2937" stroke-width="2"/>
    <text x="0" y="-30" text-anchor="middle" font-family="serif" font-size="16" font-weight="bold" fill="white">KH</text>
  </g>
  
  <!-- Company name arc -->
  <path id="topArc" d="M 40 50 A 60 60 0 0 1 160 50" fill="none"/>
  <text font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1F2937">
    <textPath href="#topArc" startOffset="50%" text-anchor="middle">
      KAWANDAMA HILLS
    </textPath>
  </text>
  
  <path id="bottomArc" d="M 50 150 A 50 50 0 0 0 150 150" fill="none"/>
  <text font-family="Arial, sans-serif" font-size="12" font-weight="normal" fill="#1F2937">
    <textPath href="#bottomArc" startOffset="50%" text-anchor="middle">
      PLANTATION
    </textPath>
  </text>
  
  <!-- Gradients and definitions -->
  <defs>
    <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#22C55E;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#16A34A;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#15803D;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="greenBorder" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#15803D;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#166534;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="hillGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#84CC16;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#65A30D;stop-opacity:1" />
    </linearGradient>
    
    <radialGradient id="crestGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#059669;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </radialGradient>
  </defs>
</svg>
