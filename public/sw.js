// Service Worker for Kawandama HR System
const CACHE_NAME = 'kawandama-hr-v1.0.0';
const STATIC_CACHE_NAME = 'kawandama-hr-static-v1.0.0';
const DYNAMIC_CACHE_NAME = 'kawandama-hr-dynamic-v1.0.0';

// Assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/login',
  '/dashboard',
  '/leave/balance',
  '/leave/request',
  '/notifications',
  '/offline',
  '/manifest.json',
  // Add your CSS and JS bundles here
  // These would be generated by your build process
];

// API endpoints that should be cached
const CACHEABLE_API_ROUTES = [
  '/api/leave/types',
  '/api/leave/balances',
  '/api/notifications',
  '/api/user/profile'
];

// API endpoints that should work offline with cached data
const OFFLINE_FALLBACK_ROUTES = [
  '/api/leave/balances',
  '/api/notifications'
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('Service Worker: Static assets cached');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('Service Worker: Error caching static assets', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE_NAME && 
                cacheName !== DYNAMIC_CACHE_NAME &&
                cacheName !== CACHE_NAME) {
              console.log('Service Worker: Deleting old cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - handle requests
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Handle API requests
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleApiRequest(request));
    return;
  }

  // Handle page requests
  event.respondWith(handlePageRequest(request));
});

// Handle API requests with network-first strategy
async function handleApiRequest(request) {
  const url = new URL(request.url);
  
  try {
    // Try network first
    const networkResponse = await fetch(request);
    
    // Cache successful responses for cacheable routes
    if (networkResponse.ok && CACHEABLE_API_ROUTES.some(route => url.pathname.startsWith(route))) {
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Service Worker: Network failed for API request', url.pathname);
    
    // Try to serve from cache for offline fallback routes
    if (OFFLINE_FALLBACK_ROUTES.some(route => url.pathname.startsWith(route))) {
      const cachedResponse = await caches.match(request);
      if (cachedResponse) {
        console.log('Service Worker: Serving API request from cache', url.pathname);
        return cachedResponse;
      }
    }
    
    // Return offline response for critical API endpoints
    return new Response(
      JSON.stringify({
        error: 'Offline',
        message: 'This feature is not available offline',
        offline: true
      }),
      {
        status: 503,
        statusText: 'Service Unavailable',
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}

// Handle page requests with cache-first strategy for static assets
async function handlePageRequest(request) {
  const url = new URL(request.url);
  
  // For navigation requests, try network first, then cache, then offline page
  if (request.mode === 'navigate') {
    try {
      const networkResponse = await fetch(request);
      
      // Cache successful page responses
      if (networkResponse.ok) {
        const cache = await caches.open(DYNAMIC_CACHE_NAME);
        cache.put(request, networkResponse.clone());
      }
      
      return networkResponse;
    } catch (error) {
      console.log('Service Worker: Network failed for page request', url.pathname);
      
      // Try to serve from cache
      const cachedResponse = await caches.match(request);
      if (cachedResponse) {
        console.log('Service Worker: Serving page from cache', url.pathname);
        return cachedResponse;
      }
      
      // Serve offline page
      const offlineResponse = await caches.match('/offline');
      if (offlineResponse) {
        return offlineResponse;
      }
      
      // Fallback offline response
      return new Response(
        `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Offline - Kawandama HR</title>
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .offline-container { max-width: 400px; margin: 0 auto; }
            .offline-icon { font-size: 64px; margin-bottom: 20px; }
            h1 { color: #333; }
            p { color: #666; margin-bottom: 30px; }
            button { 
              background: #16a34a; 
              color: white; 
              border: none; 
              padding: 12px 24px; 
              border-radius: 6px; 
              cursor: pointer; 
              font-size: 16px;
            }
            button:hover { background: #15803d; }
          </style>
        </head>
        <body>
          <div class="offline-container">
            <div class="offline-icon">📱</div>
            <h1>You're Offline</h1>
            <p>Please check your internet connection and try again.</p>
            <button onclick="window.location.reload()">Try Again</button>
          </div>
        </body>
        </html>
        `,
        {
          status: 200,
          headers: {
            'Content-Type': 'text/html'
          }
        }
      );
    }
  }
  
  // For other requests, try cache first, then network
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    
    // Cache successful responses
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Service Worker: Failed to fetch resource', request.url);
    throw error;
  }
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('Service Worker: Background sync triggered', event.tag);
  
  if (event.tag === 'leave-request-sync') {
    event.waitUntil(syncLeaveRequests());
  }
  
  if (event.tag === 'notification-sync') {
    event.waitUntil(syncNotifications());
  }
});

// Sync offline leave requests when back online
async function syncLeaveRequests() {
  try {
    // Get offline leave requests from IndexedDB
    const offlineRequests = await getOfflineLeaveRequests();
    
    for (const request of offlineRequests) {
      try {
        const response = await fetch('/api/leave/requests', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(request.data)
        });
        
        if (response.ok) {
          // Remove from offline storage
          await removeOfflineLeaveRequest(request.id);
          console.log('Service Worker: Synced offline leave request', request.id);
        }
      } catch (error) {
        console.error('Service Worker: Failed to sync leave request', request.id, error);
      }
    }
  } catch (error) {
    console.error('Service Worker: Error syncing leave requests', error);
  }
}

// Sync notifications when back online
async function syncNotifications() {
  try {
    // Fetch latest notifications
    const response = await fetch('/api/notifications');
    if (response.ok) {
      const data = await response.json();
      // Update cached notifications
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      cache.put('/api/notifications', new Response(JSON.stringify(data)));
    }
  } catch (error) {
    console.error('Service Worker: Error syncing notifications', error);
  }
}

// Placeholder functions for IndexedDB operations
async function getOfflineLeaveRequests() {
  // Implementation would use IndexedDB to store offline requests
  return [];
}

async function removeOfflineLeaveRequest(id) {
  // Implementation would remove the request from IndexedDB
  console.log('Removing offline request', id);
}

// Push notification handling
self.addEventListener('push', (event) => {
  console.log('Service Worker: Push notification received');
  
  const options = {
    body: 'You have new notifications in Kawandama HR',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'View Notifications',
        icon: '/icons/checkmark.png'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/icons/xmark.png'
      }
    ]
  };
  
  if (event.data) {
    const data = event.data.json();
    options.body = data.message || options.body;
    options.data = { ...options.data, ...data };
  }
  
  event.waitUntil(
    self.registration.showNotification('Kawandama HR', options)
  );
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  console.log('Service Worker: Notification clicked');
  
  event.notification.close();
  
  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/notifications')
    );
  } else if (event.action === 'close') {
    // Just close the notification
  } else {
    // Default action - open the app
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});
