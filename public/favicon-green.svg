<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="url(#greenGradient)" stroke="#166534" stroke-width="1"/>
  
  <!-- Simple plantation symbol -->
  <g transform="translate(16, 16)">
    <!-- Hills -->
    <path d="M-10 3 L-6 -2 L-2 1 L2 -3 L6 0 L10 -4 L10 6 L-10 6 Z" 
          fill="#84CC16" opacity="0.8"/>
    
    <!-- Trees -->
    <circle cx="-4" cy="0" r="2.5" fill="#15803D"/>
    <rect x="-4.5" y="0" width="1" height="4" fill="#8B4513"/>
    
    <circle cx="0" cy="-1" r="3" fill="#15803D"/>
    <rect x="-0.5" y="-1" width="1" height="5" fill="#8B4513"/>
    
    <circle cx="4" cy="1" r="2" fill="#15803D"/>
    <rect x="3.5" y="1" width="1" height="3" fill="#8B4513"/>
    
    <!-- Central emblem -->
    <circle cx="0" cy="-6" r="4" fill="#047857"/>
    <text x="0" y="-4" text-anchor="middle" font-family="serif" font-size="4" font-weight="bold" fill="white">K</text>
  </g>
  
  <defs>
    <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#22C55E;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#15803D;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
