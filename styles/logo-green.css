/* Green filter styles for Kawandama Hills Plantation logo */

.logo-green-filter {
  /* Apply green color filter to make the logo appear more green */
  filter: 
    hue-rotate(90deg)     /* Shift colors towards green */
    saturate(1.3)         /* Increase saturation for more vibrant green */
    brightness(1.1)       /* Slightly brighten */
    contrast(1.1);        /* Increase contrast */
  
  transition: filter 0.3s ease;
}

.logo-green-filter:hover {
  filter: 
    hue-rotate(90deg)
    saturate(1.5)
    brightness(1.2)
    contrast(1.2);
}

/* Alternative green overlay approach */
.logo-green-overlay {
  position: relative;
}

.logo-green-overlay::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(34, 197, 94, 0.3) 0%,    /* green-500 with opacity */
    rgba(22, 163, 74, 0.4) 50%,   /* green-600 with opacity */
    rgba(21, 128, 61, 0.3) 100%   /* green-700 with opacity */
  );
  mix-blend-mode: multiply;
  pointer-events: none;
  border-radius: inherit;
}

/* Green tint variations */
.logo-green-tint-light {
  filter: 
    sepia(1) 
    hue-rotate(90deg) 
    saturate(2) 
    brightness(1.2);
}

.logo-green-tint-medium {
  filter: 
    sepia(0.8) 
    hue-rotate(100deg) 
    saturate(1.8) 
    brightness(1.1) 
    contrast(1.1);
}

.logo-green-tint-dark {
  filter: 
    sepia(0.6) 
    hue-rotate(110deg) 
    saturate(1.5) 
    brightness(0.9) 
    contrast(1.2);
}

/* Plantation-themed green color scheme */
.plantation-green-primary {
  color: #16a34a; /* green-600 */
}

.plantation-green-secondary {
  color: #22c55e; /* green-500 */
}

.plantation-green-accent {
  color: #15803d; /* green-700 */
}

.plantation-green-light {
  color: #4ade80; /* green-400 */
}

.plantation-green-dark {
  color: #166534; /* green-800 */
}

/* Background variants for green theme */
.bg-plantation-green {
  background: linear-gradient(135deg, 
    #16a34a 0%,   /* green-600 */
    #15803d 100%  /* green-700 */
  );
}

.bg-plantation-green-light {
  background: linear-gradient(135deg, 
    #22c55e 0%,   /* green-500 */
    #16a34a 100%  /* green-600 */
  );
}

.bg-plantation-green-subtle {
  background: linear-gradient(135deg, 
    #dcfce7 0%,   /* green-100 */
    #bbf7d0 100%  /* green-200 */
  );
}
