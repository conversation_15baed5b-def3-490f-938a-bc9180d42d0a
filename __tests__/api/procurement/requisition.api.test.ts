import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { NextRequest } from 'next/server';
import { GET, POST, PUT, DELETE } from '@/app/api/procurement/requisition/route';
import { GET as GetById, PUT as UpdateById, DELETE as DeleteById } from '@/app/api/procurement/requisition/[id]/route';
import { POST as BulkImport } from '@/app/api/procurement/requisition/bulk-import/route';
import { POST as BulkDelete } from '@/app/api/procurement/requisition/bulk-delete/route';

// Mock dependencies
vi.mock('@/lib/backend/auth/auth', () => ({
  getCurrentUser: vi.fn(),
}));

vi.mock('@/lib/backend/auth/permissions', () => ({
  hasRequiredPermissions: vi.fn(),
}));

vi.mock('@/lib/backend/services/procurement/RequisitionService', () => ({
  RequisitionService: vi.fn().mockImplementation(() => ({
    getRequisitions: vi.fn(),
    getRequisitionDetails: vi.fn(),
    createRequisition: vi.fn(),
    updateRequisition: vi.fn(),
    deleteRequisition: vi.fn(),
  })),
}));

vi.mock('@/lib/backend/services/audit-deletion-service', () => ({
  AuditDeletionService: vi.fn().mockImplementation(() => ({
    createDeletionRecord: vi.fn(),
  })),
}));

vi.mock('@/lib/backend/services/error-service', () => ({
  errorService: {
    createApiResponse: vi.fn(),
  },
  ErrorType: {
    UNAUTHORIZED: 'UNAUTHORIZED',
    FORBIDDEN: 'FORBIDDEN',
    VALIDATION: 'VALIDATION',
    SYSTEM: 'SYSTEM',
  },
  ErrorSeverity: {
    LOW: 'LOW',
    MEDIUM: 'MEDIUM',
    HIGH: 'HIGH',
  },
}));

vi.mock('@/lib/backend/utils/logger', () => ({
  default: {
    info: vi.fn(),
    error: vi.fn(),
  },
  LogCategory: {
    PROCUREMENT: 'PROCUREMENT',
  },
}));

vi.mock('@/types/user-roles', () => ({
  UserRole: {
    SUPER_ADMIN: 'SUPER_ADMIN',
    SYSTEM_ADMIN: 'SYSTEM_ADMIN',
    PROCUREMENT_MANAGER: 'PROCUREMENT_MANAGER',
    PROCUREMENT_OFFICER: 'PROCUREMENT_OFFICER',
    DEPARTMENT_HEAD: 'DEPARTMENT_HEAD',
    EMPLOYEE: 'EMPLOYEE',
  },
}));

// Import mocked modules
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { RequisitionService } from '@/lib/backend/services/procurement/RequisitionService';
import { AuditDeletionService } from '@/lib/backend/services/audit-deletion-service';

const mockUser = {
  id: 'user123',
  email: '<EMAIL>',
  name: 'Test User',
  role: 'PROCUREMENT_OFFICER',
  departmentId: 'dept123',
};

const mockRequisition = {
  _id: 'req123',
  requisitionNumber: 'REQ-2024-001',
  title: 'Test Requisition',
  description: 'Test description',
  departmentId: 'dept123',
  requestedBy: 'user123',
  status: 'draft',
  priority: 'medium',
  items: [
    {
      name: 'Test Item',
      quantity: 1,
      unit: 'pcs',
      estimatedUnitPrice: 100000,
      estimatedTotal: 100000,
    },
  ],
  totalAmount: 100000,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

describe('Requisition API Routes', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (getCurrentUser as any).mockResolvedValue(mockUser);
    (hasRequiredPermissions as any).mockReturnValue(true);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('GET /api/procurement/requisition', () => {
    it('returns requisitions for authenticated user', async () => {
      const mockRequisitions = [mockRequisition];
      const mockRequisitionService = {
        getRequisitions: vi.fn().mockResolvedValue({
          docs: mockRequisitions,
          totalDocs: 1,
          page: 1,
          totalPages: 1,
        }),
      };
      (RequisitionService as any).mockImplementation(() => mockRequisitionService);

      const request = new NextRequest('http://localhost/api/procurement/requisition');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.docs).toEqual(mockRequisitions);
      expect(mockRequisitionService.getRequisitions).toHaveBeenCalled();
    });

    it('returns 401 for unauthenticated user', async () => {
      (getCurrentUser as any).mockResolvedValue(null);

      const request = new NextRequest('http://localhost/api/procurement/requisition');
      const response = await GET(request);

      expect(response.status).toBe(401);
    });

    it('returns 403 for unauthorized user', async () => {
      (hasRequiredPermissions as any).mockReturnValue(false);

      const request = new NextRequest('http://localhost/api/procurement/requisition');
      const response = await GET(request);

      expect(response.status).toBe(403);
    });
  });

  describe('POST /api/procurement/requisition', () => {
    it('creates new requisition successfully', async () => {
      const mockRequisitionService = {
        createRequisition: vi.fn().mockResolvedValue(mockRequisition),
      };
      (RequisitionService as any).mockImplementation(() => mockRequisitionService);

      const requestBody = {
        title: 'Test Requisition',
        description: 'Test description',
        departmentId: 'dept123',
        priority: 'medium',
        items: [
          {
            name: 'Test Item',
            quantity: 1,
            unit: 'pcs',
            estimatedUnitPrice: 100000,
          },
        ],
      };

      const request = new NextRequest('http://localhost/api/procurement/requisition', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.success).toBe(true);
      expect(data.data).toEqual(mockRequisition);
      expect(mockRequisitionService.createRequisition).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Test Requisition',
          createdBy: mockUser.id,
        })
      );
    });

    it('validates required fields', async () => {
      const request = new NextRequest('http://localhost/api/procurement/requisition', {
        method: 'POST',
        body: JSON.stringify({}),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);

      expect(response.status).toBe(400);
    });
  });

  describe('GET /api/procurement/requisition/[id]', () => {
    it('returns requisition details for authorized user', async () => {
      const mockRequisitionService = {
        getRequisitionDetails: vi.fn().mockResolvedValue(mockRequisition),
      };
      (RequisitionService as any).mockImplementation(() => mockRequisitionService);

      const request = new NextRequest('http://localhost/api/procurement/requisition/req123');
      const response = await GetById(request, { params: { id: 'req123' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toEqual(mockRequisition);
    });

    it('returns 404 for non-existent requisition', async () => {
      const mockRequisitionService = {
        getRequisitionDetails: vi.fn().mockResolvedValue(null),
      };
      (RequisitionService as any).mockImplementation(() => mockRequisitionService);

      const request = new NextRequest('http://localhost/api/procurement/requisition/nonexistent');
      const response = await GetById(request, { params: { id: 'nonexistent' } });

      expect(response.status).toBe(404);
    });
  });

  describe('PUT /api/procurement/requisition/[id]', () => {
    it('updates requisition successfully', async () => {
      const updatedRequisition = { ...mockRequisition, title: 'Updated Title' };
      const mockRequisitionService = {
        getRequisitionDetails: vi.fn().mockResolvedValue(mockRequisition),
        updateRequisition: vi.fn().mockResolvedValue(updatedRequisition),
      };
      (RequisitionService as any).mockImplementation(() => mockRequisitionService);

      const requestBody = { title: 'Updated Title' };
      const request = new NextRequest('http://localhost/api/procurement/requisition/req123', {
        method: 'PUT',
        body: JSON.stringify(requestBody),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await UpdateById(request, { params: { id: 'req123' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toEqual(updatedRequisition);
    });

    it('prevents unauthorized updates', async () => {
      const approvedRequisition = { ...mockRequisition, status: 'approved' };
      const mockRequisitionService = {
        getRequisitionDetails: vi.fn().mockResolvedValue(approvedRequisition),
      };
      (RequisitionService as any).mockImplementation(() => mockRequisitionService);
      (hasRequiredPermissions as any).mockReturnValue(false);

      const request = new NextRequest('http://localhost/api/procurement/requisition/req123', {
        method: 'PUT',
        body: JSON.stringify({ title: 'Updated Title' }),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await UpdateById(request, { params: { id: 'req123' } });

      expect(response.status).toBe(403);
    });
  });

  describe('DELETE /api/procurement/requisition/[id]', () => {
    it('deletes requisition with audit trail', async () => {
      const mockRequisitionService = {
        getRequisitionDetails: vi.fn().mockResolvedValue(mockRequisition),
        deleteRequisition: vi.fn().mockResolvedValue(mockRequisition),
      };
      const mockAuditService = {
        createDeletionRecord: vi.fn().mockResolvedValue({ _id: 'audit123' }),
      };
      (RequisitionService as any).mockImplementation(() => mockRequisitionService);
      (AuditDeletionService as any).mockImplementation(() => mockAuditService);

      const requestBody = {
        deletionReason: 'Test deletion reason for audit trail',
        context: { department: 'Procurement' },
      };
      const request = new NextRequest('http://localhost/api/procurement/requisition/req123', {
        method: 'DELETE',
        body: JSON.stringify(requestBody),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await DeleteById(request, { params: { id: 'req123' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(mockAuditService.createDeletionRecord).toHaveBeenCalledWith(
        expect.objectContaining({
          entityType: 'Requisition',
          entityId: 'req123',
          deletionReason: 'Test deletion reason for audit trail',
          deletedBy: mockUser.id,
        })
      );
    });

    it('prevents deletion of approved requisitions', async () => {
      const approvedRequisition = { ...mockRequisition, status: 'approved' };
      const mockRequisitionService = {
        getRequisitionDetails: vi.fn().mockResolvedValue(approvedRequisition),
      };
      (RequisitionService as any).mockImplementation(() => mockRequisitionService);

      const request = new NextRequest('http://localhost/api/procurement/requisition/req123', {
        method: 'DELETE',
        body: JSON.stringify({ deletionReason: 'Test reason' }),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await DeleteById(request, { params: { id: 'req123' } });

      expect(response.status).toBe(400);
    });
  });

  describe('POST /api/procurement/requisition/bulk-import', () => {
    it('processes bulk import successfully', async () => {
      const mockRequisitionService = {
        createRequisition: vi.fn().mockResolvedValue(mockRequisition),
      };
      (RequisitionService as any).mockImplementation(() => mockRequisitionService);

      // Mock FormData with CSV content
      const csvContent = 'title,description,departmentName,requestorEmail,priority,itemName,quantity,unit,estimatedUnitPrice\nTest Requisition,Test description,IT Department,<EMAIL>,medium,Test Item,1,pcs,100000';
      const file = new File([csvContent], 'test.csv', { type: 'text/csv' });
      
      const formData = new FormData();
      formData.append('file', file);

      const request = new NextRequest('http://localhost/api/procurement/requisition/bulk-import', {
        method: 'POST',
        body: formData,
      });

      const response = await BulkImport(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.successfulRequisitions).toHaveLength(1);
    });

    it('validates file type', async () => {
      const textFile = new File(['invalid content'], 'test.txt', { type: 'text/plain' });
      const formData = new FormData();
      formData.append('file', textFile);

      const request = new NextRequest('http://localhost/api/procurement/requisition/bulk-import', {
        method: 'POST',
        body: formData,
      });

      const response = await BulkImport(request);

      expect(response.status).toBe(400);
    });
  });

  describe('POST /api/procurement/requisition/bulk-delete', () => {
    it('processes bulk deletion with audit trail', async () => {
      const mockRequisitionService = {
        getRequisitionDetails: vi.fn().mockResolvedValue(mockRequisition),
        deleteRequisition: vi.fn().mockResolvedValue(mockRequisition),
      };
      const mockAuditService = {
        createDeletionRecord: vi.fn().mockResolvedValue({ _id: 'audit123' }),
      };
      (RequisitionService as any).mockImplementation(() => mockRequisitionService);
      (AuditDeletionService as any).mockImplementation(() => mockAuditService);

      const requestBody = {
        requisitionIds: ['req123'],
        deletionReason: 'Bulk deletion test with proper audit trail',
        context: { department: 'Procurement' },
      };

      const request = new NextRequest('http://localhost/api/procurement/requisition/bulk-delete', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await BulkDelete(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.deletedCount).toBe(1);
      expect(data.data.auditRecordsCreated).toBe(1);
    });

    it('validates deletion reason length', async () => {
      const requestBody = {
        requisitionIds: ['req123'],
        deletionReason: 'Short', // Too short
      };

      const request = new NextRequest('http://localhost/api/procurement/requisition/bulk-delete', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await BulkDelete(request);

      expect(response.status).toBe(400);
    });
  });
});
