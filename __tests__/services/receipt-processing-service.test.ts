import { receiptProcessingService } from '@/lib/services/accounting/receipt-processing-service';
import { ReceiptProcessingStatus } from '@/lib/services/accounting/receipt-processing-service';

// Mock the logger
jest.mock('@/lib/backend/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
  },
  LogCategory: {
    ACCOUNTING: 'accounting',
  },
}));

// Mock File API for testing
class MockFile {
  name: string;
  size: number;
  type: string;
  lastModified: number;

  constructor(name: string, size: number, type: string) {
    this.name = name;
    this.size = size;
    this.type = type;
    this.lastModified = Date.now();
  }
}

// Mock Image constructor
global.Image = class {
  onload: (() => void) | null = null;
  onerror: (() => void) | null = null;
  src: string = '';
  naturalWidth: number = 800;
  naturalHeight: number = 600;

  constructor() {
    setTimeout(() => {
      if (this.onload) {
        this.onload();
      }
    }, 10);
  }
} as any;

// Mock Canvas API
global.HTMLCanvasElement.prototype.getContext = jest.fn(() => ({
  drawImage: jest.fn(),
  getImageData: jest.fn(() => ({
    data: new Uint8ClampedArray(800 * 600 * 4),
    width: 800,
    height: 600,
  })),
  putImageData: jest.fn(),
})) as any;

global.HTMLCanvasElement.prototype.toDataURL = jest.fn(() => 'data:image/png;base64,mock-data');

// Mock URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'mock-url');
global.URL.revokeObjectURL = jest.fn();

describe('ReceiptProcessingService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('processReceipt', () => {
    it('should successfully process a valid receipt image', async () => {
      const mockFile = new MockFile('receipt.jpg', 1024 * 1024, 'image/jpeg') as any;
      const expenditureId = 'exp-123';
      const userId = 'user-456';

      const result = await receiptProcessingService.processReceipt(mockFile, expenditureId, userId);

      expect(result).toBeDefined();
      expect(result.expenditureId).toBe(expenditureId);
      expect(result.fileName).toBe('receipt.jpg');
      expect(result.fileSize).toBe(1024 * 1024);
      expect(result.mimeType).toBe('image/jpeg');
      expect(result.status).toBe(ReceiptProcessingStatus.PROCESSED);
      expect(result.extractedData).toBeDefined();
      expect(result.ocrConfidence).toBeGreaterThan(0);
      expect(result.validationResults).toBeDefined();
      expect(result.metadata).toBeDefined();
    });

    it('should reject files that are too large', async () => {
      const mockFile = new MockFile('large-receipt.jpg', 15 * 1024 * 1024, 'image/jpeg') as any;
      const expenditureId = 'exp-123';
      const userId = 'user-456';

      await expect(
        receiptProcessingService.processReceipt(mockFile, expenditureId, userId)
      ).rejects.toThrow('File size exceeds 10MB limit');
    });

    it('should reject invalid file types', async () => {
      const mockFile = new MockFile('document.txt', 1024, 'text/plain') as any;
      const expenditureId = 'exp-123';
      const userId = 'user-456';

      await expect(
        receiptProcessingService.processReceipt(mockFile, expenditureId, userId)
      ).rejects.toThrow('Invalid file type');
    });

    it('should handle PNG files correctly', async () => {
      const mockFile = new MockFile('receipt.png', 2 * 1024 * 1024, 'image/png') as any;
      const expenditureId = 'exp-123';
      const userId = 'user-456';

      const result = await receiptProcessingService.processReceipt(mockFile, expenditureId, userId);

      expect(result.mimeType).toBe('image/png');
      expect(result.status).toBe(ReceiptProcessingStatus.PROCESSED);
    });

    it('should handle PDF files correctly', async () => {
      const mockFile = new MockFile('receipt.pdf', 3 * 1024 * 1024, 'application/pdf') as any;
      const expenditureId = 'exp-123';
      const userId = 'user-456';

      const result = await receiptProcessingService.processReceipt(mockFile, expenditureId, userId);

      expect(result.mimeType).toBe('application/pdf');
      expect(result.status).toBe(ReceiptProcessingStatus.PROCESSED);
    });
  });

  describe('getProcessingStatistics', () => {
    it('should return processing statistics', async () => {
      const stats = await receiptProcessingService.getProcessingStatistics();

      expect(stats).toBeDefined();
      expect(stats.totalProcessed).toBeGreaterThan(0);
      expect(stats.successRate).toBeGreaterThan(0);
      expect(stats.successRate).toBeLessThanOrEqual(100);
      expect(stats.averageConfidence).toBeGreaterThan(0);
      expect(stats.averageConfidence).toBeLessThanOrEqual(100);
      expect(stats.processingTimeAverage).toBeGreaterThan(0);
      expect(Array.isArray(stats.commonErrors)).toBe(true);
    });

    it('should return statistics for date range', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-12-31');

      const stats = await receiptProcessingService.getProcessingStatistics(startDate, endDate);

      expect(stats).toBeDefined();
      expect(stats.totalProcessed).toBeGreaterThan(0);
    });
  });

  describe('reprocessReceipt', () => {
    it('should throw error for unimplemented reprocessing', async () => {
      const receiptId = 'receipt-123';
      const userId = 'user-456';

      await expect(
        receiptProcessingService.reprocessReceipt(receiptId, userId)
      ).rejects.toThrow('Reprocessing not implemented yet');
    });
  });

  describe('correctExtractedData', () => {
    it('should throw error for unimplemented manual correction', async () => {
      const receiptId = 'receipt-123';
      const corrections = { merchantName: 'Corrected Merchant' };
      const userId = 'user-456';

      await expect(
        receiptProcessingService.correctExtractedData(receiptId, corrections, userId)
      ).rejects.toThrow('Manual correction not implemented yet');
    });
  });

  describe('extracted data validation', () => {
    it('should extract merchant name from OCR text', async () => {
      const mockFile = new MockFile('receipt.jpg', 1024 * 1024, 'image/jpeg') as any;
      const expenditureId = 'exp-123';
      const userId = 'user-456';

      const result = await receiptProcessingService.processReceipt(mockFile, expenditureId, userId);

      expect(result.extractedData?.merchantName).toBeDefined();
      expect(typeof result.extractedData?.merchantName).toBe('string');
    });

    it('should extract total amount from OCR text', async () => {
      const mockFile = new MockFile('receipt.jpg', 1024 * 1024, 'image/jpeg') as any;
      const expenditureId = 'exp-123';
      const userId = 'user-456';

      const result = await receiptProcessingService.processReceipt(mockFile, expenditureId, userId);

      expect(result.extractedData?.totalAmount).toBeDefined();
      expect(typeof result.extractedData?.totalAmount).toBe('number');
      expect(result.extractedData?.totalAmount).toBeGreaterThan(0);
    });

    it('should extract currency information', async () => {
      const mockFile = new MockFile('receipt.jpg', 1024 * 1024, 'image/jpeg') as any;
      const expenditureId = 'exp-123';
      const userId = 'user-456';

      const result = await receiptProcessingService.processReceipt(mockFile, expenditureId, userId);

      expect(result.extractedData?.currency).toBeDefined();
      expect(result.extractedData?.currency).toBe('MWK');
    });
  });

  describe('validation results', () => {
    it('should provide validation results for extracted data', async () => {
      const mockFile = new MockFile('receipt.jpg', 1024 * 1024, 'image/jpeg') as any;
      const expenditureId = 'exp-123';
      const userId = 'user-456';

      const result = await receiptProcessingService.processReceipt(mockFile, expenditureId, userId);

      expect(result.validationResults).toBeDefined();
      expect(Array.isArray(result.validationResults)).toBe(true);
      expect(result.validationResults!.length).toBeGreaterThan(0);

      // Check validation result structure
      const validation = result.validationResults![0];
      expect(validation.field).toBeDefined();
      expect(typeof validation.isValid).toBe('boolean');
      expect(typeof validation.confidence).toBe('number');
      expect(validation.confidence).toBeGreaterThanOrEqual(0);
      expect(validation.confidence).toBeLessThanOrEqual(100);
    });

    it('should validate critical fields', async () => {
      const mockFile = new MockFile('receipt.jpg', 1024 * 1024, 'image/jpeg') as any;
      const expenditureId = 'exp-123';
      const userId = 'user-456';

      const result = await receiptProcessingService.processReceipt(mockFile, expenditureId, userId);

      const validationFields = result.validationResults!.map(v => v.field);
      expect(validationFields).toContain('merchantName');
      expect(validationFields).toContain('totalAmount');
      expect(validationFields).toContain('transactionDate');
    });
  });

  describe('metadata extraction', () => {
    it('should extract image metadata', async () => {
      const mockFile = new MockFile('receipt.jpg', 1024 * 1024, 'image/jpeg') as any;
      const expenditureId = 'exp-123';
      const userId = 'user-456';

      const result = await receiptProcessingService.processReceipt(mockFile, expenditureId, userId);

      expect(result.metadata).toBeDefined();
      expect(result.metadata.imageWidth).toBe(800);
      expect(result.metadata.imageHeight).toBe(600);
      expect(result.metadata.fileFormat).toBe('image/jpeg');
      expect(result.metadata.ocrEngine).toBe('Tesseract.js');
      expect(result.metadata.ocrVersion).toBe('4.0.0');
    });
  });

  describe('error handling', () => {
    it('should handle processing errors gracefully', async () => {
      // Mock Image to fail
      const originalImage = global.Image;
      global.Image = class {
        onerror: (() => void) | null = null;
        src: string = '';

        constructor() {
          setTimeout(() => {
            if (this.onerror) {
              this.onerror();
            }
          }, 10);
        }
      } as any;

      const mockFile = new MockFile('receipt.jpg', 1024 * 1024, 'image/jpeg') as any;
      const expenditureId = 'exp-123';
      const userId = 'user-456';

      await expect(
        receiptProcessingService.processReceipt(mockFile, expenditureId, userId)
      ).rejects.toThrow();

      // Restore original Image
      global.Image = originalImage;
    });
  });
});
