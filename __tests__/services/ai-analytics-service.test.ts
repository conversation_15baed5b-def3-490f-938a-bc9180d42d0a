import { aiAnalyticsService } from '@/lib/services/accounting/ai-analytics-service';
import {
  TrendDirection,
  AnomalyType,
  AnomalySeverity,
  RiskLevel
} from '@/lib/services/accounting/ai-analytics-service';
import { ExpenditureCategory, ExpenditurePriority } from '@/models/accounting/Expenditure';

// Mock the logger
jest.mock('@/lib/backend/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
  },
  LogCategory: {
    ACCOUNTING: 'accounting',
  },
}));

describe('AIAnalyticsService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('analyzeSpendingPatterns', () => {
    it('should analyze spending patterns for given date range', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-12-31');

      const patterns = await aiAnalyticsService.analyzeSpendingPatterns(startDate, endDate);

      expect(Array.isArray(patterns)).toBe(true);
      expect(patterns.length).toBeGreaterThan(0);

      // Check pattern structure
      const pattern = patterns[0];
      expect(pattern.category).toBeDefined();
      expect(Object.values(ExpenditureCategory)).toContain(pattern.category);
      expect(pattern.department).toBeDefined();
      expect(typeof pattern.averageAmount).toBe('number');
      expect(pattern.averageAmount).toBeGreaterThan(0);
      expect(typeof pattern.frequency).toBe('number');
      expect(pattern.frequency).toBeGreaterThan(0);
      expect(pattern.seasonality).toBeDefined();
      expect(Object.values(TrendDirection)).toContain(pattern.trend);
      expect(typeof pattern.confidence).toBe('number');
      expect(pattern.confidence).toBeGreaterThanOrEqual(0);
      expect(pattern.confidence).toBeLessThanOrEqual(1);
    });

    it('should filter patterns by departments', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-12-31');
      const filters = { departments: ['Administration', 'Finance'] };

      const patterns = await aiAnalyticsService.analyzeSpendingPatterns(startDate, endDate, filters);

      expect(Array.isArray(patterns)).toBe(true);
      patterns.forEach(pattern => {
        expect(['Administration', 'Finance']).toContain(pattern.department);
      });
    });

    it('should filter patterns by categories', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-12-31');
      const filters = { categories: [ExpenditureCategory.OPERATIONAL, ExpenditureCategory.CAPITAL] };

      const patterns = await aiAnalyticsService.analyzeSpendingPatterns(startDate, endDate, filters);

      expect(Array.isArray(patterns)).toBe(true);
      patterns.forEach(pattern => {
        expect([ExpenditureCategory.OPERATIONAL, ExpenditureCategory.CAPITAL]).toContain(pattern.category);
      });
    });

    it('should include seasonality data in patterns', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-12-31');

      const patterns = await aiAnalyticsService.analyzeSpendingPatterns(startDate, endDate);
      const pattern = patterns[0];

      expect(pattern.seasonality).toBeDefined();
      expect(pattern.seasonality.monthlyVariation).toBeDefined();
      expect(pattern.seasonality.quarterlyPattern).toBeDefined();
      expect(Array.isArray(pattern.seasonality.peakMonths)).toBe(true);
      expect(Array.isArray(pattern.seasonality.lowMonths)).toBe(true);

      // Check monthly variation structure
      const monthlyKeys = Object.keys(pattern.seasonality.monthlyVariation);
      expect(monthlyKeys.length).toBe(12);
      monthlyKeys.forEach(month => {
        const variation = pattern.seasonality.monthlyVariation[month];
        expect(typeof variation).toBe('number');
        expect(variation).toBeGreaterThan(0);
      });
    });
  });

  describe('generateSpendingForecast', () => {
    it('should generate forecasts for specified months', async () => {
      const category = ExpenditureCategory.OPERATIONAL;
      const department = 'Administration';
      const forecastMonths = 6;

      const forecasts = await aiAnalyticsService.generateSpendingForecast(
        category,
        department,
        forecastMonths
      );

      expect(Array.isArray(forecasts)).toBe(true);
      expect(forecasts.length).toBe(forecastMonths);

      // Check forecast structure
      const forecast = forecasts[0];
      expect(forecast.period).toBeDefined();
      expect(forecast.period).toMatch(/^\d{4}-\d{2}$/); // YYYY-MM format
      expect(typeof forecast.predictedAmount).toBe('number');
      expect(forecast.predictedAmount).toBeGreaterThan(0);
      expect(typeof forecast.confidence).toBe('number');
      expect(forecast.confidence).toBeGreaterThan(0);
      expect(forecast.confidence).toBeLessThanOrEqual(1);
      expect(Array.isArray(forecast.factors)).toBe(true);
      expect(Object.values(RiskLevel)).toContain(forecast.riskLevel);
      expect(Array.isArray(forecast.recommendations)).toBe(true);
    });

    it('should include forecast factors', async () => {
      const category = ExpenditureCategory.OPERATIONAL;
      const department = 'Administration';

      const forecasts = await aiAnalyticsService.generateSpendingForecast(category, department, 3);
      const forecast = forecasts[0];

      expect(forecast.factors.length).toBeGreaterThan(0);
      
      const factor = forecast.factors[0];
      expect(factor.name).toBeDefined();
      expect(typeof factor.impact).toBe('number');
      expect(factor.description).toBeDefined();
    });

    it('should have decreasing confidence over time', async () => {
      const category = ExpenditureCategory.OPERATIONAL;
      const department = 'Administration';

      const forecasts = await aiAnalyticsService.generateSpendingForecast(category, department, 6);

      for (let i = 1; i < forecasts.length; i++) {
        expect(forecasts[i].confidence).toBeLessThanOrEqual(forecasts[i - 1].confidence);
      }
    });
  });

  describe('detectAnomalies', () => {
    const mockExpenditures = [
      {
        id: 'exp-1',
        amount: 100000, // Very high amount
        category: ExpenditureCategory.OPERATIONAL,
        department: 'Administration',
        vendorId: 'vendor-1',
        date: new Date('2024-06-15')
      },
      {
        id: 'exp-2',
        amount: 25000, // Normal amount
        category: ExpenditureCategory.OPERATIONAL,
        department: 'Administration',
        vendorId: 'vendor-2',
        date: new Date('2024-06-16')
      },
      {
        id: 'exp-3',
        amount: 15000, // Normal amount
        category: ExpenditureCategory.SUPPLIES,
        department: 'ICT',
        vendorId: 'vendor-3',
        date: new Date('2024-06-17')
      }
    ];

    it('should detect anomalies in expenditure data', async () => {
      const anomalies = await aiAnalyticsService.detectAnomalies(mockExpenditures);

      expect(Array.isArray(anomalies)).toBe(true);
      
      if (anomalies.length > 0) {
        const anomaly = anomalies[0];
        expect(anomaly.id).toBeDefined();
        expect(anomaly.expenditureId).toBeDefined();
        expect(Object.values(AnomalyType)).toContain(anomaly.type);
        expect(Object.values(AnomalySeverity)).toContain(anomaly.severity);
        expect(anomaly.description).toBeDefined();
        expect(anomaly.detectedAt).toBeInstanceOf(Date);
        expect(typeof anomaly.confidence).toBe('number');
        expect(anomaly.confidence).toBeGreaterThanOrEqual(0);
        expect(anomaly.confidence).toBeLessThanOrEqual(1);
        expect(anomaly.suggestedAction).toBeDefined();
        expect(anomaly.metadata).toBeDefined();
      }
    });

    it('should filter anomalies by confidence threshold', async () => {
      const anomalies = await aiAnalyticsService.detectAnomalies(mockExpenditures);

      anomalies.forEach(anomaly => {
        expect(anomaly.confidence).toBeGreaterThanOrEqual(0.7); // Default threshold
      });
    });

    it('should detect different types of anomalies', async () => {
      const anomalies = await aiAnalyticsService.detectAnomalies(mockExpenditures);

      if (anomalies.length > 0) {
        const anomalyTypes = anomalies.map(a => a.type);
        const uniqueTypes = [...new Set(anomalyTypes)];
        
        // Should detect at least one type of anomaly
        expect(uniqueTypes.length).toBeGreaterThan(0);
        uniqueTypes.forEach(type => {
          expect(Object.values(AnomalyType)).toContain(type);
        });
      }
    });
  });

  describe('optimizeBudgetAllocation', () => {
    const mockBudgets = [
      {
        department: 'Administration',
        category: ExpenditureCategory.OPERATIONAL,
        allocated: 100000,
        spent: 65000 // 65% utilization - under-utilized
      },
      {
        department: 'ICT',
        category: ExpenditureCategory.TECHNOLOGY,
        allocated: 200000,
        spent: 195000 // 97.5% utilization - over-utilized
      },
      {
        department: 'Finance',
        category: ExpenditureCategory.PROFESSIONAL_SERVICES,
        allocated: 80000,
        spent: 45000 // 56% utilization - under-utilized
      }
    ];

    it('should generate budget optimization recommendations', async () => {
      const optimizations = await aiAnalyticsService.optimizeBudgetAllocation(mockBudgets);

      expect(Array.isArray(optimizations)).toBe(true);
      
      if (optimizations.length > 0) {
        const optimization = optimizations[0];
        expect(optimization.department).toBeDefined();
        expect(Object.values(ExpenditureCategory)).toContain(optimization.category);
        expect(typeof optimization.currentAllocation).toBe('number');
        expect(typeof optimization.suggestedAllocation).toBe('number');
        expect(typeof optimization.potentialSavings).toBe('number');
        expect(optimization.reasoning).toBeDefined();
        expect(typeof optimization.confidence).toBe('number');
        expect(optimization.confidence).toBeGreaterThan(0);
        expect(optimization.confidence).toBeLessThanOrEqual(1);
        expect(Array.isArray(optimization.implementationSteps)).toBe(true);
      }
    });

    it('should recommend budget reduction for under-utilized budgets', async () => {
      const optimizations = await aiAnalyticsService.optimizeBudgetAllocation(mockBudgets);

      const underUtilizedOptimizations = optimizations.filter(opt => 
        opt.suggestedAllocation < opt.currentAllocation
      );

      expect(underUtilizedOptimizations.length).toBeGreaterThan(0);
      underUtilizedOptimizations.forEach(opt => {
        expect(opt.potentialSavings).toBeGreaterThan(0);
        expect(opt.reasoning).toContain('under-utilized');
      });
    });

    it('should recommend budget increase for over-utilized budgets', async () => {
      const optimizations = await aiAnalyticsService.optimizeBudgetAllocation(mockBudgets);

      const overUtilizedOptimizations = optimizations.filter(opt => 
        opt.suggestedAllocation > opt.currentAllocation
      );

      if (overUtilizedOptimizations.length > 0) {
        overUtilizedOptimizations.forEach(opt => {
          expect(opt.potentialSavings).toBeLessThan(0); // Negative savings = increased budget
          expect(opt.reasoning).toMatch(/exhausted|increase/i);
        });
      }
    });
  });

  describe('analyzeVendorPerformance', () => {
    it('should analyze vendor performance', async () => {
      const vendorId = 'vendor-123';

      const insight = await aiAnalyticsService.analyzeVendorPerformance(vendorId);

      expect(insight.vendorId).toBe(vendorId);
      expect(insight.vendorName).toBeDefined();
      expect(typeof insight.performanceScore).toBe('number');
      expect(insight.performanceScore).toBeGreaterThanOrEqual(0);
      expect(insight.performanceScore).toBeLessThanOrEqual(100);
      expect(typeof insight.costEfficiency).toBe('number');
      expect(insight.costEfficiency).toBeGreaterThanOrEqual(0);
      expect(insight.costEfficiency).toBeLessThanOrEqual(100);
      expect(typeof insight.reliabilityScore).toBe('number');
      expect(insight.reliabilityScore).toBeGreaterThanOrEqual(0);
      expect(insight.reliabilityScore).toBeLessThanOrEqual(100);
      expect(Object.values(RiskLevel)).toContain(insight.riskAssessment);
      expect(Array.isArray(insight.recommendations)).toBe(true);
      expect(Array.isArray(insight.trends)).toBe(true);
    });

    it('should provide vendor recommendations', async () => {
      const vendorId = 'vendor-123';

      const insight = await aiAnalyticsService.analyzeVendorPerformance(vendorId);

      if (insight.recommendations.length > 0) {
        const recommendation = insight.recommendations[0];
        expect(['cost_optimization', 'performance_improvement', 'risk_mitigation']).toContain(recommendation.type);
        expect(recommendation.description).toBeDefined();
        expect(typeof recommendation.potentialImpact).toBe('number');
        expect(Object.values(ExpenditurePriority)).toContain(recommendation.priority);
      }
    });

    it('should provide vendor trends', async () => {
      const vendorId = 'vendor-123';

      const insight = await aiAnalyticsService.analyzeVendorPerformance(vendorId);

      if (insight.trends.length > 0) {
        const trend = insight.trends[0];
        expect(trend.metric).toBeDefined();
        expect(Object.values(TrendDirection)).toContain(trend.direction);
        expect(typeof trend.magnitude).toBe('number');
        expect(trend.timeframe).toBeDefined();
      }
    });

    it('should assess vendor risk correctly', async () => {
      const vendorId = 'vendor-123';

      const insight = await aiAnalyticsService.analyzeVendorPerformance(vendorId);

      // Risk should be based on performance and reliability scores
      const averageScore = (insight.performanceScore + insight.reliabilityScore) / 2;
      
      if (averageScore < 70) {
        expect(insight.riskAssessment).toBe(RiskLevel.HIGH);
      } else if (averageScore < 80) {
        expect(insight.riskAssessment).toBe(RiskLevel.MEDIUM);
      } else {
        expect(insight.riskAssessment).toBe(RiskLevel.LOW);
      }
    });
  });

  describe('error handling', () => {
    it('should handle errors in spending pattern analysis', async () => {
      // Test with invalid date range
      const startDate = new Date('invalid-date');
      const endDate = new Date('2024-12-31');

      await expect(
        aiAnalyticsService.analyzeSpendingPatterns(startDate, endDate)
      ).rejects.toThrow();
    });

    it('should handle errors in forecast generation', async () => {
      const category = 'INVALID_CATEGORY' as ExpenditureCategory;
      const department = 'Administration';

      // Should handle gracefully or throw appropriate error
      try {
        await aiAnalyticsService.generateSpendingForecast(category, department, 6);
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
      }
    });
  });
});
