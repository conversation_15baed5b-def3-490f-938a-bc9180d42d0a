import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { PurchaseRequisitions } from '@/components/procurement/purchase-requisitions';
import { BulkRequisitionUpload } from '@/components/procurement/requisitions/bulk-requisition-upload';
import { BulkRequisitionDelete } from '@/components/procurement/requisitions/bulk-requisition-delete';

// Mock fetch globally
global.fetch = vi.fn();

// Mock toast
vi.mock('@/components/ui/use-toast', () => ({
  toast: vi.fn(),
}));

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
}));

// Mock data
const mockRequisitions = [
  {
    _id: '1',
    requisitionNumber: 'REQ-2024-001',
    title: 'Office Equipment Purchase',
    description: 'Laptops and monitors for new employees',
    departmentId: {
      _id: 'dept1',
      name: 'IT Department'
    },
    requestedBy: {
      _id: 'user1',
      name: '<PERSON>a',
      email: '<EMAIL>'
    },
    status: 'pending_approval' as const,
    priority: 'high' as const,
    items: [
      {
        name: 'Dell Laptop',
        description: 'Business laptop',
        quantity: 3,
        unit: 'pcs',
        estimatedUnitPrice: 450000,
        estimatedTotal: 1350000,
        category: 'IT Equipment'
      }
    ],
    totalAmount: 1350000,
    createdAt: '2024-01-15T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z'
  },
  {
    _id: '2',
    requisitionNumber: 'REQ-2024-002',
    title: 'Office Supplies',
    description: 'Monthly office supplies',
    departmentId: {
      _id: 'dept2',
      name: 'HR Department'
    },
    requestedBy: {
      _id: 'user2',
      name: 'Mary Phiri',
      email: '<EMAIL>'
    },
    status: 'approved' as const,
    priority: 'medium' as const,
    items: [
      {
        name: 'A4 Paper',
        description: 'White printing paper',
        quantity: 20,
        unit: 'ream',
        estimatedUnitPrice: 3500,
        estimatedTotal: 70000,
        category: 'Office Supplies'
      }
    ],
    totalAmount: 70000,
    createdAt: '2024-01-14T00:00:00Z',
    updatedAt: '2024-01-14T00:00:00Z'
  }
];

describe('Requisition Module', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock successful API responses
    (global.fetch as any).mockResolvedValue({
      ok: true,
      json: async () => ({ success: true, data: mockRequisitions }),
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('PurchaseRequisitions Component', () => {
    it('renders requisitions list correctly', async () => {
      render(<PurchaseRequisitions />);

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('REQ-2024-001')).toBeInTheDocument();
        expect(screen.getByText('REQ-2024-002')).toBeInTheDocument();
      });

      // Check if requisition details are displayed
      expect(screen.getByText('Office Equipment Purchase')).toBeInTheDocument();
      expect(screen.getByText('Office Supplies')).toBeInTheDocument();
      expect(screen.getByText('IT Department')).toBeInTheDocument();
      expect(screen.getByText('HR Department')).toBeInTheDocument();
    });

    it('filters requisitions by search term', async () => {
      render(<PurchaseRequisitions />);

      await waitFor(() => {
        expect(screen.getByText('REQ-2024-001')).toBeInTheDocument();
      });

      // Search for specific requisition
      const searchInput = screen.getByPlaceholderText('Search requisitions...');
      fireEvent.change(searchInput, { target: { value: 'Office Equipment' } });

      // Should show only matching requisition
      expect(screen.getByText('Office Equipment Purchase')).toBeInTheDocument();
      expect(screen.queryByText('Office Supplies')).not.toBeInTheDocument();
    });

    it('filters requisitions by status', async () => {
      render(<PurchaseRequisitions />);

      await waitFor(() => {
        expect(screen.getByText('REQ-2024-001')).toBeInTheDocument();
      });

      // Filter by approved status
      const statusFilter = screen.getByRole('combobox');
      fireEvent.click(statusFilter);
      
      const approvedOption = screen.getByText('Approved');
      fireEvent.click(approvedOption);

      // Should show only approved requisitions
      expect(screen.queryByText('REQ-2024-001')).not.toBeInTheDocument();
      expect(screen.getByText('REQ-2024-002')).toBeInTheDocument();
    });

    it('handles bulk selection correctly', async () => {
      render(<PurchaseRequisitions />);

      await waitFor(() => {
        expect(screen.getByText('REQ-2024-001')).toBeInTheDocument();
      });

      // Select individual requisition
      const checkboxes = screen.getAllByRole('checkbox');
      const firstRequisitionCheckbox = checkboxes[1]; // Skip the "select all" checkbox
      
      fireEvent.click(firstRequisitionCheckbox);

      // Should show bulk actions bar
      expect(screen.getByText('1 requisition selected')).toBeInTheDocument();
      expect(screen.getByText('Delete Selected')).toBeInTheDocument();
    });

    it('handles requisition approval', async () => {
      render(<PurchaseRequisitions />);

      await waitFor(() => {
        expect(screen.getByText('REQ-2024-001')).toBeInTheDocument();
      });

      // Mock approval API call
      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      });

      // Find and click approve button for pending requisition
      const approveButtons = screen.getAllByRole('button');
      const approveButton = approveButtons.find(button => 
        button.querySelector('svg')?.classList.contains('lucide-check-circle')
      );

      if (approveButton) {
        fireEvent.click(approveButton);

        await waitFor(() => {
          expect(global.fetch).toHaveBeenCalledWith(
            '/api/procurement/requisition/1',
            expect.objectContaining({
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: expect.stringContaining('"status":"approved"'),
            })
          );
        });
      }
    });

    it('handles requisition deletion with audit trail', async () => {
      // Mock window.prompt
      window.prompt = vi.fn().mockReturnValue('Test deletion reason for audit trail');

      render(<PurchaseRequisitions />);

      await waitFor(() => {
        expect(screen.getByText('REQ-2024-001')).toBeInTheDocument();
      });

      // Mock deletion API call
      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      });

      // Find and click delete button
      const deleteButtons = screen.getAllByRole('button');
      const deleteButton = deleteButtons.find(button => 
        button.querySelector('svg')?.classList.contains('lucide-trash')
      );

      if (deleteButton) {
        fireEvent.click(deleteButton);

        await waitFor(() => {
          expect(global.fetch).toHaveBeenCalledWith(
            '/api/procurement/requisition/1',
            expect.objectContaining({
              method: 'DELETE',
              headers: { 'Content-Type': 'application/json' },
              body: expect.stringContaining('Test deletion reason'),
            })
          );
        });
      }
    });
  });

  describe('BulkRequisitionUpload Component', () => {
    it('renders upload interface correctly', () => {
      render(<BulkRequisitionUpload />);

      expect(screen.getByText('Upload Requisitions')).toBeInTheDocument();
      expect(screen.getByText('Download Template')).toBeInTheDocument();
      expect(screen.getByText('Upload a file')).toBeInTheDocument();
    });

    it('validates file type correctly', () => {
      render(<BulkRequisitionUpload />);

      const fileInput = screen.getByLabelText('Upload a file');
      
      // Create a mock file with invalid type
      const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });
      
      fireEvent.change(fileInput, { target: { files: [invalidFile] } });

      expect(screen.getByText('Please upload a CSV or Excel file')).toBeInTheDocument();
    });

    it('validates file size correctly', () => {
      render(<BulkRequisitionUpload />);

      const fileInput = screen.getByLabelText('Upload a file');
      
      // Create a mock file that's too large (>10MB)
      const largeFile = new File(['x'.repeat(11 * 1024 * 1024)], 'large.xlsx', { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
      
      fireEvent.change(fileInput, { target: { files: [largeFile] } });

      expect(screen.getByText('File size exceeds 10MB limit')).toBeInTheDocument();
    });

    it('handles successful upload', async () => {
      const mockOnSuccess = vi.fn();
      render(<BulkRequisitionUpload onSuccess={mockOnSuccess} />);

      // Mock successful upload response
      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            totalRows: 5,
            successfulRequisitions: [
              {
                row: 2,
                requisitionNumber: 'REQ-2024-003',
                title: 'Test Requisition',
                totalAmount: 100000,
                status: 'draft'
              }
            ],
            failedRequisitions: [],
            duplicateRequisitions: []
          }
        }),
      });

      const fileInput = screen.getByLabelText('Upload a file');
      const validFile = new File(['test'], 'test.xlsx', { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
      
      fireEvent.change(fileInput, { target: { files: [validFile] } });

      const uploadButton = screen.getByText('Upload Requisitions');
      fireEvent.click(uploadButton);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          '/api/procurement/requisition/bulk-import',
          expect.objectContaining({
            method: 'POST',
            body: expect.any(FormData),
          })
        );
      });

      await waitFor(() => {
        expect(mockOnSuccess).toHaveBeenCalled();
      });
    });

    it('downloads template correctly', () => {
      // Mock window.location.href
      delete (window as any).location;
      window.location = { href: '' } as any;

      render(<BulkRequisitionUpload />);

      // Switch to template tab
      const templateTab = screen.getByText('Download Template');
      fireEvent.click(templateTab);

      const downloadButton = screen.getByText('Download Requisition Template');
      fireEvent.click(downloadButton);

      expect(window.location.href).toContain('/api/procurement/requisition/template');
    });
  });

  describe('BulkRequisitionDelete Component', () => {
    const mockSelectedRequisitions = [mockRequisitions[0]];
    const mockSelectedIds = ['1'];

    it('renders delete interface correctly', () => {
      render(
        <BulkRequisitionDelete
          selectedRequisitionIds={mockSelectedIds}
          selectedRequisitions={mockSelectedRequisitions}
        />
      );

      expect(screen.getByText('Delete Requisitions')).toBeInTheDocument();
      expect(screen.getByText('You are about to delete 1 requisition. This action cannot be undone.')).toBeInTheDocument();
      expect(screen.getByText('REQ-2024-001')).toBeInTheDocument();
    });

    it('validates deletion reason', () => {
      render(
        <BulkRequisitionDelete
          selectedRequisitionIds={mockSelectedIds}
          selectedRequisitions={mockSelectedRequisitions}
        />
      );

      const deleteButton = screen.getByText('Delete 1 Requisition');
      
      // Should be disabled initially
      expect(deleteButton).toBeDisabled();

      // Enter short reason
      const reasonTextarea = screen.getByPlaceholderText(/Please provide a detailed reason/);
      fireEvent.change(reasonTextarea, { target: { value: 'Short reason' } });

      // Should still be disabled
      expect(deleteButton).toBeDisabled();

      // Enter valid reason
      fireEvent.change(reasonTextarea, { 
        target: { value: 'This is a valid deletion reason that meets the minimum character requirement' } 
      });

      // Should now be enabled
      expect(deleteButton).not.toBeDisabled();
    });

    it('warns about undeletable requisitions', () => {
      const approvedRequisition = {
        ...mockRequisitions[0],
        status: 'approved' as const
      };

      render(
        <BulkRequisitionDelete
          selectedRequisitionIds={['1']}
          selectedRequisitions={[approvedRequisition]}
        />
      );

      expect(screen.getByText('Warning: Some requisitions cannot be deleted')).toBeInTheDocument();
      expect(screen.getByText(/cannot be deleted because they have been approved/)).toBeInTheDocument();
    });

    it('handles successful bulk deletion', async () => {
      const mockOnSuccess = vi.fn();
      
      render(
        <BulkRequisitionDelete
          selectedRequisitionIds={mockSelectedIds}
          selectedRequisitions={mockSelectedRequisitions}
          onSuccess={mockOnSuccess}
        />
      );

      // Mock successful deletion response
      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            requestedCount: 1,
            deletedCount: 1,
            auditRecordsCreated: 1,
            errors: []
          }
        }),
      });

      // Enter valid deletion reason
      const reasonTextarea = screen.getByPlaceholderText(/Please provide a detailed reason/);
      fireEvent.change(reasonTextarea, { 
        target: { value: 'Valid deletion reason for testing purposes with audit trail' } 
      });

      const deleteButton = screen.getByText('Delete 1 Requisition');
      fireEvent.click(deleteButton);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          '/api/procurement/requisition/bulk-delete',
          expect.objectContaining({
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: expect.stringContaining('Valid deletion reason'),
          })
        );
      });

      await waitFor(() => {
        expect(mockOnSuccess).toHaveBeenCalled();
      });
    });
  });

  describe('API Integration', () => {
    it('handles API errors gracefully', async () => {
      // Mock API error
      (global.fetch as any).mockRejectedValueOnce(new Error('Network error'));

      render(<PurchaseRequisitions />);

      await waitFor(() => {
        // Should handle error gracefully and show appropriate message
        expect(screen.getByText('Loading requisitions...')).toBeInTheDocument();
      });
    });

    it('handles 404 responses correctly', async () => {
      (global.fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({ error: 'Not found' }),
      });

      render(<PurchaseRequisitions />);

      await waitFor(() => {
        // Should handle 404 gracefully
        expect(screen.queryByText('REQ-2024-001')).not.toBeInTheDocument();
      });
    });

    it('handles unauthorized access correctly', async () => {
      (global.fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => ({ error: 'Unauthorized' }),
      });

      render(<PurchaseRequisitions />);

      await waitFor(() => {
        // Should handle unauthorized access
        expect(screen.queryByText('REQ-2024-001')).not.toBeInTheDocument();
      });
    });
  });
});
