import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ReceiptUpload } from '@/components/accounting/expenditures/receipt-upload';
import { receiptProcessingService } from '@/lib/services/accounting/receipt-processing-service';

// Mock the receipt processing service
jest.mock('@/lib/services/accounting/receipt-processing-service', () => ({
  receiptProcessingService: {
    processReceipt: jest.fn(),
  },
}));

// Mock react-dropzone
jest.mock('react-dropzone', () => ({
  useDropzone: jest.fn(({ onDrop, accept, maxFiles, disabled }) => ({
    getRootProps: () => ({
      'data-testid': 'dropzone',
      onClick: () => {},
    }),
    getInputProps: () => ({
      'data-testid': 'file-input',
      type: 'file',
    }),
    isDragActive: false,
  })),
}));

// Mock URL.createObjectURL and revokeObjectURL
global.URL.createObjectURL = jest.fn(() => 'mock-url');
global.URL.revokeObjectURL = jest.fn();

const mockReceiptProcessingService = receiptProcessingService as jest.Mocked<typeof receiptProcessingService>;

describe('ReceiptUpload', () => {
  const defaultProps = {
    expenditureId: 'exp-123',
    onReceiptProcessed: jest.fn(),
    onExtractedDataUpdate: jest.fn(),
    maxFiles: 5,
    disabled: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock successful receipt processing
    mockReceiptProcessingService.processReceipt.mockResolvedValue({
      id: 'receipt-123',
      expenditureId: 'exp-123',
      fileName: 'test-receipt.jpg',
      fileSize: 1024 * 1024,
      mimeType: 'image/jpeg',
      uploadedAt: new Date(),
      processedAt: new Date(),
      status: 'processed' as any,
      extractedData: {
        merchantName: 'Test Merchant',
        totalAmount: 25000,
        currency: 'MWK',
        transactionDate: new Date(),
        paymentMethod: 'Cash',
      },
      ocrConfidence: 85.5,
      validationResults: [
        {
          field: 'merchantName',
          isValid: true,
          confidence: 90,
        },
        {
          field: 'totalAmount',
          isValid: true,
          confidence: 95,
        },
      ],
      metadata: {
        imageWidth: 800,
        imageHeight: 600,
        fileFormat: 'image/jpeg',
        colorSpace: 'RGB',
        ocrEngine: 'Tesseract.js',
        ocrVersion: '4.0.0',
      },
    });
  });

  it('should render upload area when no files uploaded', () => {
    render(<ReceiptUpload {...defaultProps} />);

    expect(screen.getByText('Receipt Upload & Processing')).toBeInTheDocument();
    expect(screen.getByText('Upload receipt images or PDFs for automatic data extraction')).toBeInTheDocument();
    expect(screen.getByText('Drag & drop receipts here')).toBeInTheDocument();
    expect(screen.getByText('Take Photo')).toBeInTheDocument();
  });

  it('should display help text', () => {
    render(<ReceiptUpload {...defaultProps} />);

    expect(screen.getByText(/Ensure receipts are clear and well-lit/)).toBeInTheDocument();
    expect(screen.getByText(/Supported formats: JPEG, PNG, WebP, PDF/)).toBeInTheDocument();
    expect(screen.getByText(/Extracted data will be automatically filled/)).toBeInTheDocument();
  });

  it('should handle file upload and processing', async () => {
    const user = userEvent.setup();
    render(<ReceiptUpload {...defaultProps} />);

    // Create a mock file
    const file = new File(['test content'], 'test-receipt.jpg', {
      type: 'image/jpeg',
    });

    // Simulate file drop
    const dropzone = screen.getByTestId('dropzone');
    
    // Mock the onDrop callback from useDropzone
    const { useDropzone } = require('react-dropzone');
    const mockOnDrop = jest.fn();
    useDropzone.mockReturnValue({
      getRootProps: () => ({
        'data-testid': 'dropzone',
        onClick: mockOnDrop,
      }),
      getInputProps: () => ({
        'data-testid': 'file-input',
        type: 'file',
      }),
      isDragActive: false,
    });

    // Trigger file upload
    fireEvent.click(dropzone);
    
    // Wait for processing to complete
    await waitFor(() => {
      expect(mockReceiptProcessingService.processReceipt).toHaveBeenCalledWith(
        expect.any(Object),
        'exp-123',
        'current-user'
      );
    });
  });

  it('should display extracted data after successful processing', async () => {
    render(<ReceiptUpload {...defaultProps} />);

    // Simulate successful file processing by directly calling the callback
    const mockExtractedData = {
      merchantName: 'Test Merchant',
      totalAmount: 25000,
      currency: 'MWK',
      transactionDate: new Date(),
      paymentMethod: 'Cash',
    };

    // Trigger the extracted data update
    defaultProps.onExtractedDataUpdate(mockExtractedData);

    expect(defaultProps.onExtractedDataUpdate).toHaveBeenCalledWith(mockExtractedData);
  });

  it('should call onReceiptProcessed when processing completes', async () => {
    render(<ReceiptUpload {...defaultProps} />);

    const mockReceiptData = {
      id: 'receipt-123',
      expenditureId: 'exp-123',
      fileName: 'test-receipt.jpg',
      extractedData: {
        merchantName: 'Test Merchant',
        totalAmount: 25000,
      },
    };

    // Trigger the receipt processed callback
    defaultProps.onReceiptProcessed(mockReceiptData as any);

    expect(defaultProps.onReceiptProcessed).toHaveBeenCalledWith(mockReceiptData);
  });

  it('should disable upload when disabled prop is true', () => {
    render(<ReceiptUpload {...defaultProps} disabled={true} />);

    const takePhotoButton = screen.getByText('Take Photo');
    expect(takePhotoButton).toBeDisabled();
  });

  it('should respect maxFiles limit', () => {
    render(<ReceiptUpload {...defaultProps} maxFiles={2} />);

    expect(screen.getByText(/Uploaded Receipts \(0\/2\)/)).toBeInTheDocument();
  });

  it('should handle processing errors gracefully', async () => {
    // Mock processing failure
    mockReceiptProcessingService.processReceipt.mockRejectedValue(
      new Error('Processing failed')
    );

    render(<ReceiptUpload {...defaultProps} />);

    // The component should handle the error internally
    // and display appropriate error state
    expect(screen.getByText('Receipt Upload & Processing')).toBeInTheDocument();
  });

  it('should display processing status', () => {
    render(<ReceiptUpload {...defaultProps} />);

    // Initially should not show processing status
    expect(screen.queryByText(/Processing receipts with OCR/)).not.toBeInTheDocument();
  });

  it('should format file sizes correctly', () => {
    // This would be tested through the component's internal file display
    // when files are uploaded and displayed in the list
    render(<ReceiptUpload {...defaultProps} />);
    
    // The component should be ready to display file information
    expect(screen.getByText('Receipt Upload & Processing')).toBeInTheDocument();
  });

  it('should handle camera capture button click', async () => {
    const user = userEvent.setup();
    render(<ReceiptUpload {...defaultProps} />);

    const cameraButton = screen.getByText('Take Photo');
    await user.click(cameraButton);

    // Should trigger file input click
    expect(cameraButton).toBeInTheDocument();
  });

  it('should validate file types and sizes', () => {
    render(<ReceiptUpload {...defaultProps} />);

    // The component should display supported formats
    expect(screen.getByText(/JPEG, PNG, WebP, PDF/)).toBeInTheDocument();
    expect(screen.getByText(/max 10MB/)).toBeInTheDocument();
  });

  it('should handle multiple file uploads', () => {
    render(<ReceiptUpload {...defaultProps} maxFiles={3} />);

    // Should show correct file count
    expect(screen.getByText(/\(0\/3\)/)).toBeInTheDocument();
  });

  it('should provide confidence indicators', () => {
    render(<ReceiptUpload {...defaultProps} />);

    // Component should be ready to display confidence indicators
    // when files are processed
    expect(screen.getByText('Receipt Upload & Processing')).toBeInTheDocument();
  });

  it('should handle file removal', () => {
    render(<ReceiptUpload {...defaultProps} />);

    // Component should be ready to handle file removal
    // when files are uploaded
    expect(screen.getByText('Receipt Upload & Processing')).toBeInTheDocument();
  });

  it('should handle reprocessing failed receipts', () => {
    render(<ReceiptUpload {...defaultProps} />);

    // Component should be ready to handle reprocessing
    // when files fail to process
    expect(screen.getByText('Receipt Upload & Processing')).toBeInTheDocument();
  });

  it('should display progress during upload and processing', () => {
    render(<ReceiptUpload {...defaultProps} />);

    // Component should be ready to display progress
    // during file processing
    expect(screen.getByText('Receipt Upload & Processing')).toBeInTheDocument();
  });

  it('should auto-fill form data from extracted receipt data', () => {
    const mockOnExtractedDataUpdate = jest.fn();
    
    render(
      <ReceiptUpload 
        {...defaultProps} 
        onExtractedDataUpdate={mockOnExtractedDataUpdate}
      />
    );

    const extractedData = {
      merchantName: 'Test Merchant',
      totalAmount: 25000,
      transactionDate: new Date(),
    };

    // Simulate extracted data update
    mockOnExtractedDataUpdate(extractedData);

    expect(mockOnExtractedDataUpdate).toHaveBeenCalledWith(extractedData);
  });

  it('should handle drag and drop states', () => {
    // Mock isDragActive state
    const { useDropzone } = require('react-dropzone');
    useDropzone.mockReturnValue({
      getRootProps: () => ({
        'data-testid': 'dropzone',
      }),
      getInputProps: () => ({
        'data-testid': 'file-input',
      }),
      isDragActive: true,
    });

    render(<ReceiptUpload {...defaultProps} />);

    expect(screen.getByTestId('dropzone')).toBeInTheDocument();
  });
});
