import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AIAnalyticsDashboard } from '@/components/accounting/expenditures/ai-analytics-dashboard';
import { aiAnalyticsService } from '@/lib/services/accounting/ai-analytics-service';
import { ExpenditureCategory } from '@/models/accounting/Expenditure';

// Mock the AI analytics service
jest.mock('@/lib/services/accounting/ai-analytics-service', () => ({
  aiAnalyticsService: {
    analyzeSpendingPatterns: jest.fn(),
    generateSpendingForecast: jest.fn(),
    detectAnomalies: jest.fn(),
    optimizeBudgetAllocation: jest.fn(),
    analyzeVendorPerformance: jest.fn(),
  },
}));

// Mock Recharts components
jest.mock('recharts', () => ({
  LineChart: ({ children }: any) => <div data-testid="line-chart">{children}</div>,
  Line: () => <div data-testid="line" />,
  AreaChart: ({ children }: any) => <div data-testid="area-chart">{children}</div>,
  Area: () => <div data-testid="area" />,
  BarChart: ({ children }: any) => <div data-testid="bar-chart">{children}</div>,
  Bar: () => <div data-testid="bar" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  Legend: () => <div data-testid="legend" />,
  ResponsiveContainer: ({ children }: any) => <div data-testid="responsive-container">{children}</div>,
  ScatterChart: ({ children }: any) => <div data-testid="scatter-chart">{children}</div>,
  Scatter: () => <div data-testid="scatter" />,
}));

const mockAIAnalyticsService = aiAnalyticsService as jest.Mocked<typeof aiAnalyticsService>;

describe('AIAnalyticsDashboard', () => {
  const defaultProps = {
    expenditureData: [
      {
        id: 'exp-1',
        amount: 25000,
        category: ExpenditureCategory.OPERATIONAL,
        department: 'Administration',
        vendorId: 'vendor-1',
        date: new Date(),
      },
    ],
    onInsightAction: jest.fn(),
  };

  const mockSpendingPatterns = [
    {
      category: ExpenditureCategory.OPERATIONAL,
      department: 'Administration',
      averageAmount: 25000,
      frequency: 10,
      seasonality: {
        monthlyVariation: { Jan: 0.9, Feb: 1.1 },
        quarterlyPattern: { Q1: 0.95, Q2: 1.05 },
        peakMonths: ['Mar', 'Jun'],
        lowMonths: ['Jan', 'Aug'],
      },
      trend: 'increasing' as any,
      confidence: 0.85,
    },
  ];

  const mockForecasts = [
    {
      period: '2024-07',
      predictedAmount: 30000,
      confidence: 0.9,
      factors: [
        { name: 'Seasonal', impact: 5, description: 'Seasonal variation' },
      ],
      riskLevel: 'low' as any,
      recommendations: ['Monitor spending'],
    },
  ];

  const mockAnomalies = [
    {
      id: 'anomaly-1',
      expenditureId: 'exp-1',
      type: 'unusual_amount' as any,
      severity: 'medium' as any,
      description: 'Amount significantly higher than average',
      detectedAt: new Date(),
      confidence: 0.8,
      suggestedAction: 'Review expenditure justification',
      metadata: {},
    },
  ];

  const mockBudgetOptimizations = [
    {
      department: 'Administration',
      category: ExpenditureCategory.OPERATIONAL,
      currentAllocation: 100000,
      suggestedAllocation: 85000,
      potentialSavings: 15000,
      reasoning: 'Budget consistently under-utilized',
      confidence: 0.85,
      implementationSteps: ['Review usage', 'Adjust allocation'],
    },
  ];

  const mockVendorInsights = [
    {
      vendorId: 'vendor-1',
      vendorName: 'Test Vendor',
      performanceScore: 85,
      costEfficiency: 78,
      reliabilityScore: 92,
      riskAssessment: 'low' as any,
      recommendations: [
        {
          type: 'cost_optimization' as any,
          description: 'Negotiate better rates',
          potentialImpact: 10,
          priority: 'medium' as any,
        },
      ],
      trends: [
        {
          metric: 'Cost per transaction',
          direction: 'decreasing' as any,
          magnitude: 5,
          timeframe: 'Last 6 months',
        },
      ],
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock all service methods
    mockAIAnalyticsService.analyzeSpendingPatterns.mockResolvedValue(mockSpendingPatterns);
    mockAIAnalyticsService.generateSpendingForecast.mockResolvedValue(mockForecasts);
    mockAIAnalyticsService.detectAnomalies.mockResolvedValue(mockAnomalies);
    mockAIAnalyticsService.optimizeBudgetAllocation.mockResolvedValue(mockBudgetOptimizations);
    mockAIAnalyticsService.analyzeVendorPerformance.mockResolvedValue(mockVendorInsights[0]);
  });

  it('should render AI analytics dashboard header', async () => {
    render(<AIAnalyticsDashboard {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText('AI-Powered Analytics')).toBeInTheDocument();
      expect(screen.getByText('Intelligent insights and predictions for expenditure management')).toBeInTheDocument();
    });
  });

  it('should display loading state initially', () => {
    render(<AIAnalyticsDashboard {...defaultProps} />);

    // Should show loading skeletons initially
    expect(screen.getByText('AI-Powered Analytics')).toBeInTheDocument();
  });

  it('should load and display analytics data', async () => {
    render(<AIAnalyticsDashboard {...defaultProps} />);

    await waitFor(() => {
      expect(mockAIAnalyticsService.analyzeSpendingPatterns).toHaveBeenCalled();
      expect(mockAIAnalyticsService.generateSpendingForecast).toHaveBeenCalled();
      expect(mockAIAnalyticsService.detectAnomalies).toHaveBeenCalled();
      expect(mockAIAnalyticsService.optimizeBudgetAllocation).toHaveBeenCalled();
      expect(mockAIAnalyticsService.analyzeVendorPerformance).toHaveBeenCalled();
    });
  });

  it('should display key metrics cards', async () => {
    render(<AIAnalyticsDashboard {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText('Patterns Detected')).toBeInTheDocument();
      expect(screen.getByText('Anomalies Found')).toBeInTheDocument();
      expect(screen.getByText('Optimization Opportunities')).toBeInTheDocument();
      expect(screen.getByText('Forecast Accuracy')).toBeInTheDocument();
    });
  });

  it('should display analytics tabs', async () => {
    render(<AIAnalyticsDashboard {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText('Patterns')).toBeInTheDocument();
      expect(screen.getByText('Forecasts')).toBeInTheDocument();
      expect(screen.getByText('Anomalies')).toBeInTheDocument();
      expect(screen.getByText('Optimization')).toBeInTheDocument();
      expect(screen.getByText('Vendors')).toBeInTheDocument();
    });
  });

  it('should switch between tabs', async () => {
    const user = userEvent.setup();
    render(<AIAnalyticsDashboard {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText('Patterns')).toBeInTheDocument();
    });

    // Click on Forecasts tab
    const forecastsTab = screen.getByText('Forecasts');
    await user.click(forecastsTab);

    // Should display forecasts content
    await waitFor(() => {
      expect(screen.getByText('Spending Forecasts')).toBeInTheDocument();
    });
  });

  it('should display spending patterns', async () => {
    render(<AIAnalyticsDashboard {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText('Spending Patterns Analysis')).toBeInTheDocument();
      expect(screen.getByText('AI-identified patterns in organizational spending behavior')).toBeInTheDocument();
    });
  });

  it('should display forecasts with charts', async () => {
    const user = userEvent.setup();
    render(<AIAnalyticsDashboard {...defaultProps} />);

    await waitFor(() => {
      const forecastsTab = screen.getByText('Forecasts');
      user.click(forecastsTab);
    });

    await waitFor(() => {
      expect(screen.getByText('Spending Forecasts')).toBeInTheDocument();
      expect(screen.getByTestId('responsive-container')).toBeInTheDocument();
      expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    });
  });

  it('should display anomalies with severity indicators', async () => {
    const user = userEvent.setup();
    render(<AIAnalyticsDashboard {...defaultProps} />);

    await waitFor(() => {
      const anomaliesTab = screen.getByText('Anomalies');
      user.click(anomaliesTab);
    });

    await waitFor(() => {
      expect(screen.getByText('Spending Anomalies')).toBeInTheDocument();
      expect(screen.getByText('AI-detected unusual spending patterns requiring attention')).toBeInTheDocument();
    });
  });

  it('should display budget optimization recommendations', async () => {
    const user = userEvent.setup();
    render(<AIAnalyticsDashboard {...defaultProps} />);

    await waitFor(() => {
      const optimizationTab = screen.getByText('Optimization');
      user.click(optimizationTab);
    });

    await waitFor(() => {
      expect(screen.getByText('Budget Optimization Recommendations')).toBeInTheDocument();
      expect(screen.getByText('AI-suggested budget adjustments for improved efficiency')).toBeInTheDocument();
    });
  });

  it('should display vendor performance insights', async () => {
    const user = userEvent.setup();
    render(<AIAnalyticsDashboard {...defaultProps} />);

    await waitFor(() => {
      const vendorsTab = screen.getByText('Vendors');
      user.click(vendorsTab);
    });

    await waitFor(() => {
      expect(screen.getByText('Vendor Performance Insights')).toBeInTheDocument();
      expect(screen.getByText('AI analysis of vendor performance and recommendations')).toBeInTheDocument();
    });
  });

  it('should handle refresh button click', async () => {
    const user = userEvent.setup();
    render(<AIAnalyticsDashboard {...defaultProps} />);

    await waitFor(() => {
      const refreshButton = screen.getByText('Refresh');
      user.click(refreshButton);
    });

    // Should call analytics services again
    await waitFor(() => {
      expect(mockAIAnalyticsService.analyzeSpendingPatterns).toHaveBeenCalledTimes(2);
    });
  });

  it('should handle export button click', async () => {
    const user = userEvent.setup();
    render(<AIAnalyticsDashboard {...defaultProps} />);

    await waitFor(() => {
      const exportButton = screen.getByText('Export');
      expect(exportButton).toBeInTheDocument();
    });
  });

  it('should handle insight actions', async () => {
    const user = userEvent.setup();
    const mockOnInsightAction = jest.fn();
    
    render(<AIAnalyticsDashboard {...defaultProps} onInsightAction={mockOnInsightAction} />);

    // Wait for anomalies to load and switch to anomalies tab
    await waitFor(() => {
      const anomaliesTab = screen.getByText('Anomalies');
      user.click(anomaliesTab);
    });

    // Look for action buttons (View, Resolve)
    await waitFor(() => {
      const viewButtons = screen.queryAllByText('View');
      if (viewButtons.length > 0) {
        user.click(viewButtons[0]);
        expect(mockOnInsightAction).toHaveBeenCalledWith('view', expect.any(Object));
      }
    });
  });

  it('should display no anomalies message when none found', async () => {
    // Mock empty anomalies
    mockAIAnalyticsService.detectAnomalies.mockResolvedValue([]);
    
    const user = userEvent.setup();
    render(<AIAnalyticsDashboard {...defaultProps} />);

    await waitFor(() => {
      const anomaliesTab = screen.getByText('Anomalies');
      user.click(anomaliesTab);
    });

    await waitFor(() => {
      expect(screen.getByText('No anomalies detected. Your spending patterns look normal!')).toBeInTheDocument();
    });
  });

  it('should format currency correctly', async () => {
    render(<AIAnalyticsDashboard {...defaultProps} />);

    // The component should format currency values
    // This would be visible in the patterns or optimization sections
    await waitFor(() => {
      expect(screen.getByText('AI-Powered Analytics')).toBeInTheDocument();
    });
  });

  it('should display confidence indicators', async () => {
    render(<AIAnalyticsDashboard {...defaultProps} />);

    await waitFor(() => {
      // Should display confidence percentages for patterns
      expect(screen.getByText('AI-Powered Analytics')).toBeInTheDocument();
    });
  });

  it('should handle service errors gracefully', async () => {
    // Mock service error
    mockAIAnalyticsService.analyzeSpendingPatterns.mockRejectedValue(new Error('Service error'));
    
    render(<AIAnalyticsDashboard {...defaultProps} />);

    // Should still render the component without crashing
    await waitFor(() => {
      expect(screen.getByText('AI-Powered Analytics')).toBeInTheDocument();
    });
  });

  it('should display last updated timestamp', async () => {
    render(<AIAnalyticsDashboard {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText(/Last updated:/)).toBeInTheDocument();
    });
  });

  it('should display trend icons correctly', async () => {
    render(<AIAnalyticsDashboard {...defaultProps} />);

    // Should display trend indicators in patterns
    await waitFor(() => {
      expect(screen.getByText('Spending Patterns Analysis')).toBeInTheDocument();
    });
  });

  it('should display risk level indicators', async () => {
    const user = userEvent.setup();
    render(<AIAnalyticsDashboard {...defaultProps} />);

    await waitFor(() => {
      const vendorsTab = screen.getByText('Vendors');
      user.click(vendorsTab);
    });

    // Should display risk level badges
    await waitFor(() => {
      expect(screen.getByText('Vendor Performance Insights')).toBeInTheDocument();
    });
  });
});
