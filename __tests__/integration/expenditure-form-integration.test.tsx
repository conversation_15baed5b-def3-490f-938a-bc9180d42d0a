import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ExpenditureForm } from '@/components/accounting/expenditures/expenditure-form';
import { useExpenditureManagement } from '@/lib/hooks/accounting/use-expenditure-management';

// Mock the expenditure management hook
jest.mock('@/lib/hooks/accounting/use-expenditure-management', () => ({
  useExpenditureManagement: jest.fn(),
}));

// Mock the receipt upload component
jest.mock('@/components/accounting/expenditures/receipt-upload', () => ({
  ReceiptUpload: ({ onExtractedDataUpdate, onReceiptProcessed }: any) => (
    <div data-testid="receipt-upload">
      <button
        data-testid="mock-upload-receipt"
        onClick={() => {
          // Simulate receipt processing
          const mockExtractedData = {
            merchantName: 'Test Merchant',
            totalAmount: 25000,
            transactionDate: new Date('2024-06-15'),
            paymentMethod: 'Cash',
          };
          onExtractedDataUpdate(mockExtractedData);
          
          const mockReceiptData = {
            id: 'receipt-123',
            extractedData: mockExtractedData,
            ocrConfidence: 85.5,
          };
          onReceiptProcessed(mockReceiptData);
        }}
      >
        Upload Receipt
      </button>
    </div>
  ),
}));

// Mock react-hook-form
jest.mock('react-hook-form', () => ({
  useForm: () => ({
    control: {},
    handleSubmit: (fn: any) => (e: any) => {
      e.preventDefault();
      fn({
        title: 'Test Expenditure',
        description: 'Test Description',
        category: 'operational',
        subcategory: 'office_supplies',
        amount: 25000,
        currency: 'MWK',
        expenditureDate: new Date(),
        department: 'Administration',
        vendor: {
          vendorName: 'Test Merchant',
        },
        budgetAllocations: [
          {
            budgetId: 'budget-1',
            allocatedAmount: 25000,
            percentage: 100,
          },
        ],
      });
    },
    watch: jest.fn(() => ({})),
    setValue: jest.fn(),
    getValues: jest.fn(() => ({})),
    setError: jest.fn(),
    formState: { errors: {} },
  }),
  useFieldArray: () => ({
    fields: [
      {
        budgetId: 'budget-1',
        allocatedAmount: 25000,
        percentage: 100,
      },
    ],
    append: jest.fn(),
    remove: jest.fn(),
  }),
}));

// Mock Form components
jest.mock('@/components/ui/form', () => ({
  Form: ({ children }: any) => <form data-testid="expenditure-form">{children}</form>,
  FormControl: ({ children }: any) => <div>{children}</div>,
  FormDescription: ({ children }: any) => <div>{children}</div>,
  FormField: ({ render }: any) => render({ field: { onChange: jest.fn(), value: '' } }),
  FormItem: ({ children }: any) => <div>{children}</div>,
  FormLabel: ({ children }: any) => <label>{children}</label>,
  FormMessage: () => <div />,
}));

const mockUseExpenditureManagement = useExpenditureManagement as jest.MockedFunction<typeof useExpenditureManagement>;

describe('ExpenditureForm Integration', () => {
  const defaultProps = {
    onSubmit: jest.fn(),
    onCancel: jest.fn(),
    isEditing: false,
  };

  const mockHookReturn = {
    createExpenditure: jest.fn(),
    updateExpenditure: jest.fn(),
    isCreating: false,
    isUpdating: false,
    validateExpenditure: jest.fn(() => []),
    calculateTotalAmount: jest.fn((amount) => amount * 1.165), // With 16.5% tax
    formatCurrency: jest.fn((amount) => `MWK ${amount.toLocaleString()}`),
    createError: null,
    updateError: null,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseExpenditureManagement.mockReturnValue(mockHookReturn);
  });

  it('should render expenditure form with receipt upload section', () => {
    render(<ExpenditureForm {...defaultProps} />);

    expect(screen.getByText('Create New Expenditure')).toBeInTheDocument();
    expect(screen.getByText('Receipt & Documentation')).toBeInTheDocument();
    expect(screen.getByTestId('receipt-upload')).toBeInTheDocument();
  });

  it('should auto-fill form fields when receipt data is extracted', async () => {
    const user = userEvent.setup();
    render(<ExpenditureForm {...defaultProps} />);

    // Click the mock upload receipt button
    const uploadButton = screen.getByTestId('mock-upload-receipt');
    await user.click(uploadButton);

    // The form should be auto-filled with extracted data
    // This would be verified through the setValue calls in the mock
    expect(screen.getByTestId('receipt-upload')).toBeInTheDocument();
  });

  it('should handle form submission with receipt data', async () => {
    const user = userEvent.setup();
    const mockOnSubmit = jest.fn();
    
    render(<ExpenditureForm {...defaultProps} onSubmit={mockOnSubmit} />);

    // Upload a receipt first
    const uploadButton = screen.getByTestId('mock-upload-receipt');
    await user.click(uploadButton);

    // Submit the form
    const submitButton = screen.getByText(/Create Expenditure/);
    await user.click(submitButton);

    // Should call createExpenditure
    expect(mockHookReturn.createExpenditure).toHaveBeenCalled();
  });

  it('should validate expenditure data before submission', async () => {
    const user = userEvent.setup();
    const mockValidateExpenditure = jest.fn(() => ['Amount is required']);
    mockUseExpenditureManagement.mockReturnValue({
      ...mockHookReturn,
      validateExpenditure: mockValidateExpenditure,
    });

    render(<ExpenditureForm {...defaultProps} />);

    // Submit the form
    const submitButton = screen.getByText(/Create Expenditure/);
    await user.click(submitButton);

    // Should call validation
    expect(mockValidateExpenditure).toHaveBeenCalled();
  });

  it('should calculate total amount including tax', () => {
    render(<ExpenditureForm {...defaultProps} />);

    // Should call calculateTotalAmount
    expect(mockHookReturn.calculateTotalAmount).toHaveBeenCalled();
  });

  it('should handle receipt processing errors gracefully', async () => {
    const user = userEvent.setup();
    
    // Mock receipt upload component that throws error
    jest.doMock('@/components/accounting/expenditures/receipt-upload', () => ({
      ReceiptUpload: ({ onExtractedDataUpdate }: any) => (
        <div data-testid="receipt-upload">
          <button
            data-testid="mock-upload-error"
            onClick={() => {
              // Simulate processing error
              throw new Error('Receipt processing failed');
            }}
          >
            Upload Error
          </button>
        </div>
      ),
    }));

    render(<ExpenditureForm {...defaultProps} />);

    // The form should still be functional even if receipt upload fails
    expect(screen.getByTestId('receipt-upload')).toBeInTheDocument();
  });

  it('should disable receipt upload during form submission', () => {
    mockUseExpenditureManagement.mockReturnValue({
      ...mockHookReturn,
      isCreating: true,
    });

    render(<ExpenditureForm {...defaultProps} />);

    // Receipt upload should be disabled
    expect(screen.getByTestId('receipt-upload')).toBeInTheDocument();
  });

  it('should handle editing mode with existing receipt data', () => {
    const initialData = {
      id: 'exp-123',
      title: 'Existing Expenditure',
      amount: 15000,
      vendor: {
        vendorName: 'Existing Vendor',
      },
    };

    render(
      <ExpenditureForm 
        {...defaultProps} 
        isEditing={true}
        initialData={initialData}
      />
    );

    expect(screen.getByText('Edit Expenditure')).toBeInTheDocument();
    expect(screen.getByTestId('receipt-upload')).toBeInTheDocument();
  });

  it('should pass expenditure ID to receipt upload component', () => {
    const initialData = { id: 'exp-123' };

    render(
      <ExpenditureForm 
        {...defaultProps} 
        initialData={initialData}
      />
    );

    // Receipt upload should receive the expenditure ID
    expect(screen.getByTestId('receipt-upload')).toBeInTheDocument();
  });

  it('should handle multiple receipt uploads', async () => {
    const user = userEvent.setup();
    render(<ExpenditureForm {...defaultProps} />);

    // Upload multiple receipts
    const uploadButton = screen.getByTestId('mock-upload-receipt');
    await user.click(uploadButton);
    await user.click(uploadButton);

    // Should handle multiple receipts
    expect(screen.getByTestId('receipt-upload')).toBeInTheDocument();
  });

  it('should update form when receipt data changes', async () => {
    const user = userEvent.setup();
    render(<ExpenditureForm {...defaultProps} />);

    // Upload receipt with different data
    const uploadButton = screen.getByTestId('mock-upload-receipt');
    await user.click(uploadButton);

    // Form should update with new data
    expect(screen.getByTestId('receipt-upload')).toBeInTheDocument();
  });

  it('should maintain form state when receipt upload fails', async () => {
    const user = userEvent.setup();
    render(<ExpenditureForm {...defaultProps} />);

    // Fill form manually first
    // Then try to upload receipt that fails
    // Form should maintain existing data

    expect(screen.getByTestId('expenditure-form')).toBeInTheDocument();
  });

  it('should show loading state during receipt processing', () => {
    render(<ExpenditureForm {...defaultProps} />);

    // Should show appropriate loading states
    expect(screen.getByTestId('receipt-upload')).toBeInTheDocument();
  });

  it('should handle receipt confidence scores', async () => {
    const user = userEvent.setup();
    render(<ExpenditureForm {...defaultProps} />);

    // Upload receipt with confidence score
    const uploadButton = screen.getByTestId('mock-upload-receipt');
    await user.click(uploadButton);

    // Should display confidence information
    expect(screen.getByTestId('receipt-upload')).toBeInTheDocument();
  });

  it('should allow manual override of extracted data', async () => {
    const user = userEvent.setup();
    render(<ExpenditureForm {...defaultProps} />);

    // Upload receipt
    const uploadButton = screen.getByTestId('mock-upload-receipt');
    await user.click(uploadButton);

    // User should be able to manually edit the auto-filled fields
    expect(screen.getByTestId('expenditure-form')).toBeInTheDocument();
  });

  it('should validate extracted data against form constraints', async () => {
    const user = userEvent.setup();
    render(<ExpenditureForm {...defaultProps} />);

    // Upload receipt
    const uploadButton = screen.getByTestId('mock-upload-receipt');
    await user.click(uploadButton);

    // Should validate extracted data
    expect(mockHookReturn.validateExpenditure).toHaveBeenCalled();
  });

  it('should handle receipt upload cancellation', () => {
    render(<ExpenditureForm {...defaultProps} />);

    // Should handle when user cancels receipt upload
    expect(screen.getByTestId('receipt-upload')).toBeInTheDocument();
  });
});
